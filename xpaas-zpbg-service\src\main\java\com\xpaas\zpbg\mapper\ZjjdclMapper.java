package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zjjdcl;
import com.xpaas.zpbg.vo.ZjjdclVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-知识库管理-标准体系管理-专家解读材料 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Repository
public interface ZjjdclMapper extends BaseMapper<Zjjdcl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zjjdcl
	 * @return
	 */
	List<ZjjdclVO> selectZjjdclPage(IPage page, ZjjdclVO zjjdcl);
	List<Zjjdcl> list1(@Param("zjjdcl") Zjjdcl zjjdcl);
	/**
	 * 自定义删除冗余数据
	 *
	 * @param parentId
	 * @return
	 */
	void deleteZjjdclData(String parentId);

	/**
	 * 查询数量总和分组
	 *
	 * @param parentId
	 * @return
	 */
	List<Map<String, Object>> selectZjjdclCount(@Param("parentId") String parentId);

}
