package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.service.IBgfzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 教学评价-自评报告-报告复制 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bgfz")
@Api(value = "教学评价-自评报告-报告复制", tags = "教学评价-自评报告-报告复制接口")
public class BgfzController extends BaseController {

    private IBgfzService bgfzService;


    @PostMapping("/bgfz")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "新增", notes = "传入报告复制信息")
    @ApiLog("报告复制")
    public R bgfz(@RequestBody Bggl bggl) {
        bgfzService.bgfz(bggl);
        return R.success("1");
    }
}
