package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Jdmkgl;
import com.xpaas.zpbg.vo.JdmkglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-进度模块关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class JdmkglWrapper extends BaseEntityWrapper<Jdmkgl, JdmkglVO>  {


	@Override
	public JdmkglVO entityVO(Jdmkgl jdmkgl) {
		JdmkglVO jdmkglVO = Objects.requireNonNull(BeanUtil.copy(jdmkgl, JdmkglVO.class));
		//User cjr = UserCache.getUser(jdmkgl.getCjr());
		//if (cjr != null){
		//	jdmkglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdmkgl.getGxr());
		//if (gxr != null){
		//	jdmkglVO.setGxrName(gxr.getName());
		//}
		return jdmkglVO;
	}

    @Override
    public JdmkglVO wrapperVO(JdmkglVO jdmkglVO) {
		//User cjr = UserCache.getUser(jdmkglVO.getCjr());
		//if (cjr != null){
		//	jdmkglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdmkglVO.getGxr());
		//if (gxr != null){
		//	jdmkglVO.setGxrName(gxr.getName());
		//}
        return jdmkglVO;
    }

}
