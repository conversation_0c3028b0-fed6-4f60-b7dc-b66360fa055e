package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-审阅专家实体类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_SYZJ")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Syzj对象", description = "教学评价-自评报告-审阅专家")
public class Syzj extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 专家ID
	*/
	@ExcelProperty("专家ID")
	@ApiModelProperty(value = "专家ID")
	@TableField("ZJID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long zjid;

	/**
	* 专家姓名
	*/
	@ExcelProperty("专家姓名")
	@ApiModelProperty(value = "专家姓名")
	@TableField("ZJXM")
	private String zjxm;

	/**
	* 专家单位ID
	*/
	@ExcelProperty("专家单位ID")
	@ApiModelProperty(value = "专家单位ID")
	@TableField("ZJDWID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long zjdwid;

	/**
	* 专家单位名称
	*/
	@ExcelProperty("专家单位名称")
	@ApiModelProperty(value = "专家单位名称")
	@TableField("ZJDWMC")
	private String zjdwmc;

	/**
	* 审阅状态
	*/
	@ExcelProperty("审阅状态")
	@ApiModelProperty(value = "审阅状态")
	@TableField("SYZT")
	private Integer syzt;



}
