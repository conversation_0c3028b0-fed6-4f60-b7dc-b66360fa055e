package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Gjcgl;
import com.xpaas.zpbg.mapper.GjcglMapper;
import com.xpaas.zpbg.service.IGjcglService;
import com.xpaas.zpbg.vo.GjcglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-关键词管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class GjcglServiceImpl extends BaseServiceImpl<GjcglMapper, Gjcgl> implements IGjcglService {

	@Override
	public IPage<GjcglVO> selectGjcglPage(IPage<GjcglVO> page, GjcglVO gjcgl) {
		return page.setRecords(baseMapper.selectGjcglPage(page, gjcgl));
	}

	@Override
	public List<GjcglVO> selectMkglList(GjcglVO gjcgl) {
		return baseMapper.selectMkglList(gjcgl);
	}

	@Override
	public List<GjcglVO> selectBgglList(GjcglVO gjcgl) {
		return baseMapper.selectBgglList(gjcgl);
	}

	@Override
	public List<GjcglVO> selectJdglList(GjcglVO gjcgl) {
		return baseMapper.selectJdglList(gjcgl);
	}

	@Override
	public List<GjcglVO> selectZxjd(GjcglVO gjcgl) {
		return baseMapper.selectZxjd(gjcgl);
	}

}
