package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Pcgl;
import com.xpaas.zpbg.vo.PcglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-自评报告-批次管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Component
public class PcglWrapper extends BaseEntityWrapper<Pcgl, PcglVO>  {


	@Override
	public PcglVO entityVO(Pcgl bggl) {
		PcglVO bgglVO = Objects.requireNonNull(BeanUtil.copy(bggl, PcglVO.class));
		return bgglVO;
	}

    @Override
    public PcglVO wrapperVO(PcglVO bgglVO) {
        return bgglVO;
    }

}
