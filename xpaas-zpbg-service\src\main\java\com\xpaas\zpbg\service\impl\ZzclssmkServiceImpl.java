package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zzclssmk;
import com.xpaas.zpbg.mapper.ZzclssmkMapper;
import com.xpaas.zpbg.service.IZzclssmkService;
import com.xpaas.zpbg.vo.ZzclssmkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-佐证材料所属模块 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Slf4j
@Service
public class ZzclssmkServiceImpl extends BaseServiceImpl<ZzclssmkMapper, Zzclssmk> implements IZzclssmkService {

	@Override
	public IPage<ZzclssmkVO> selectZzclssmkPage(IPage<ZzclssmkVO> page, ZzclssmkVO zzclssmk) {
		return page.setRecords(baseMapper.selectZzclssmkPage(page, zzclssmk));
	}

}
