package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.IInitializeDataServiceImpl;
import com.xpaas.zpbg.service.IZcclService;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZzclVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 初始化数据
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/initializeData")
@Api(value = "初始化数据", tags = "初始化数据")
public class InitializeDataController extends BaseController {
    private IZzclService zzclService;
    private IZcclService zcclService;
    private IInitializeDataServiceImpl initializeDataService;
    /**
     * 详情
     */
    @GetMapping("/setData")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入zzcl")
    @ApiLog("初始化数据")
    public R InitializeData(Query query) {
        ZzclVO  zzclVO = new ZzclVO();
        ZcclVO  zcclVO = new ZcclVO();
        // 获取备查材料
        IPage<ZzclVO> zzclPage= zzclService.selectZzclPage(Condition.getPage(query), zzclVO);
        List<ZzclVO> zzList = zzclPage.getRecords();

        // 获取佐证材料
        IPage<ZcclVO> zcclPage = zcclService.selectZcclPage(Condition.getPage(query), zcclVO);
        List<ZcclVO> zcList = zcclPage.getRecords();

        return R.status( initializeDataService.setData(zzList,zcList));
    }

}
