package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bqgl;
import com.xpaas.zpbg.vo.BqglVO;

/**
 * 教学评价-自评报告-标签管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IBqglService extends BaseService<Bqgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bqgl
	 * @return
	 */
	IPage<BqglVO> selectBqglPage(IPage<BqglVO> page, BqglVO bqgl);

	/**
	 * 自定义分页
	 *
	 * @param bqgl
	 * @return
	 */
	IPage<BqglVO> selectZjBqgl( IPage<BqglVO> page, BqglVO bqgl);

}
