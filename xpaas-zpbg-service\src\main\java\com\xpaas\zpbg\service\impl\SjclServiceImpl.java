package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.entity.Sjcl;
import com.xpaas.zpbg.entity.Sjclssmk;
import com.xpaas.zpbg.mapper.SjclMapper;
import com.xpaas.zpbg.service.IBgmkService;
import com.xpaas.zpbg.service.ISjclService;
import com.xpaas.zpbg.service.ISjclssmkService;
import com.xpaas.zpbg.vo.SjclVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-数据材料 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class SjclServiceImpl extends BaseServiceImpl<SjclMapper, Sjcl> implements ISjclService {

	@Autowired
	private ISjclssmkService sjclssmkService;

	@Autowired
	private IBgmkService bgmkService;

	@Override
	public IPage<SjclVO> selectSjclPage(IPage<SjclVO> page, SjclVO sjcl) {
		return page.setRecords(baseMapper.selectSjclPage(page, sjcl));
	}

	@Override
	public boolean saveFromSjzx(SjclVO sjclVO) {
		Bgmk bgmk = bgmkService.getById(sjclVO.getBgmkid());
		if(bgmk == null) {
			throw new ServiceException("未查询到报告模块！");
		}

		// 保存主表
		sjclVO.setMkid(null);
		super.save(sjclVO);

		// 保存子表
		Sjclssmk sjclssmk = new Sjclssmk();
		sjclssmk.setSjclid(sjclVO.getId());
		sjclssmk.setMkid(bgmk.getMkid());
		sjclssmkService.save(sjclssmk);

		return true;
	}

}
