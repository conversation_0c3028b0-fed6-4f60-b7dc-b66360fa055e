<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZtglMapper">

    <!-- 加锁报告记录 -->
    <update id="lockBg" parameterType="long">
        update t_dt_jxpj_zpbg_bggl
        set id = id
        where id = #{bgId}
          and scbj = 0
    </update>

    <!-- 获取活跃报告ID -->
    <select id="selectBgidList" resultType="long">
        select id
        from t_dt_jxpj_zpbg_bggl
        where scbj = 0
    </select>

    <!-- 获取报告当前进度管理ID -->
    <select id="selectCurJdglid" parameterType="long" resultType="long">
        SELECT MAX(j.id) jdglid
        FROM t_dt_jxpj_zpbg_jdgl j
                 INNER JOIN (
            SELECT j2.bgid,
                   MAX(j2.kssj) AS max_kssj
            FROM t_dt_jxpj_zpbg_jdgl j2
            WHERE j2.scbj = 0
              AND j2.kssj &lt;= curdate()
              AND j2.bgid = #{bgId}
            GROUP BY bgid
        ) t ON j.bgid = t.bgid
            AND j.kssj = t.max_kssj;
    </select>

    <!-- 更新报告进度管理ID -->
    <update id="updateBgJdglid">
        UPDATE t_dt_jxpj_zpbg_bggl
        SET jdglid = #{jdglid}
        WHERE id = #{bgId}
    </update>
    <!-- 更新报告各模块主线版本进度管理ID -->
    <update id="updateBbJdglid">
        UPDATE t_dt_jxpj_zpbg_bbgl bb
            INNER JOIN t_dt_jxpj_zpbg_bggl bg
        ON bg.id = bb.bgid
            INNER JOIN t_dt_jxpj_zpbg_bgmk mk ON mk.id = bb.bgmkid
            AND mk.bgid = bg.id
            SET bb.jdglid = #{jdglid}
        WHERE
            bb.bgid = #{bgId}
          AND bb.bblx = 1
          AND bg.scbj = 0
          AND mk.scbj = 0
          AND bb.scbj = 0
    </update>

    <!-- 获取进度记录 -->
    <select id="selectZtjl" resultType="map">
        SELECT *
        FROM (
        SELECT t.cjrq sj,
        t.bgmkid bgmkid,
        b.id bbid,
        t.czlx czlx,
        null zxjssj
        FROM t_dt_jxpj_zpbg_bgmkztjl t
        INNER JOIN t_dt_jxpj_zpbg_bbgl b ON b.bgid = t.bgid
        AND b.bgmkid = t.bgmkid
        AND b.bblx = 1
        AND b.scbj = 0
        WHERE t.bgid = #{bgid}
        AND t.scbj = 0
        <if test="bbid != null and bbid > 0">
            AND b.id = #{bbid}
        </if>
        UNION ALL
        SELECT t.kssj sj,
        b.bgmkid bgmkid,
        b.id bbid,
        - 1 czlx,
        t.zxjssj zxjssj
        FROM t_dt_jxpj_zpbg_jdgl t
        INNER JOIN t_dt_jxpj_zpbg_bbgl b ON b.bgid = t.bgid
        AND b.bblx = 1
        AND b.scbj = 0
        WHERE t.bgid = #{bgid}
        AND t.scbj = 0
        <if test="bbid != null and bbid > 0">
            AND b.id = #{bbid}
        </if>
        ) u
        where u.sj &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and u.bgmkid is not null
        ORDER BY u.bgmkid,
        u.sj
    </select>

    <!-- 各类消息发送信息 - 撰写开始 -->
    <select id="selectMessageZxks" resultType="map">
        SELECT bbgl.id   AS bbid,
               rwfg.ryid AS userId
        FROM t_dt_jxpj_zpbg_bggl bggl
                 INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON jdgl.bgid = bggl.id
            AND jdgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.jdglid = jdgl.id
            AND jdmkgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = jdmkgl.bgmkid
            AND bgmk.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_rwfg rwfg ON rwfg.bgmkid = bgmk.id
            AND rwfg.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
            AND bbgl.bblx = 1
            AND bbgl.scbj = 0
        WHERE bggl.scbj = 0
          AND jdgl.kssj = curdate()
        GROUP BY bbgl.id,
                 rwfg.ryid
        ORDER BY bbgl.id
    </select>
    <!-- 各类消息发送信息 - 撰写快结束 -->
    <select id="selectMessageZxjs" resultType="map">
        SELECT bbgl.id   AS bbid,
               rwfg.ryid AS userId,
               bgmk.mkmc,
               bgmk.cmm,
               jdgl.zxjssj
        FROM t_dt_jxpj_zpbg_bggl bggl
                 INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON jdgl.bgid = bggl.id
            AND jdgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.jdglid = jdgl.id
            AND jdmkgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = jdmkgl.bgmkid
            AND bgmk.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_rwfg rwfg ON rwfg.bgmkid = bgmk.id
            AND rwfg.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
            AND bbgl.bblx = 1
            AND bbgl.scbj = 0
        WHERE bggl.scbj = 0
          AND jdgl.zxjssj = curdate() + INTERVAL 1 DAY
          AND jdgl.zxjssj = curdate() + INTERVAL 2 DAY
          AND bbgl.bjdzsbc
         &lt; 2
          AND bbgl.bgmkzt IN ( 0
            , 10
            , 20 )
        GROUP BY
            bbgl.id,
            bgmk.id,
            jdgl.id,
            rwfg.ryid
        ORDER BY
            bbgl.id
    </select>
    <!-- 各类消息发送信息 - 撰写催提交 -->
    <select id="selectMessageZxtj" resultType="map">
        SELECT bbgl.id   AS bbid,
               rwfg.ryid AS userId,
               bgmk.mkmc,
               bgmk.cmm
        FROM t_dt_jxpj_zpbg_bggl bggl
                 INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON jdgl.bgid = bggl.id
            AND jdgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.jdglid = jdgl.id
            AND jdmkgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = jdmkgl.bgmkid
            AND bgmk.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_rwfg rwfg ON rwfg.bgmkid = bgmk.id
            AND rwfg.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
            AND bbgl.bblx = 1
            AND bbgl.scbj = 0
        WHERE bggl.scbj = 0
          AND jdgl.zxjssj = curdate()
          AND bbgl.bjdzsbc &lt; 2
          AND bbgl.bgmkzt IN (0, 10, 20)
        GROUP BY bbgl.id,
                 bgmk.id,
                 rwfg.ryid
        ORDER BY bbgl.id
    </select>
    <!-- 各类消息发送信息 - 撰写延期 -->
    <select id="selectMessageZxtjGly" resultType="map">
        SELECT bbgl.id AS bbid,
               bgmk.mkmc,
               bgmk.cmm
        FROM t_dt_jxpj_zpbg_bggl bggl
                 INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON jdgl.bgid = bggl.id
            AND jdgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.jdglid = jdgl.id
            AND jdmkgl.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = jdmkgl.bgmkid
            AND bgmk.scbj = 0
                 INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
            AND bbgl.bblx = 1
            AND bbgl.scbj = 0
        WHERE bggl.scbj = 0
          AND jdgl.zxjssj = curdate()
          AND bbgl.bjdzsbc &lt; 2
          AND bbgl.bgmkzt IN (0, 10, 20)
        GROUP BY bbgl.id,
                 bgmk.id
        ORDER BY bbgl.id
    </select>
    <select id="selectZjyjCnt" resultType="java.lang.Integer">
        select
            COUNT(1) cnt
        from
            t_dt_jxpj_zpbg_zjyj y
                left join t_dt_jxpj_zpbg_bbpz p on
                    p.COMMENT_ID = y.YJKEY
                    and p.BBID = y.BBID
                    and P.SCBJ = 0
        where
            y.SCBJ = 0
          and (y.YJQF in (1,3) or p.ID is not null)
          and Y.BBID = #{bbid}
    </select>
</mapper>
