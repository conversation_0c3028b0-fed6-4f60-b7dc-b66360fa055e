package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Gjcjcjl;
import com.xpaas.zpbg.entity.Gjcjcqk;
import com.xpaas.zpbg.mapper.GjcjcjlMapper;
import com.xpaas.zpbg.service.IGjcjcjlService;
import com.xpaas.zpbg.service.IGjcjcqkService;
import com.xpaas.zpbg.vo.GjcglVO;
import com.xpaas.zpbg.vo.GjcjcjlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-关键词检测记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class GjcjcjlServiceImpl extends BaseServiceImpl<GjcjcjlMapper, Gjcjcjl> implements IGjcjcjlService {

	@Autowired
	private IGjcjcqkService gjcjcqkService;

	@Override
	public IPage<GjcjcjlVO> selectGjcjcjlPage(IPage<GjcjcjlVO> page, GjcjcjlVO gjcjcjl) {
		return page.setRecords(baseMapper.selectGjcjcjlPage(page, gjcjcjl));
	}

	@Override
	public boolean detect(GjcjcjlVO gjcjcjlVO) {
		int gjcgs = 0;

		// 插入关键词检测记录
		gjcjcjlVO.setGjcgs(0);
		super.save(gjcjcjlVO);

		// 关键词检测情况
		List<Gjcjcqk> gjcjcqkList = new ArrayList<Gjcjcqk>();

		// 取得关键词列表
		List<GjcglVO> gjcList = baseMapper.getGjcList(gjcjcjlVO);

		// 取得文章内容
		List<GjcjcjlVO> wznrList = baseMapper.getWznr(gjcjcjlVO);

		if(wznrList != null && wznrList.size() > 0 && gjcList != null && gjcList.size() > 0) {
			for(GjcjcjlVO nr : wznrList) {
				Long bbid = nr.getBbid();
				String wjnr = nr.getWjnr();
				Long bgmkid = nr.getBgmkid();
				Long mkid = nr.getMkid();

				for(GjcglVO gjc : gjcList) {
					// 所有模块 或者 符合模块
					if(gjc.getGlfw() == 1 || (gjc.getMkids() != null && gjc.getMkids().indexOf(String.valueOf(mkid)) >= 0)) {
						String gjcmc = gjc.getGjcmc();
						int pc = (wjnr.length() - wjnr.replaceAll(gjcmc, "").length()) / gjcmc.length();
						gjcgs += pc;

						Gjcjcqk gjcjcqk = new Gjcjcqk();
						gjcjcqk.setJcjlid(gjcjcjlVO.getId());
						gjcjcqk.setBgmkid(bgmkid);
						gjcjcqk.setBbid(bbid);
						gjcjcqk.setGjcid(gjc.getId());
						gjcjcqk.setGjcmc(gjcmc);
						gjcjcqk.setPc(pc);

						gjcjcqkList.add(gjcjcqk);
					}
				}
			}
		}

		// 插入关键词检测情况
		gjcjcqkService.saveBatch(gjcjcqkList);

		// 更新频次
		if(gjcgs > 0) {
			gjcjcjlVO.setGjcgs(gjcgs);

			super.updateById(gjcjcjlVO);
		}

		return true;
	}

	@Override
	public List<GjcjcjlVO> getBgList() {
		return baseMapper.getBgList();
	}

}
