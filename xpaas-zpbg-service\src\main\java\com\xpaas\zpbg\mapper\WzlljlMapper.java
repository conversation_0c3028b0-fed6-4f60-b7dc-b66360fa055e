package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Wzlljl;
import com.xpaas.zpbg.vo.WzlljlVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-文章浏览记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Repository
public interface WzlljlMapper extends BaseMapper<Wzlljl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param wzlljl
	 * @return
	 */
	List<WzlljlVO> selectWzlljlPage(IPage page, WzlljlVO wzlljl);

}
