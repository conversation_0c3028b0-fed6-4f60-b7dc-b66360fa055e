package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Gjcgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-关键词管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GjcglVO对象", description = "教学评价-自评报告-关键词管理")
public class GjcglVO extends Gjcgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "多个模块ID")
    private String mkids;

    @ApiModelProperty(value = "应用模块")
    private String yymk;

    @ApiModelProperty(value = "频次统计")
    private Integer pcSum;

    @ApiModelProperty(value = "模块名称")
    private String mkmc;

    @ApiModelProperty(value = "模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mkid;

    @ApiModelProperty(value = "模块ID数组")
    private Long[] mkidList;

    @ApiModelProperty(value = "报告ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgid;

    @ApiModelProperty(value = "报告名称")
    private String bgmc;

    @ApiModelProperty(value = "进度ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jdid;

    @ApiModelProperty(value = "进度名称")
    private String jdmc;

}
