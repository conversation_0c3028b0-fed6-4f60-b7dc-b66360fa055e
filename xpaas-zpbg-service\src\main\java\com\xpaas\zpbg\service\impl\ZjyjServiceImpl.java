package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.zpbg.dto.ZjyjExcelDTO;
import com.xpaas.zpbg.entity.Zjyj;
import com.xpaas.zpbg.mapper.BbglMapper;
import com.xpaas.zpbg.mapper.ZjyjMapper;
import com.xpaas.zpbg.service.IZjyjService;
import com.xpaas.zpbg.vo.BbglVO;
import com.xpaas.zpbg.vo.ZjyjVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-专家意见 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class ZjyjServiceImpl extends BaseServiceImpl<ZjyjMapper, Zjyj> implements IZjyjService {

	 @Autowired
	 private BbglMapper bbglMapper;

	@Override
	public IPage<ZjyjVO> selectZjyjPage(IPage<ZjyjVO> page, ZjyjVO zjyj) {
		LoginUser user = AuthUtil.getUser();
		zjyj.setSyzjid(user.getUserId());
		return page.setRecords(baseMapper.selectZjyjPage(page, zjyj));
	}
	@Override
	public IPage<ZjyjVO> selectZxrPage(IPage<ZjyjVO> page, ZjyjVO zjyj) {
		return page.setRecords(baseMapper.selectZjyjPage(page, zjyj));
	}

	@Override
	public IPage<ZjyjVO> selectOtherzjPage(IPage<ZjyjVO> page, ZjyjVO zjyj) {
		return page.setRecords(baseMapper.selectOtherzjPage(page, zjyj));
	}

	@Override
	public List<ZjyjExcelDTO> getzjyjExcel(Map<String,Object> map){
		String bgid = map.get("bgid").toString();
		String bbid = map.get("bbid").toString();
		String bgmkid = map.get("bgmkid").toString();
		List<ZjyjExcelDTO> zjyjExcelDTO = baseMapper.getZjyjExcel(bgid,bbid,bgmkid);
		return zjyjExcelDTO;
	}

	@Override
	public List<ZjyjVO> getzjyjList(String bbid,List<String> yjkeyList){
		if(yjkeyList.isEmpty()){
			return new ArrayList<>();
		}
        return baseMapper.getzjyjList(bbid,yjkeyList);
	}

	@Override
	public int getZjyjCount(ZjyjVO zjyjvo){
		return 	baseMapper.getZjyjCount(zjyjvo);
	}

	@Override
	public int getyjChecking(String bgid, String bbid,String bgmkid){
		return 	baseMapper.getyjChecking(bgid,  bbid, bgmkid);
	}

	@Override
	public List<Zjyj> getLsyj(ZjyjVO zjyjvo){
		List<Zjyj> zjyjVO = new ArrayList<>();
		int index = 0;
    List<BbglVO> bbglVOS =bbglMapper.getLsyjList(zjyjvo.getBgmkid().toString());
    String id = "";
    if(bbglVOS.size()>0){
       for (int i = 0; i < bbglVOS.size(); i++) {
          if(bbglVOS.get(i).getSsjs()==1){
			  id = bbglVOS.get(i).getId().toString();
             index =i+1;
             break;
          }
          if(bbglVOS.get(i).getSsjs()==2){
			  id = bbglVOS.get(i).getId().toString();
		  }
       }
    }
  if("".equals(id)){
	  for (int i = index; i < bbglVOS.size(); i++) {
		  if(bbglVOS.get(i).getSsjs()==2){
			  id = bbglVOS.get(i).getId().toString();
			  break;
		  }
	  }
  }
      String bgid = zjyjvo.getBgid().toString();
      String bbid = id;
      String bgmkid = zjyjvo.getBgmkid().toString();
		zjyjVO = baseMapper.getLsyj( bgid, bbid,bgmkid,zjyjvo.getSyzjid().toString());
       if (zjyjVO.size()>0){
		   for (int i = 0; i < zjyjVO.size(); i++) {
			   if(zjyjVO.get(i).getYjlx()==10){
				   zjyjVO.get(i).setGlwz("---------");
			   }
		   }
	   }
		return zjyjVO;
	}
}
