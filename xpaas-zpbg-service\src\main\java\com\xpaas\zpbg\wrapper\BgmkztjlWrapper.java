package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.vo.BgmkztjlVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-报告模块状态记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Component
public class BgmkztjlWrapper extends BaseEntityWrapper<Bgmkztjl, BgmkztjlVO>  {


	@Override
	public BgmkztjlVO entityVO(Bgmkztjl bgmkztjl) {
		BgmkztjlVO bgmkztjlVO = Objects.requireNonNull(BeanUtil.copy(bgmkztjl, BgmkztjlVO.class));
		//User cjr = UserCache.getUser(bgmkztjl.getCjr());
		//if (cjr != null){
		//	bgmkztjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmkztjl.getGxr());
		//if (gxr != null){
		//	bgmkztjlVO.setGxrName(gxr.getName());
		//}
		return bgmkztjlVO;
	}

    @Override
    public BgmkztjlVO wrapperVO(BgmkztjlVO bgmkztjlVO) {
		//User cjr = UserCache.getUser(bgmkztjlVO.getCjr());
		//if (cjr != null){
		//	bgmkztjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmkztjlVO.getGxr());
		//if (gxr != null){
		//	bgmkztjlVO.setGxrName(gxr.getName());
		//}
        return bgmkztjlVO;
    }

}
