package com.xpaas.zpbg.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xpaas.zpbg.converter.GenericStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 教学评价-自评报告-专家意见实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@ApiModel(value = "ZjyjExecl格式", description = "教学评价-自评报告-导出专家意见")
public class ZjyjExcelDTO {

    private static final long serialVersionUID = 1L;


    /**
     * 一级指标
     */
    @ExcelProperty("一级指标")
    @ApiModelProperty(value = "一级指标")
    @TableField("YJZB")
    private String yjzb;

    /**
     * 二级指标
     */
    @ExcelProperty("二级指标")
    @ApiModelProperty(value = "二级指标")
    @TableField("EJZB")
    private String ejzb;
    /**
     * 主要关注点
     */
    @ExcelProperty("主要关注点")
    @ApiModelProperty(value = "主要关注点")
    @TableField("ZYFZD")
    private String zygzd;

    /**
     * 二级指标
     */
    @ExcelProperty("责任人/点长")
    @ApiModelProperty(value = "责任人/点长")
    @TableField("ZXRORDZ")
    private String zxrordz;

    /**
     * 审阅专家姓名
     */
    @ExcelProperty("评价专家")
    @ApiModelProperty(value = "评价专家")
    @TableField("SYZJXM")
    private String syzjxm;

    /**
     * 审阅专家姓名
     */
    @ExcelProperty("审阅时间")
    @ApiModelProperty(value = "审阅时间")
    @TableField("SYTIME")
    private String sytime;

    /**
     * 意见区分
     */
    @ExcelProperty(value = "分类", converter = GenericStatusConverter.class)
   // @GenericStatusMapping(keyValuePairs = {"0:--","1:审阅报告整体意见", "2:审阅报告内容意见", "3:审阅材料意见"})
    @ApiModelProperty(value = "分类")
    @TableField("YJQF")
    private String yjqf;

    /**
     * 意见类型
     */
    @ExcelProperty(value = "意见类型")
    @ApiModelProperty(value = "意见类型")
    @TableField("YJLX")
    private String yjlx;
    /**
     * 意见类型copy
     */
    @ExcelProperty(value = "意见类型")
//    @GenericStatusMapping(keyValuePairs = {"0:--", "1:缺少数据", "2:缺少材料", "3:其他","4:整体性意见","5:修改文字"})
    @ApiModelProperty(value = "意见类型")
    @TableField("YJLXCOPY")
    private String yjlxcopy;
    /**
     * 材料名称
     */
    @ExcelProperty("材料名称")
    @ApiModelProperty(value = "材料名称")
    @TableField("CLMC")
    private String clmc;/**

     * 快捷标签
     */
    @ExcelProperty("快捷标签")
    @ApiModelProperty(value = "快捷标签")
    @TableField("KJBQ")
    private String kjbq;

    /**
     * 审阅意见
     */
    @ExcelProperty("审阅意见")
    @ApiModelProperty(value = "审阅意见")
    @TableField("PJNR")
    private String pjnr;

}
