package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Syzj;
import com.xpaas.zpbg.mapper.SyzjMapper;
import com.xpaas.zpbg.service.ISyzjService;
import com.xpaas.zpbg.vo.SyzjVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-审阅专家 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Slf4j
@Service
public class SyzjServiceImpl extends BaseServiceImpl<SyzjMapper, Syzj> implements ISyzjService {

	@Override
	public IPage<SyzjVO> selectSyzjPage(IPage<SyzjVO> page, SyzjVO syzj) {
		return page.setRecords(baseMapper.selectSyzjPage(page, syzj));
	}

}
