package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_ZZCL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zzcl对象", description = "教学评价-自评报告-佐证材料")
public class Zzcl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 材料名称
	*/
	@ExcelProperty("材料名称")
	@ApiModelProperty(value = "材料名称")
	@TableField("CLMC")
	private String clmc;

	/**
	* 重命名
	*/
	@ExcelProperty("重命名")
	@ApiModelProperty(value = "重命名")
	@TableField("CMM")
	private String cmm;

	/**
	* 文件号
	*/
	@ExcelProperty("文件号")
	@ApiModelProperty(value = "文件号")
	@TableField("WJH")
	private String wjh;

	/**
	* 文件路径
	*/
	@ExcelProperty("文件路径")
	@ApiModelProperty(value = "文件路径")
	@TableField("WJLJ")
	private String wjlj;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	 * 存放位置
	 */
	@ExcelProperty("存放位置")
	@ApiModelProperty(value = "存放位置")
	@TableField("CFWZ")
	private String cfwz;

	/**
	 * 年度
	 */
	@ExcelProperty("年度")
	@ApiModelProperty(value = "年度")
	@TableField("ND")
	private String nd;

	/**
	 * 任务类型:字典编码rwlx
	 */
	@ExcelProperty("任务类型:字典编码rwlx")
	@ApiModelProperty(value = "任务类型:字典编码rwlx")
	@TableField("RWLX")
	private Integer rwlx;

	/**
	 * 批次
	 */
	@ExcelProperty("批次")
	@ApiModelProperty(value = "批次")
	@TableField("PC")
	private String pc;
	@ApiModelProperty(value = "批次名称")
	@TableField(exist = false)
	private String pcmc;

	@TableField(exist = false)
	private Integer yyzt;
	@TableField(exist = false)
	private String yyztstr;
	@TableField(exist = false)
	private Integer yycs;
	@TableField(exist = false)
	private String ssmk;
	@TableField(exist = false)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long ssmkid;
	@TableField(exist = false)
	private Integer editOk;

	@ApiModelProperty(value = "模块类型")
	@TableField(exist = false)
	private Integer mklx;
	@ApiModelProperty(value = "模块名称")
	@TableField(exist = false)
	private String mkmc;
	@ApiModelProperty(value = "模块ID数组")
	@TableField(exist = false)
	private Long[] mkidList;
	@ApiModelProperty(value = "是否本用户上传")
	@TableField(exist = false)
	private boolean isploadinperson;
	@ApiModelProperty(value = "是否引用")
	@TableField(exist = false)
	private boolean isquote;
	@ApiModelProperty(value = "模块id")
	@TableField(exist = false)
	private String mkid;
	@ApiModelProperty(value = "模块id集合")
	@TableField(exist = false)
	private String ssmkids;
	@ApiModelProperty(value = "模块id列表")
	@TableField(exist = false)
	private List<String> ssmkidList;


}
