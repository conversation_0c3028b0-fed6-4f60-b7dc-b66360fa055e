package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Syzj;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-审阅专家视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SyzjVO对象", description = "教学评价-自评报告-审阅专家")
public class SyzjVO extends Syzj {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
