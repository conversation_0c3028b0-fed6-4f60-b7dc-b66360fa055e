package com.xpaas.zpbg.common.utils;

import java.util.ArrayList;
import java.util.List;

public class ListGroupUtil {
    /**
     * 将集合按指定分组数分组
     *
     * @param list     数据集合
     * @param fzs 分组个数
     * @return 分组结果
     */
    public static <T> List<List<T>> groupListByQuantity(List<T> list, int fzs) {
        if (list == null || list.size() == 0) {
            return null;
        }
        int quantity = (int) Math.ceil(list.size()* 1.0 / fzs );


        List<List<T>> wrapList = new ArrayList<List<T>>();
        int count = 0;
        while (count < list.size()) {
            wrapList.add(new ArrayList<T>(list.subList(count, Math.min((count + quantity), list.size()))));
            count += quantity;
        }

        return wrapList;
    }

    public static void main(String[] args) {
        List<Integer> list = new ArrayList<Integer>();
        for (int i = 1; i <= 1010; i++) {
            list.add(i);
        }

        String cqjg = "";
        int num = 1;//第num分组
        List<List<Integer>> fzlist = ListGroupUtil.groupListByQuantity(list, 3);
        for (List<Integer> jgList : fzlist) {
            for (int i = 0; i < jgList.size(); i++) {
                //获取人员id
                int s = jgList.get(i);
                cqjg = cqjg  + s + "," + "第" + num + "组"+";";
            }
            num++;
        }
        System.out.println(cqjg);
    }



}
