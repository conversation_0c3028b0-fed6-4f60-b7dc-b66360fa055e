package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Sjbgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-数据表关联视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SjbglVO对象", description = "教学评价-自评报告-数据表关联")
public class SjbglVO extends Sjbgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "材料地址")
    private String cldz;

    @ApiModelProperty(value = "多条记录")
    private List<Sjbgl> selectList;

    @ApiModelProperty(value = "关联KEY字符串")
    private String glkeyStr;

    @ApiModelProperty(value = "关联KEY列表")
    private List<String> glkeyList;

    @ApiModelProperty(value = "重复数量")
    private Integer cfsl;

    @ApiModelProperty(value = "序号")
    private Integer xh;
}
