package com.xpaas.zpbg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 本级人才库实体类
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@Data
@TableName("T_DT_XLGL_ZYRCDW_BJRCK")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bjrck对象", description = "本级人才库")
public class Demo extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 姓名
	*/
		@ApiModelProperty(value = "姓名")
		@TableField("XM")
	private String xm;
	/**
	* 出生年月
	*/
		@ApiModelProperty(value = "出生年月")
		@TableField("CSNY")
		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date csny;
	/**
	* 性别
	*/
		@ApiModelProperty(value = "性别")
		@TableField("XB")
	private String xb;
	/**
	* 入伍时间
	*/
		@ApiModelProperty(value = "入伍时间")
		@TableField("RWSJ")
		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date rwsj;
	/**
	* 民族
	*/
		@ApiModelProperty(value = "民族")
		@TableField("MZ")
	private String mz;
	/**
	* 入党时间
	*/
		@ApiModelProperty(value = "入党时间")
		@TableField("RDSJ")
		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date rdsj;
	/**
	* 学历
	*/
		@ApiModelProperty(value = "学历")
		@TableField("XL")
	private String xl;
	/**
	* 职务
	*/
		@ApiModelProperty(value = "职务")
		@TableField("ZW")
	private String zw;
	/**
	* 军衔
	*/
		@ApiModelProperty(value = "军衔")
		@TableField("JX")
	private String jx;
	/**
	* 现从事专业
	*/
		@ApiModelProperty(value = "现从事专业")
		@TableField("XCSZY")
	private String xcszy;
	/**
	* 办公电话
	*/
		@ApiModelProperty(value = "办公电话")
		@TableField("BGDH")
	private String bgdh;
	/**
	* 手机号码
	*/
		@ApiModelProperty(value = "手机号码")
		@TableField("SJHM")
	private String sjhm;
	/**
	* 专业领域
	*/
		@ApiModelProperty(value = "专业领域")
		@TableField("ZYLY")
	private String zyly;
	/**
	* 专业领域
	*/
		@ApiModelProperty(value = "专业领域")
		@TableField("ZYLYTWO")
	private String zylytwo;
	/**
	* 部职级
	*/
		@ApiModelProperty(value = "部职级")
		@TableField("BZJ")
	private String bzj;
	/**
	* 推荐单位
	*/
		@ApiModelProperty(value = "推荐单位")
		@TableField("TJDW")
	private String tjdw;
	/**
	* 推荐日期
	*/
		@ApiModelProperty(value = "推荐日期")
		@TableField("TJRQ")
		@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date tjrq;
	/**
	* 推荐理由
	*/
		@ApiModelProperty(value = "推荐理由")
		@TableField("TJLY")
	private String tjly;
	/**
	* 来源
	*/
		@ApiModelProperty(value = "来源")
		@TableField("LY")
	private String ly;
	/**
	* 评审状态
	*/
		@ApiModelProperty(value = "评审状态")
		@TableField("PSZT")
	private String pszt;
	/**
	* 适用性
	*/
		@ApiModelProperty(value = "适用性")
		@TableField("SYX")
	private String syx;
	/**
	* 推荐人
	*/
		@ApiModelProperty(value = "推荐人")
		@TableField("TJR")
	private String tjr;
	/**
	* 接收人
	*/
		@ApiModelProperty(value = "接收人")
		@TableField("JSR")
	private String jsr;
	/**
	* 人才等级
	*/
		@ApiModelProperty(value = "人才等级")
		@TableField("RCDJ")
	private String rcdj;
	/**
	* 人才类型
	*/
		@ApiModelProperty(value = "人才类型")
		@TableField("RCLXX")
	private String rclxx;
	/**
	 * 冗余字段
	 */
		@ApiModelProperty(value = "冗余字段")
		@TableField("RYZD")
	private String ryzd;

	/**
	 * 证件类别
	 */
	@ApiModelProperty(value = "证件类别")
	@TableField("ZJLB")
	private String zjlb;
	/**
	 * 证件号码
	 */
	@ApiModelProperty(value = "证件号码")
	@TableField("ZJHM")
	private String zjhm;
	/**
	 * 籍贯
	 */
	@ApiModelProperty(value = "籍贯")
	@TableField("JG")
	private String jg;
	/**
	 * 家庭住址
	 */
	@ApiModelProperty(value = "家庭住址")
	@TableField("JTZZ")
	private String jtzz;
	/**
	 * 政治面貌
	 */
	@ApiModelProperty(value = "政治面貌")
	@TableField("ZZMM")
	private String zzmm;
	/**
	 * 婚姻状况
	 */
	@ApiModelProperty(value = "婚姻状况")
	@TableField("HYZK")
	private String hyzk;
	/**
	 * 入伍地点
	 */
	@ApiModelProperty(value = "入伍地点")
	@TableField("RWDD")
	private String rwdd;
	/**
	 * 部队代号
	 */
	@ApiModelProperty(value = "部队代号")
	@TableField("BDDH")
	private String bddh;
	/**
	 * 部队番号
	 */
	@ApiModelProperty(value = "部队番号")
	@TableField("BDFH")
	private String bdfh;
	/**
	 * 职务级别
	 */
	@ApiModelProperty(value = "职务级别")
	@TableField("ZWJB")
	private String zwjb;
	/**
	 * 岗位等级
	 */
	@ApiModelProperty(value = "岗位等级")
	@TableField("GWDJ")
	private String gwdj;
	/**
	 * 所属机构
	 */
	@ApiModelProperty(value = "所属机构")
	@TableField("SSJG")
	private String ssjg;
	/**
	 * 主要职能
	 */
	@ApiModelProperty(value = "主要职能")
	@TableField("ZYZN")
	private String zyzn;
	/**
	 * 专家级别
	 */
	@ApiModelProperty(value = "专家级别")
	@TableField("ZJJB")
	private String zjjb;
	/**
	 * 研究方向
	 */
	@ApiModelProperty(value = "研究方向")
	@TableField("YJFX")
	private String yjfx;
	/**
	 * 专家开始时间
	 */
	@ApiModelProperty(value = "专家开始时间")
	@TableField("ZJKSSJ")
	private Date zjkssj;
	/**
	 * 专家结束时间
	 */
	@ApiModelProperty(value = "专家结束时间")
	@TableField("ZJJSSJ")
	private Date zjjssj;
	/**
	 * 专家类别
	 */
	@ApiModelProperty(value = "专家类别")
	@TableField("ZJXXLB")
	private String zjxxlb;
	/**
	 * 任职状态
	 */
	@ApiModelProperty(value = "任职状态")
	@TableField("RZZT")
	private String rzzt;
	/**
	 * 代表论著
	 */
	@ApiModelProperty(value = "代表论著")
	@TableField("DBLZ")
	private String dblz;
	/**
	 * 入库时间
	 */
	@ApiModelProperty(value = "入库时间")
	@TableField("RKSJ")
	private Date rksj;
	/**
	 * 专业特长
	 */
	@ApiModelProperty(value = "专业特长")
	@TableField("ZYTC")
	private String zytc;
	/**
	 * 管理工作突出贡献
	 */
	@ApiModelProperty(value = "管理工作突出贡献")
	@TableField("GLGZTCGX")
	private String glgztcgx;
	/**
	 * 部队管理相关情况
	 */
	@ApiModelProperty(value = "部队管理相关情况")
	@TableField("BDGLXGQK")
	private String bdglxgqk;
	/**
	 * 接收单位
	 */
	@ApiModelProperty(value = "接收单位")
	@TableField("JSDW")
	private String jsdw;

}
