package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Sjcl;
import com.xpaas.zpbg.vo.SjclVO;

/**
 * 教学评价-自评报告-数据材料 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface ISjclService extends BaseService<Sjcl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjcl
	 * @return
	 */
	IPage<SjclVO> selectSjclPage(IPage<SjclVO> page, SjclVO sjcl);

	/**
	 * 撰写端新增
	 *
	 * @param sjclVO
	 * @return
	 */
	boolean saveFromSjzx(SjclVO sjclVO);
}
