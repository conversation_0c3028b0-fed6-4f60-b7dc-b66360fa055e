package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Gjcjcqk;
import com.xpaas.zpbg.vo.GjcjcqkVO;

/**
 * 教学评价-自评报告-关键词检测情况 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IGjcjcqkService extends BaseService<Gjcjcqk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcjcqk
	 * @return
	 */
	IPage<GjcjcqkVO> selectGjcjcqkPage(IPage<GjcjcqkVO> page, GjcjcqkVO gjcjcqk);

}
