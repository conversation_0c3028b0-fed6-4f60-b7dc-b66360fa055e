<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.SjzbglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sjzbglResultMap" type="com.xpaas.zpbg.entity.Sjzbgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="GLWZ" property="glwz"/>
        <result column="SJZBLX" property="sjzblx"/>
        <result column="ZBMC" property="zbmc"/>
        <result column="CKJG" property="ckjg"/>
        <result column="SJLY" property="sjly"/>
        <result column="SJLYMC" property="sjlymc"/>
        <result column="SJBMCORG" property="sjbmcorg"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PX" property="px"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="sjzbglResultMapVO" type="com.xpaas.zpbg.vo.SjzbglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="GLWZ" property="glwz"/>
        <result column="SJZBLX" property="sjzblx"/>
        <result column="ZBMC" property="zbmc"/>
        <result column="CKJG" property="ckjg"/>
        <result column="SJLY" property="sjly"/>
        <result column="SJLYMC" property="sjlymc"/>
        <result column="SJBMCORG" property="sjbmcorg"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PX" property="px"/>
        <result column="XH" property="xh"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectSjzbglPage" resultMap="sjzbglResultMapVO">
        select t2.* from (
        select t1.*,(select @rownum:=@rownum+1) xh from(
        select
            sjzbgl.*,(select @rownum:=0) r,bbpz.px as bbpzpx
        from
            T_DT_JXPJ_ZPBG_SJZBGL sjzbgl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = sjzbgl.BBID
                and bbpz.COMMENT_ID = sjzbgl.GLKEY
                and bbpz.SCBJ = 0
        where
            sjzbgl.scbj = 0

            <if test="sjzbgl.bgid != null and sjzbgl.bgid != ''">
                and sjzbgl.BGID = #{sjzbgl.bgid}
            </if>

            <if test="sjzbgl.bgmkid != null and sjzbgl.bgmkid != ''">
                and sjzbgl.BGMKID = #{sjzbgl.bgmkid}
            </if>

            <if test="sjzbgl.bbid != null and sjzbgl.bbid != ''">
                and sjzbgl.BBID = #{sjzbgl.bbid}
            </if>

        ) t1 ) t2
        <where>
            <if test="sjzbgl.glkeyList != null and sjzbgl.glkeyList.size() > 0">
                <foreach collection="sjzbgl.glkeyList" item="item" open=" AND t2.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by
        t2.bbpzpx, t2.PX, t2.ID
    </select>

    <!--取得关键词-->
    <select id="searchGjc" resultType="com.xpaas.zpbg.vo.SjzbglVO">
        SELECT DISTINCT
            gjc.ID AS gjcid,
            gjc.GJCMC AS gjcmc
        FROM
            T_DT_JXPJ_ZPBG_GJCGL gjc
        LEFT JOIN
            T_DT_JXPJ_ZPBG_GJCGLMK gjcmk
                ON gjcmk.GJCID = gjc.ID
                AND gjcmk.SCBJ = 0
        WHERE
            gjc.GJCLX = 9
            AND gjc.ZBLY = (SELECT MKLX FROM T_DT_JXPJ_ZPBG_BGGL WHERE ID = #{bgid})
            AND (
                gjc.GLFW = 1
                OR ( gjc.GLFW = 2 AND gjcmk.MKID = #{mkid} )
            )
    </select>

    <!--不同的内容引用了相同的数据表-->
    <select id="sameGl" resultType="com.xpaas.zpbg.vo.SjzbglVO">
        SELECT
            gl.*
        FROM
            T_DT_JXPJ_ZPBG_SJZBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
        WHERE
            gl.BGID = #{bgid}
            AND gl.BGMKID = #{bgmkid}
            AND gl.BBID = #{bbid}
            AND gl.GLKEY != #{glkey}
            AND gl.SCBJ = 0

            <if test="sjzblx != null and sjzblx == 1">
                AND gl.ZBMC = #{zbmc}
            </if>
            <if test="sjzblx != null and sjzblx == 2">
                AND gl.GJCID = #{gjcid}
            </if>
    </select>

    <!--校验关键词是否关联-->
    <select id="checkGlkey" resultType="String">
        ( SELECT count( 1 )
            FROM T_DT_JXPJ_ZPBG_SJZBGL gl
            inner join
                t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
            WHERE gl.SCBJ = 0
            AND gl.BBID = #{bbid}

            <if test="glkeyList != null and glkeyList.size() > 0">
                <foreach collection="glkeyList" item="item" open=" AND gl.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )
        UNION ALL
        ( SELECT count( 1 )
            FROM T_DT_JXPJ_ZPBG_SJBGL gl
            inner join
                t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
            WHERE gl.SCBJ = 0
            AND gl.BBID = #{bbid}

            <if test="glkeyList != null and glkeyList.size() > 0">
                <foreach collection="glkeyList" item="item" open=" AND gl.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )
    </select>

    <!--取得关联信息-->
    <select id="getGlAllList" resultType="com.xpaas.zpbg.vo.SjzbglVO">
        select
            a.*
        from
        (( SELECT
            gl.CJR,
            gl.CJRQ,
            gl.GLKEY,
            gl.GLWZ,
            (case when gl.SJZBLX = 1 then gl.ZBMC else gl.GJCMC end) as mc
        FROM
            T_DT_JXPJ_ZPBG_SJZBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
            on bbpz.BBID = gl.BBID
            and bbpz.COMMENT_ID = gl.GLKEY
            and bbpz.SCBJ = 0
        WHERE
            gl.SCBJ = 0
            AND gl.BBID = #{bbid}

            <if test="glkeyList != null and glkeyList.size() > 0">
                <foreach collection="glkeyList" item="item" open=" AND gl.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )
        UNION ALL
        ( SELECT
            gl.CJR,
            gl.CJRQ,
            gl.GLKEY,
            gl.GLWZ,
            gl.SJBMC as mc
        FROM 
            T_DT_JXPJ_ZPBG_SJBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
            on bbpz.BBID = gl.BBID
            and bbpz.COMMENT_ID = gl.GLKEY
            and bbpz.SCBJ = 0
        WHERE
            gl.SCBJ = 0
            AND gl.BBID = #{bbid}

            <if test="glkeyList != null and glkeyList.size() > 0">
                <foreach collection="glkeyList" item="item" open=" AND gl.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        )) a
        order by
            a.CJRQ
    </select>

    <!--查询数据材料列表-->
    <select id="searchSjlc" resultType="com.xpaas.zpbg.vo.SjclVO">
        SELECT
            sjcl.*
        FROM
            T_DT_JXPJ_ZPBG_SJCL sjcl
        INNER JOIN
            T_DT_JXPJ_ZPBG_SJCLSSMK ssmk
                ON ssmk.SJCLID = sjcl.ID
                AND ssmk.SCBJ = 0
                AND ssmk.MKID = #{mkid}
        WHERE
            sjcl.SCBJ = 0
            AND sjcl.CLMC LIKE concat( '%', #{gjcmc}, '%' )
        ORDER BY
            sjcl.PX,
            sjcl.ID
    </select>

    <!--判断是否已关联-->
    <select id="getGlList" resultType="com.xpaas.zpbg.vo.SjzbglVO">
        SELECT
            gl.*
        FROM
            T_DT_JXPJ_ZPBG_SJZBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
        WHERE
            gl.BGID = #{bgid}
            AND gl.BGMKID = #{bgmkid}
            AND gl.BBID = #{bbid}
            AND gl.GLKEY = #{glkey}
            AND gl.SCBJ = 0
        ORDER BY
            gl.PX
    </select>
</mapper>
