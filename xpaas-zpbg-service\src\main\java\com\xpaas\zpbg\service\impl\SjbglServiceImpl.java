package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Sjbgl;
import com.xpaas.zpbg.mapper.SjbglMapper;
import com.xpaas.zpbg.service.ISjbglService;
import com.xpaas.zpbg.utils.ZpbgUtils;
import com.xpaas.zpbg.vo.SjbglVO;
import com.xpaas.zpbg.wrapper.SjbglWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-数据表关联 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class SjbglServiceImpl extends BaseServiceImpl<SjbglMapper, Sjbgl> implements ISjbglService {

	@Autowired
	private SjbglWrapper sjbglWrapper;
	
	@Override
	public IPage<SjbglVO> selectSjbglPage(IPage<SjbglVO> page, SjbglVO sjbgl) {
		return page.setRecords(baseMapper.selectSjbglPage(page, sjbgl));
	}

	@Override
	public String sameGl(SjbglVO sjbglVO) {
		// 判断重复关联
		checkGl(sjbglVO);

		List<String> msg = new ArrayList();

		if(sjbglVO.getSelectList() != null && sjbglVO.getSelectList().size() > 0) {
			for(Sjbgl item : sjbglVO.getSelectList()) {
				List<SjbglVO> glList = baseMapper.sameGl(item);

				if(glList != null && glList.size() > 0) {
					List<String> glwz = new ArrayList();

					for(SjbglVO vo : glList) {
						glwz.add(ZpbgUtils.convertGlwz(vo.getGlwz()));
					}

					msg.add("数据表《" + item.getSjbmc() + "》已在报告" + Joiner.on("、").join(glwz) + "处引用");
				}
			}
		}

		if(msg.size() > 0) {
			return Joiner.on("<br>").join(msg) + "，确定要引用吗？";
		} else {
			return "";
		}
	}

	@Override
	public boolean saveBatch(SjbglVO sjbglVO) {
		if(sjbglVO.getSelectList() != null && sjbglVO.getSelectList().size() > 0) {
			// 判断重复关联
			int px = checkGl(sjbglVO);

			for(Sjbgl item : sjbglVO.getSelectList()) {
				item.setPx(px);
				px++;
			}

			super.saveBatch(sjbglVO.getSelectList());
		}

		return true;
	}

	private int checkGl(SjbglVO sjbglVO) {
		int px = 1;
		List<String> mc = new ArrayList<String>();
		List<SjbglVO> glList = baseMapper.getGlList(sjbglVO.getSelectList().get(0));

		if(glList != null) {
			for(SjbglVO vo : glList) {
				for(Sjbgl item : sjbglVO.getSelectList()) {
					// 没有关联KEY时，不用校验
					if(StringUtil.isEmpty(item.getGlkey())) {
						return px;
					}

					if(item.getSjly() == 1) {
						if(item.getSjb().equals(vo.getSjb())) {
							mc.add(item.getSjbmc());
						}
					}
					if(item.getSjly() == 2) {
						if(item.getSjclid().equals(vo.getSjclid())) {
							mc.add(item.getSjbmc());
						}
					}
				}

				px = vo.getPx() + 1;
			}
		}

		if(mc.size() > 0) {
			throw new ServiceException(Joiner.on("、").join(mc) + "，关联已添加！");
		}

		return px;
	}

	@Override
	public void deleteGl(String ids) {
		if(StringUtil.isNotBlank(ids)) {
			String[] array = ids.split(",");

			for(String id : array) {
				Sjbgl sjbgl = this.getById(id);
				this.deleteLogic(Func.toLongList(id));
			}
		} else {
			return;
		}

		// 取得已关联的数据，并刷新序号
//		List<SjbglVO> glList = baseMapper.getGlList(sjbglWrapper.entityVO(sjbgl));
//		if(glList != null) {
//			for(int i = 1; i <= glList.size(); i++) {
//				SjbglVO vo = glList.get(i - 1);
//				vo.setPx(i);
//
//				this.updateById(vo);
//			}
//		}
	}
}
