package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xpaas.zpbg.entity.Home;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;
import java.util.Map;

/**
 * 自评报告-首页视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "HomeVO对象", description = "自评报告-首页")
public class HomeVO extends Home {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "报告id")
	private String bgId;

	@ApiModelProperty(value = "报告名称")
	private String bgmc;

	@ApiModelProperty(value = "进度id")
	private String jdId;

	@ApiModelProperty(value = "进度名称")
	private String jdmc;

	/**
	 * 开始时间(精确到日) 进度
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "开始时间")
	private String kssj;

	/**
	 * 开始时间(精确到日) 进度
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "撰写结束时间")
	private String zxjssj;

	/**
	 * 开始时间(精确到日) 进度
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "审阅结束时间")
	private String syjssj;


	/**
	 * 当前系统初始时间(精确到秒) 进度
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "当前系统初始时间")
	private String nowTime;

	@ApiModelProperty(value = "报告模块id")
	private String bgmkId;

	@ApiModelProperty(value = "模块名称")
	private String mkmc;

	@ApiModelProperty(value = "版本id")
	private String bbId;

	/**
	 * 牵头人_名称
	 */
	@ApiModelProperty(value = "牵头人")
	private String qtrStr;

	/**
	 * 撰写人_名称
	 */
	@ApiModelProperty(value = "撰写人")
	private String zxrStr;

	@ApiModelProperty(value = "用户")
	private String userId;

	@ApiModelProperty(value = "报告模块状态")
	private Integer bgmkzt;

	@ApiModelProperty(value = "报告模块状态类型")
	private Integer bgmkztType;

	@ApiModelProperty(value = "模块类型")
	private Integer mklx;

	@ApiModelProperty(value = "排序")
	private Integer px;

	@ApiModelProperty(value = "点长")
	private Integer dzFlag;

	@ApiModelProperty(value = "本进度撰审版次")
	private Integer bjdzsbc;

	@ApiModelProperty(value = "占比")
	private Integer zb;

	@ApiModelProperty(value = "专家审阅的报告模块状态")
	private Integer syzt;

	@ApiModelProperty(value = "专家审阅的历史版本id")
	private String bbgllsbbId;

	@ApiModelProperty(value = "进度状态")
	private Integer jdztFlag;

	@ApiModelProperty(value = "所有模块对应的任务分工列表")
	private List<Map<String, Object>> symkRwfgList;

	@ApiModelProperty(value = "所有模块列表")
	private List<Map<String, Object>> symkList;

	@ApiModelProperty(value = "年度")
	private String nd;

	@ApiModelProperty(value = "任务类型")
	private String rwlx;

	@ApiModelProperty(value = "指标体系")
	private String bglx;

	@ApiModelProperty(value = "报告名称(不带年度)")
	private String mc;

}
