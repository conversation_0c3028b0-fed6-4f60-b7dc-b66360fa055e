package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zcclssmk;
import com.xpaas.zpbg.vo.ZcclssmkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-备查材料所属模块包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Component
public class ZcclssmkWrapper extends BaseEntityWrapper<Zcclssmk, ZcclssmkVO>  {


	@Override
	public ZcclssmkVO entityVO(Zcclssmk zcclssmk) {
		ZcclssmkVO zcclssmkVO = Objects.requireNonNull(BeanUtil.copy(zcclssmk, ZcclssmkVO.class));
		//User cjr = UserCache.getUser(zcclssmk.getCjr());
		//if (cjr != null){
		//	zcclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zcclssmk.getGxr());
		//if (gxr != null){
		//	zcclssmkVO.setGxrName(gxr.getName());
		//}
		return zcclssmkVO;
	}

    @Override
    public ZcclssmkVO wrapperVO(ZcclssmkVO zcclssmkVO) {
		//User cjr = UserCache.getUser(zcclssmkVO.getCjr());
		//if (cjr != null){
		//	zcclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zcclssmkVO.getGxr());
		//if (gxr != null){
		//	zcclssmkVO.setGxrName(gxr.getName());
		//}
        return zcclssmkVO;
    }

}
