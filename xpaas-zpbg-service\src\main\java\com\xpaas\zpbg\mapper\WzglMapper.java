package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Wzgl;
import com.xpaas.zpbg.vo.WzglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自评报告-文章管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Repository
public interface WzglMapper extends BaseMapper<Wzgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param wzgl
	 * @return
	 */
	List<WzglVO> selectWzPage(IPage page, WzglVO wzgl);

	/**
	 * 取得慕课列表
	 *
	 * @return
	 */
	List<WzglVO> getKcList();

	/**
	 * 取得浏览人数
	 *
	 * @return
	 */
	int getLlrs(WzglVO wzglVO);
}
