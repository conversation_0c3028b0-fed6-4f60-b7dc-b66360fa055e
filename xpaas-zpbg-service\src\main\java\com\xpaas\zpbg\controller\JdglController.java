package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.service.IJdglService;
import com.xpaas.zpbg.service.IJdmkglService;
import com.xpaas.zpbg.service.IZtglService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.JdglVO;
import com.xpaas.zpbg.vo.JdmkglVO;
import com.xpaas.zpbg.wrapper.JdglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-进度管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/jdgl")
@Api(value = "教学评价-自评报告-进度管理", tags = "教学评价-自评报告-进度管理接口")
public class JdglController extends BaseController {

    private JdglWrapper jdglWrapper;
    private IJdglService jdglService;
    private IJdmkglService jdmkglService;
    private IZtglService ztglService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入jdgl")
    @ApiLog("进度-详情")
    public R<JdglVO> detail(Jdgl jdgl) {
        Jdgl detail = jdglService.getOne(Condition.getQueryWrapper(jdgl));
        return R.data(jdglWrapper.entityVO(detail));
    }

    /**
     * 分页 教学评价-自评报告-进度管理 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入jdgl")
    @ApiLog("进度-分页")
    public R<IPage<JdglVO>> list(Jdgl jdgl, Query query) {
        IPage<Jdgl> pages = jdglService.page(Condition.getPage(query), Condition.getQueryWrapper(jdgl));
        return R.data(jdglWrapper.pageVO(pages));
    }

    /**
     * 自定义分页 教学评价-自评报告-进度管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入jdgl")
    @ApiLog("进度-自定义分页")
    public R<IPage<JdglVO>> page(JdglVO jdgl, Query query) {
        IPage<JdglVO> pages = jdglService.selectJdglPage(Condition.getPage(query), jdgl);
        return R.data(jdglWrapper.wrapperPageVO(pages));
    }


    /**
     * 新增 教学评价-自评报告-进度管理
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入jdgl")
    @ApiLog("进度-新增")
    @Transactional
    public R save(@Valid @RequestBody JdglVO jdglVO) {
        ztglService.lockBg(jdglVO.getBgid());

        jdglService.checkExistByInfo(jdglVO);
        boolean b = jdglService.save(jdglVO);

        ztglService.whileJdChange(jdglVO.getBgid());

        return R.status(b);
    }

    /**
     * 修改 教学评价-自评报告-进度管理
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入jdgl")
    @ApiLog("进度-修改")
    @Transactional
    public R update(@Valid @RequestBody JdglVO jdglVO) {
        ztglService.lockBg(jdglVO.getBgid());

        jdglService.checkExistByInfo(jdglVO);
        boolean b = jdglService.updateById(jdglVO);

        ztglService.whileJdChange(jdglVO.getBgid());

        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-进度管理 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入jdgl")
    @ApiLog("进度-新增或修改")
    @Transactional
    public R submit(@Valid @RequestBody Jdgl jdgl) {
        ztglService.lockBg(jdgl.getBgid());

        boolean b = jdglService.saveOrUpdate(jdgl);

        ztglService.whileJdChange(jdgl.getBgid());

        return R.status(b);
    }


    /**
     * 删除 教学评价-自评报告-进度管理
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("进度-逻辑删除")
    @Transactional
    public R remove(@RequestBody BaseDeleteVO deleteVO) {
        boolean b = jdglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
        return R.status(b);
    }


    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("进度-高级查询")
    public R<IPage<JdglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Jdgl> queryWrapper = Condition.getQueryWrapper(map, Jdgl.class);
        IPage<Jdgl> pages = jdglService.page(Condition.getPage(query), queryWrapper);
        return R.data(jdglWrapper.pageVO(pages));
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("进度-导出")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<Jdgl> queryWrapper = Condition.getQueryWrapper(map, Jdgl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<Jdgl> list = jdglService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Jdgl.class);
    }


//	/**
//	 * 导入Excel
//	 */
//	@PostMapping("/import")
//	@ApiOperationSupport(order = 10)
//	@ApiOperation(value = "导入Excel", notes = "导入Excel")
//	@ApiLog("进度-导入")
//	public R importExcel(@RequestParam("file") MultipartFile file) {
//		List<Jdgl> list = ExcelUtil.read(file, Jdgl.class);
//		//TODO 此处需要根据具体业务添加代码
//		jdglService.saveBatch(list);
//		return R.status(true);
//	}

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("进度-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Jdgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Jdgl> list = jdglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        //TODO 此处需要根据具体业务添加代码
        //columnFiledNames.add("id");
        //columnFiledNames.add("cjrq");
        ExcelUtil.export(response, "Jdgl导入模板", "Jdgl导入模板", columnFiledNames, list, Jdgl.class);
    }

    /**
     * 报告信息取得 教学评价-自评报告-进度管理
     */
    @GetMapping("/getHeaderInfo")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "报告信息取得", notes = "传入jdgl")
    @ApiLog("进度-报告信息取得")
    public R<IPage<JdglVO>> getHeaderInfo(JdglVO jdgl, Query query) {
        IPage<JdglVO> pages = jdglService.getHeaderInfo(Condition.getPage(query), jdgl);
        return R.data(jdglWrapper.wrapperPageVO(pages));
    }

    /**
     * 报告模块取得 教学评价-自评报告-进度管理
     */
    @GetMapping("/getBgmkInfo")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "报告模块取得", notes = "传入jdgl")
    @ApiLog("进度-报告模块取得")
    public List<JdglVO> getBgmkInfo(JdglVO jdgl) {
        return jdglService.getBgmkInfo( jdgl);
    }

    /**
     * 进度模块关联取得 教学评价-自评报告-进度管理
     */
    @GetMapping("/getJdmkglInfo")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "进度模块关联取得", notes = "传入jdgl")
    @ApiLog("进度-进度模块关联取得")
    public List<JdglVO> getJdmkglInfo(JdglVO jdgl) {
        return jdglService.getJdmkglInfo(jdgl);
    }

    /**
     * 进度模块关联保存 教学评价-自评报告-进度管理
     */
    @PostMapping("/jdmkglSave")
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "进度模块关联保存", notes = "传入jdgl")
    @ApiLog("进度-进度模块关联保存")
    @Transactional
    public R jdmkglSave(@Valid @RequestBody JdmkglVO JdmkglVO) {
        Jdgl detail = jdglService.getById(JdmkglVO.getJdglid());
        Long bgid = detail.getBgid();
        ztglService.lockBg(bgid);

        boolean b = jdmkglService.jdmkglSave(JdmkglVO);

        ztglService.whileJdChange(bgid);

        return R.status(b);
    }
}
