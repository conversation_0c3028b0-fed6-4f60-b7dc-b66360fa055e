package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.vo.JdglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-进度管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Repository
public interface JdglMapper extends BaseMapper<Jdgl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param jdgl
     * @return
     */
    List<JdglVO> selectJdglPage(IPage page, JdglVO jdgl);

    /**
     * 报告信息取得
     *
     * @param page
     * @param jdgl
     * @return
     */
    List<JdglVO> getHeaderInfo(IPage page, JdglVO jdgl);

    /**
     * 报告模块取得
     *
     * @param jdgl
     * @return
     */
    List<JdglVO> getBgmkInfo(JdglVO jdgl);

    /**
     * 进度模块关联取得
     *
     * @param jdgl
     * @return
     */
    List<JdglVO> getJdmkglInfo(JdglVO jdgl);

    /**
     * 进度名称存在查询
     *
     * @param jdgl
     * @return
     */
    int checkExistByInfo(JdglVO jdgl);

//    /**
//     * 查询报告当前进度信息
//     *
//     * @param bgidList
//     * @return
//     */
//    List<Jdgl> selectJdglByBggl(@Param("bgidList") List<Long> bgidList);

}
