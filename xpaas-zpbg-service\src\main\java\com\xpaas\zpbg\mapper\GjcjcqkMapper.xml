<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.GjcjcqkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcjcqkResultMap" type="com.xpaas.zpbg.entity.Gjcjcqk">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="JCJLID" property="jcjlid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PC" property="pc"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcjcqkResultMapVO" type="com.xpaas.zpbg.vo.GjcjcqkVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="JCJLID" property="jcjlid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PC" property="pc"/>

        <result column="GJCLX" property="gjclx"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectGjcjcqkPage" resultMap="gjcjcqkResultMapVO">
        SELECT
            cjqk.GJCID,
            cjqk.GJCMC,
            gjc.GJCLX,
            sum( cjqk.pc ) AS pc
        FROM
            T_DT_JXPJ_ZPBG_GJCJCQK cjqk
            LEFT JOIN T_DT_JXPJ_ZPBG_GJCGL gjc ON gjc.ID = cjqk.GJCID
        WHERE
            cjqk.scbj = 0

            <if test="gjcjcqk.jcjlid!=null and gjcjcqk.jcjlid!=''">
                and cjqk.JCJLID = #{gjcjcqk.jcjlid}
            </if>
        GROUP BY
            cjqk.GJCID
        ORDER BY
            pc DESC,
            cjqk.GJCID DESC
    </select>

</mapper>
