package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.vo.BgmkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-报告模块 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Repository
public interface BgmkMapper extends BaseMapper<Bgmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmk
	 * @return
	 */
	List<BgmkVO> selectBgmkPage(IPage page, BgmkVO bgmk);

}
