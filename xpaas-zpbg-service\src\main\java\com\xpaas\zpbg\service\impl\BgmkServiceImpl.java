package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.mapper.BgmkMapper;
import com.xpaas.zpbg.service.IBgmkService;
import com.xpaas.zpbg.vo.BgmkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-报告模块 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Slf4j
@Service
public class BgmkServiceImpl extends BaseServiceImpl<BgmkMapper, Bgmk> implements IBgmkService {

	@Override
	public IPage<BgmkVO> selectBgmkPage(IPage<BgmkVO> page, BgmkVO bgmk) {
		return page.setRecords(baseMapper.selectBgmkPage(page, bgmk));
	}

}
