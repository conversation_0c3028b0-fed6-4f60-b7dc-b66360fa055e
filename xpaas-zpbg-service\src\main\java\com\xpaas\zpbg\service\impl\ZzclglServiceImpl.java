package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.mapper.ZzclglMapper;
import com.xpaas.zpbg.service.IZzclglService;
import com.xpaas.zpbg.utils.ZpbgUtils;
import com.xpaas.zpbg.vo.ZzclglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-佐证材料关联 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class ZzclglServiceImpl extends BaseServiceImpl<ZzclglMapper, Zzclgl> implements IZzclglService {

	@Override
	public IPage<ZzclglVO> selectZzclglPage(IPage<ZzclglVO> page, ZzclglVO zzclgl) {
		return page.setRecords(baseMapper.selectZzclglPage(page, zzclgl));
	}

	@Override
	public ZzclglVO getMKId(Long bgmkid) {
		ZzclglVO mkid = baseMapper.getMkId(bgmkid);
		return mkid;
	}
	@Override
	public int getMaxPx(){
	  return baseMapper.getMaxPx();
	}
	@Override
	public String sameGl(ZzclglVO zzclglVO) {
		List<String> msg = new ArrayList();
		if(zzclglVO.getSelectList() != null && zzclglVO.getSelectList().size() > 0) {
			for(Zzclgl item : zzclglVO.getSelectList()) {
				List<ZzclglVO> glList = baseMapper.sameGl(item);
				if(glList != null && glList.size() > 0) {
					List<String> glwz = new ArrayList();
					for(ZzclglVO vo : glList) {
						glwz.add(ZpbgUtils.convertGlwz(vo.getGlwz()));
					}
                    String name  =  item.getCmm()==null||"".equals(item.getCmm())?item.getClmc():item.getCmm();
					boolean constainsFuillemets =name.contains("《")||name.contains("》");
					if(constainsFuillemets){
						msg.add("佐证材料" + name + "：已在报告中" + Joiner.on("，").join(glwz) + "引用,确定要引用吗？");
					}else{
						msg.add("佐证材料《" + name + "》：已在报告中" + Joiner.on("，").join(glwz) + "引用,确定要引用吗？");
					}

				}
			}
		}

		if(msg.size() > 0) {
			return Joiner.on("<br>").join(msg);
		} else {
			return "";
		}
	}

	@Override
	public int checkGlkey(ZzclglVO zzclglVO) {
		return baseMapper.checkGlkey(zzclglVO);
	}

	@Override
	public List<ZzclglVO> getzzclList(String bbid, List<String> glkeyList) {
		if(glkeyList.isEmpty()){
			return new ArrayList<>();
		}
        return baseMapper.getzzclList(bbid,glkeyList);
	}
	@Override
	public List<ZzclglVO>getZzlglList(Zzclgl zzclgl){
		return baseMapper.getZzlglList(zzclgl);
	}

	@Override
	public boolean updataPx(String id,String newpx){
		return  baseMapper.updataPx(id,newpx);
	}
}
