package com.xpaas.zlkgl.vo;

import com.xpaas.zlkgl.entity.WxzlWjj;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-资料库平台-外校资料文件夹树表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WxzlWjjVO对象", description = "教学评价-资料库平台-外校资料文件夹树表")
public class WxzlWjjVO extends WxzlWjj {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    /**
     * 子节点
     */
    private List<WxzlWjjVO> children;
}
