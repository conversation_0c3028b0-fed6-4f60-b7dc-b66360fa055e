package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bqgl;
import com.xpaas.zpbg.mapper.BqglMapper;
import com.xpaas.zpbg.service.IBqglService;
import com.xpaas.zpbg.vo.BqglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-标签管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class BqglServiceImpl extends BaseServiceImpl<BqglMapper, Bqgl> implements IBqglService {

	/**
	 * 标签管理端查询
	 */
	@Override
	public IPage<BqglVO> selectBqglPage(IPage<BqglVO> page, BqglVO bqgl) {
		return page.setRecords(baseMapper.selectBqglPage(page, bqgl));
	}

	/**
	 * 标签撰写端查询
	 */
	@Override
	public IPage<BqglVO> selectZjBqgl(IPage<BqglVO> page, BqglVO bqgl) {
		return page.setRecords(baseMapper.selectZjBqgl(page, bqgl));
	}

}
