package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Rwfg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-任务分工视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RwfgVO对象", description = "教学评价-自评报告-任务分工")
public class RwfgVO extends Rwfg {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "多个人员ID字符串")
    private String ryids;

}
