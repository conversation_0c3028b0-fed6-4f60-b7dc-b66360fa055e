package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;


/**
 * 自评报告-状态信息结果类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class ZtxxResultVO {
    private static final long serialVersionUID = 1L;

    // 版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbid;

    // 操作角色
    private ZtxxRoleType role;

    // 操作模式
    private ZtxxModeType mode;

    // 相关数据
    private ZtxxContext ctx;
}
