package com.xpaas.zpbg.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xpaas.zpbg.entity.Mkgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-模块管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MkglVO对象", description = "教学评价-自评报告-模块管理")
public class MkglVO extends Mkgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    /**
     * 进度模块关联标识
     */
    @ExcelProperty("进度模块关联标识")
    @ApiModelProperty(value = "进度模块关联标识")
    private Boolean jdmkglbs;

}
