package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Sjzbgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-数据指标关联视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SjzbglVO对象", description = "教学评价-自评报告-数据指标关联")
public class SjzbglVO extends Sjzbgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "关键词名称")
    private String gjcmc;

    @ApiModelProperty(value = "关联KEY字符串")
    private String glkeyStr;

    @ApiModelProperty(value = "关联KEY列表")
    private List<String> glkeyList;

    @ApiModelProperty(value = "模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mkid;

    @ApiModelProperty(value = "重复数量")
    private Integer cfsl;

    @ApiModelProperty(value = "指标名称/关键词/数据表名称")
    private String mc;

    @ApiModelProperty(value = "序号")
    private Integer xh;
}
