package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.vo.MkglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-模块管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Component
public class MkglWrapper extends BaseEntityWrapper<Mkgl, MkglVO>  {


	@Override
	public MkglVO entityVO(Mkgl mkgl) {
		MkglVO mkglVO = Objects.requireNonNull(BeanUtil.copy(mkgl, MkglVO.class));
		//User cjr = UserCache.getUser(mkgl.getCjr());
		//if (cjr != null){
		//	mkglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(mkgl.getGxr());
		//if (gxr != null){
		//	mkglVO.setGxrName(gxr.getName());
		//}
		return mkglVO;
	}

    @Override
    public MkglVO wrapperVO(MkglVO mkglVO) {
		//User cjr = UserCache.getUser(mkglVO.getCjr());
		//if (cjr != null){
		//	mkglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(mkglVO.getGxr());
		//if (gxr != null){
		//	mkglVO.setGxrName(gxr.getName());
		//}
        return mkglVO;
    }

}
