package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Gjcgl;
import com.xpaas.zpbg.vo.GjcglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-关键词管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface GjcglMapper extends BaseMapper<Gjcgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectGjcglPage(IPage page, GjcglVO gjcgl);

	/**
	 * 模块列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectMkglList(GjcglVO gjcgl);

	/**
	 * 报告列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectBgglList(GjcglVO gjcgl);

	/**
	 * 进度列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectJdglList(GjcglVO gjcgl);

	/**
	 * 最新进度查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectZxjd(GjcglVO gjcgl);

}
