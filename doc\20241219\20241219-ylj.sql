﻿-- 报告管理添加批次管理字段的外键id
ALTER TABLE T_DT_JXPJ_ZPBG_BGGL ADD COLUMN PCID BIGINT COMMENT '批次管理ID';
ALTER TABLE T_DT_JXPJ_ZPBG_BGGL ADD COLUMN BGLX tinyint(4) COMMENT '报告类型';
ALTER TABLE T_DT_JXPJ_ZPBG_BGGL ADD COLUMN BGGN varchar(3000) COMMENT '报告功能';
ALTER TABLE T_DT_JXPJ_ZPBG_BBGL ADD COLUMN DGBB tinyint(4) COMMENT '定稿版本  如果为1则时定稿版本';



ALTER TABLE T_DT_JXPJ_ZPBG_ZCCL ADD COLUMN PC varchar(3000) COMMENT '批次';
ALTER TABLE T_DT_JXPJ_ZPBG_ZZCL ADD COLUMN PC varchar(3000) COMMENT '批次';

DELETE FROM xpaas_dict_biz WHERE CODE='zpbg_bglx';

INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111111, '000000', 0, 'zpbg_bglx', NULL, '报告类型', NULL, '', 0, 0, NULL, 1, NULL, '2024-07-30 14:12:00', NULL, NULL, NULL, NULL, NULL,NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111112, '000000', 1818111111111111111, 'zpbg_bglx', '1', '综合评价自评报告', 1, NULL, 0, 0, NULL, 0, 'null,1818111111111111111', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111113, '000000', 1818111111111111111, 'zpbg_bglx', '2', '政治理论自评报告', 2, NULL, 0, 0, NULL, 0, 'null,1818111111111111111', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111114, '000000', 1818111111111111111, 'zpbg_bglx', '3', '专业评价报告', 4, NULL, 0, 0, NULL, 0, 'null,1818111111111111111', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111115, '000000', 1818111111111111111, 'zpbg_bglx', '4', '课程评价报告', 3, NULL, 0, 1, NULL, 0, 'null,1818111111111111111', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111116, '000000', 0, 'zpbg_bggn', NULL, '功能报告', NULL, '', 0, 0, NULL, 1, NULL, '2024-07-30 14:12:00', NULL, NULL, NULL, NULL, NULL,NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111117, '000000', 1818111111111111116, 'zpbg_bggn', '数据引用', '数据引用', 1, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111118, '000000', 1818111111111111116, 'zpbg_bggn', '关联佐证材料', '关联佐证材料', 2, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111119, '000000', 1818111111111111116, 'zpbg_bggn', '关联备查材料', '关联备查材料', 3, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111120, '000000', 1818111111111111116, 'zpbg_bggn', '专家意见', '专家意见', 4, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111121, '000000', 1818111111111111116, 'zpbg_bggn', '智慧检测', '智慧检测', 5, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111122, '000000', 1818111111111111116, 'zpbg_bggn', '历史版本', '历史版本', 6, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111123, '000000', 1818111111111111116, 'zpbg_bggn', '提交记录', '提交记录', 7, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `xpaas_dict_biz`(`ID`, `ZHID`, `PARENT_ID`, `CODE`, `DICT_KEY`, `DICT_VALUE`, `SORT`, `REMARK`, `IS_SEALED`, `SCBJ`, `CJDW`, `DICT_TYPE`, `ANCESTORS`, `CJRQ`, `CJR`, `CJBM`, `GXR`, `GXRQ`, `ZT`, `MENU_ID`) VALUES (1818111111111111124, '000000', 1818111111111111116, 'zpbg_bggn', '某功能', '某功能', 8, NULL, 0, 0, NULL, 0, 'null,1818111111111111116', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


-- 添加批次管理的表格
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_pcgl`;
CREATE TABLE `t_dt_jxpj_zpbg_pcgl`  (
`ID` bigint(20) NOT NULL COMMENT '主键ID',
`ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
`CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
`CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
`CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
`CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
`GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
`GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
`SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
`ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
`PCMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
`ZSZT` tinyint(4) NULL DEFAULT 1 COMMENT '展示状态 1展示 2取消展示 默认展示',
PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-批次管理' ROW_FORMAT = Dynamic;


INSERT INTO xpaas_menu (ID,PARENT_ID,CODE,NAME,ALIAS,NO_MENU,`PATH`,SOURCE,SORT,CATEGORY,`ACTION`,IS_OPEN,REMARK,SCBJ,IS_THIRD,TARGET,VISIBLE,IS_CACHE,IS_FRAME,ZT,YYLY,YYBH,LB,SFWZXT,EXPLAINS,TEMPLATE,CJDW,IS_SHOW,ANCESTORS,PARAM_TYPE,PARAM_VALUE) VALUES
                                                                                                                                                                                                                                                            (1873896682127142913,1798219302920359937,'pcgl','批次管理','',0,'/pcgl/pcgl','iconfont iconicon_work',10,1,0,1,'',0,'0','','0',0,'',NULL,'','',2,0,'','',-1,1,'0,1798219302920359937',NULL,'[]'),
                                                                                                                                                                                                                                                            (1874662100320169986,1873896682127142913,'zpbg_pcgl_add','新增',NULL,0,'','',1,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1873896682127142913',NULL,'[]'),
                                                                                                                                                                                                                                                            (1874662165533208578,1873896682127142913,'zpbg_pcgl_edit','编辑',NULL,0,'','',2,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1873896682127142913',NULL,'[]'),
                                                                                                                                                                                                                                                            (1874662243949916162,1873896682127142913,'zpbg_pcgl_delete','删除','',0,'','',3,2,0,1,'',0,'0','','0',0,'',NULL,'','',2,0,'','',-1,-1,'0,1798219302920359937,1873896682127142913',NULL,'[]'),
                                                                                                                                                                                                                                                            (1874662425307426818,1873896682127142913,'zpbg_pcgl_zs','是否展示',NULL,0,'','',4,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1873896682127142913',NULL,'[]'),
                                                                                                                                                                                                                                                            (1874662617414938625,1873896682127142913,'zpbg_pcgl_fz','复制',NULL,0,'','',5,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1873896682127142913',NULL,'[]');

-- 初始化报告、佐证材料、支撑材料老数据批次id
update t_dt_jxpj_zpbg_bggl set PCID=数据库中批次id;
update t_dt_jxpj_zpbg_zzcl set PC=数据库中批次id;
update t_dt_jxpj_zpbg_zccl set PC=数据库中批次id;



--- 报告类型默认值
update t_dt_jxpj_zpbg_bggl set BGLX=MKLX;




