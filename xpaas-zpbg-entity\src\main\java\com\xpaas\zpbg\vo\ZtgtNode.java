package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Bgmk;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ZtgtNode {
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String name;

    private String zygzdfzr;

    private String zrdw;

    private boolean fzmk;

    private List<ZtgtBar> bars = new ArrayList<>();

    @JsonIgnore
    private Bgmk bgmk;
}
