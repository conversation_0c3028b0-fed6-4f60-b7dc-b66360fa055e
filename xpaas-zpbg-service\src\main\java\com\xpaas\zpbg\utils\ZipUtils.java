package com.xpaas.zpbg.utils;

import com.alibaba.fastjson.JSON;
import com.xpaas.core.oss.model.XpaasFile;
import com.xpaas.core.tool.api.R;
import com.xpaas.resource.feign.IOssClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2023/09/12 14:35
 **/
@Slf4j
public class ZipUtils {

    public static String filesToZipUpload(List<Map<String, Object>> filesList,IOssClient client) throws IOException {
        String zipName = new Date().getTime()+".zip";
        File zipFile = new File(zipName);
        FileOutputStream fos = new FileOutputStream(zipFile);
        ZipOutputStream zipOut = new ZipOutputStream(fos);

        log.info("开始打包");

        for (Map<String,Object> fileMap : filesList) {
            String fileUrl = String.valueOf(fileMap.get("url"));
            String fileName = String.valueOf(fileMap.get("name"));
            log.info("fileUrl={},fileName={}", fileUrl, fileName);
            boolean ret = addToZip(fileUrl, fileName, zipOut,client);
            if(!ret){
                zipOut.close();
                fos.close();
                if(zipFile.exists()){
                    zipFile.delete();
                }
                log.info("取得文件流返回");
                return "";
            }
        }

        zipOut.close();
        fos.close();

        log.info("完成打包");

        // 通过输出流生成输入流
        FileInputStream inputStream = new FileInputStream(zipFile);

        MultipartFile multipartFile = new MockMultipartFile("file", UUID.randomUUID().toString()+".zip", null, inputStream);

        R<XpaasFile> res = client.putFile(multipartFile);
        if(zipFile.exists()){
            log.info("删除压缩包");
            zipFile.delete();
        }
        if(res.isSuccess()){
            log.info("上传成功");
            return res.getData().getName();
        }else{
            log.info("上传失败");
            return "";
        }
    }

    private static boolean addToZip(String fileUrl, String fileName, ZipOutputStream zipOut, IOssClient client) throws IOException {
        ZipEntry zipEntry = new ZipEntry(fileName);
        zipOut.putNextEntry(zipEntry);

        log.info("取得文件流" + fileUrl);
        try {
            R<byte[]> inputR = client.getFileBuffer(fileUrl,"minio11");
            if (inputR.isSuccess()) {
                log.info("取得文件流成功");
                byte[] bytes = inputR.getData();
                log.info("附件长度:{}", bytes.length);
                zipOut.write(bytes, 0, bytes.length);
            }else{
                log.info("取得文件流失败");
                log.info(JSON.toJSONString(inputR));
                return false;
            }
        }
        catch (Exception exception){
            exception.printStackTrace();
        }
        return true;

    }


}
