package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bggl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-报告管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BgglVO对象", description = "教学评价-自评报告-报告管理")
public class BgglVO extends Bggl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;


    @ApiModelProperty(value = "报告下的模块信息")
    private List<BgmkVO> bgmkVOS;

}
