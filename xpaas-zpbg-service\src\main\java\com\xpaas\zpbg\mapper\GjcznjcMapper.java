package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Gjcznjc;
import com.xpaas.zpbg.vo.GjcznjcVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-关键词智能检测 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Repository
public interface GjcznjcMapper extends BaseMapper<Gjcznjc> {

	/**
	 * 智能检测-查询
	 *
	 * @param gjcznjc
	 * @return
	 */
	List<GjcznjcVO> selectGjcznjc(GjcznjcVO gjcznjc);

	/**
	 * 智能检测-删除
	 *
	 * @param gjcznjc
	 * @return
	 */
	boolean removeGjcznjc(Gjcznjc gjcznjc);

}
