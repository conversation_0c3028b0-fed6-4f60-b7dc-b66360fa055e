package com.xpaas.zpbg.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/7/2 14:58
 */
@Data
@ApiModel(value = "DownloadFilesVO对象", description = "自评报告-文件下载")
public class DownloadFilesVO {

    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgid;

    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgmkid;

    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbid;
}
