<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BbpzMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bbpzResultMap" type="com.xpaas.zpbg.entity.Bbpz">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BBID" property="bbid"/>
        <result column="COMMENT_ID" property="commentId"/>
        <result column="PX" property="px"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bbpzResultMapVO" type="com.xpaas.zpbg.vo.BbpzVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BBID" property="bbid"/>
        <result column="COMMENT_ID" property="commentId"/>
        <result column="COMMENT_TEXT" property="commentText"/>
        <result column="PX" property="px"/>
    </resultMap>

    <!-- 删除版本批注 -->
    <update id="deleteByBbId">
        update T_DT_JXPJ_ZPBG_BBPZ set scbj = 1 where bbid = #{_parameter}
    </update>

    <!-- 列表 -->
    <select id="selectBbpzPage" resultMap="bbpzResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BBPZ where scbj = 0
    </select>

    <!-- 加锁版本 -->
    <update id="lockBb" parameterType="string">
        update t_dt_jxpj_zpbg_bbgl
        set id = id
        where id = #{bgId}
          and scbj = 0
    </update>
</mapper>
