package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.tool.api.R;
import com.xpaas.system.cache.DeptCache;
import com.xpaas.user.entity.User;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.client.IZjjyClient;
import com.xpaas.zpbg.entity.Syzj;
import com.xpaas.zpbg.service.IZtglService;
import com.xpaas.zpbg.vo.ZtProcInfoVO;
import com.xpaas.zpbg.vo.ZtProcType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 教学评价-自评报告-状态管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/ztgl")
@Api(value = "教学评价-自评报告-状态管理", tags = "教学评价-自评报告-状态管理接口")
public class ZtglController extends BaseController {

    private IZtglService ztglService;
    private IUserClient userClient;
    private IZjjyClient zjjyClient;

    /**
     * 新增 教学评价-自评报告-状态管理-撰写开始
     */
    @PostMapping("/zxks")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "新增", notes = "传入撰写开始信息")
    @ApiLog("状态管理-撰写开始")
    public R zxks(@RequestBody(required = false) ZtProcInfoVO procInfo) {
        if (procInfo == null || procInfo.getBgid() == null || procInfo.getBbid() == null) {
            log.error("未指定报告ID或版本ID");
            return R.data(procInfo);
        }
        try {
            ztglService.whileProc(ZtProcType.ZXKS10, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        } catch (Exception e) {
            log.info("重复撰写开始警告", e);
        }
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-点长提交
     */
    @PostMapping("/dztj")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "新增", notes = "传入点长提交信息")
    @ApiLog("状态管理-点长提交")
    public R dztj(@RequestBody ZtProcInfoVO procInfo) {
         List<Syzj> syzjList = procInfo.getSyzjList();
         if (syzjList != null) {
             for (Syzj syzj : syzjList) {
                 Long zjid = syzj.getZjid();
                 User user = userClient.userInfoById(zjid).getData();
                 if (user == null) {
                     throw new ServiceException("指定的专家不存在");
                 }
                 syzj.setZjxm(user.getName());
                 syzj.setZjdwid(Long.parseLong(user.getDeptId()));
                 syzj.setZjdwmc(DeptCache.getDeptName(Long.parseLong(user.getDeptId())));
             }
         }
        ztglService.whileProc(ZtProcType.DZTJ20, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-审阅开始
     */
    @PostMapping("/syks")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "新增", notes = "传入审阅开始信息")
    @ApiLog("状态管理-审阅开始")
    public R syks(@RequestBody ZtProcInfoVO procInfo) {
        try {
            ztglService.whileProc(ZtProcType.SYKS30, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        } catch (Exception e) {
            log.info("重复审阅开始警告", e);
        }
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-审阅提交
     */
    @PostMapping("/sytj")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入审阅提交信息")
    @ApiLog("状态管理-审阅提交")
    public R sytj(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.SYTJ40, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-审阅完成
     */
    @PostMapping("/sywc")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "新增", notes = "传入审阅完成信息")
    @ApiLog("状态管理-审阅完成")
    public R sywc(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.SYWC50, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-点长定稿
     */
    @PostMapping("/dzdg")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增", notes = "传入点长定稿信息")
    @ApiLog("状态管理-点长定稿")
    public R dzdg(@RequestBody ZtProcInfoVO procInfo) {
        return R.data(ztglService.whileProc(ZtProcType.DZDG70, procInfo.getBgid(), procInfo.getBbid(), procInfo));
    }

    /**
     * 新增 教学评价-自评报告-状态管理-撰写暂存
     */
    @PostMapping("/zxzc")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "新增", notes = "传入撰写暂存信息")
    @ApiLog("状态管理-撰写暂存")
    public R zxzc(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.ZXZC91, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-撰写提交
     */
    @PostMapping("/zxtj")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "新增", notes = "传入撰写提交信息")
    @ApiLog("状态管理-撰写提交")
    public R zxtj(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.ZXZC92, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-撰写编辑
     */
    @PostMapping("/zxbj")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "新增", notes = "传入撰写编辑信息")
    @ApiLog("状态管理-撰写编辑")
    public R zxbj(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.ZXZC93, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-点长撤回
     */
    @PostMapping("/dzch")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "新增", notes = "传入点长撤回信息")
    @ApiLog("状态管理-点长撤回")
    public R dzch(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.DZCH94, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理-取消定稿
     */
    @PostMapping("/qxdgBb")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "新增", notes = "传入取消定稿信息")
    @ApiLog("状态管理-取消定稿")
    public R qxdgBb(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.whileProc(ZtProcType.QXDG95, procInfo.getBgid(), procInfo.getBbid(), procInfo);
        return R.data(procInfo);
    }


    /**
     * 新增 教学评价-自评报告-状态管理-取消定稿
     */
    @PostMapping("/qxdg")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "新增", notes = "传入取消定稿信息")
    @ApiLog("状态管理-取消定稿")
    public R qxdg(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.qxdg(procInfo.getBgid());
//        zjjyClient.deleteByBgid(procInfo.getBgid());//之前取消定稿同时删除专家进院信息，现在不删除了

        return R.data(procInfo);
    }

    /**
     * 新增 教学评价-自评报告-状态管理--单个版本-取消定稿
     */
    @PostMapping("/qxdgD")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "取消定稿单体", notes = "传入取消定稿信息")
    @ApiLog("状态管理-单个版本-取消定稿")
    public R qxdgD(@RequestBody ZtProcInfoVO procInfo) {
        ztglService.qxdgD(procInfo.getBgid(),procInfo.getBbid());
//        zjjyClient.deleteByBgid(procInfo.getBgid());//之前取消定稿同时删除专家进院信息，现在不删除了

        return R.data(procInfo);
    }
    /**
     * 查询 教学评价-自评报告-状态管理-根据专家意见数检查是否可定稿
     */
    @PostMapping("/selectZjyjCnt")
    @ApiOperationSupport(order = 100)
    @ApiOperation(value = "根据专家意见数检查是否可定稿", notes = "传入查询信息")
    @ApiLog("状态管理-根据专家意见数检查是否可定稿")
    public R selectZjyjCnt(@RequestBody ZtProcInfoVO procInfo) {
        Integer zjyjcnt = ztglService.selectZjyjCnt(procInfo.getBbid());
        return R.data(zjyjcnt);
    }

//    ////////////////////////////////////////////
//    // 测试用
//    ////////////////////////////////////////////
//
//    /**
//     * 更新 测试用
//     */
//    @PostMapping("/procTest")
//    @ApiOperationSupport(order = 13)
//    @ApiOperation(value = "测试", notes = "测试")
//    public R procTest(@RequestBody Map<String, Object> param) {
//        boolean b = true;
//        Long bgid = 1805552371828686849L;
//        Long bbid = 1805552372206174220L;
//        ztglService.whileProc(ZtProcType.ZXKS10,
//                bgid,
//                bbid,
//                null);
//
//        ztglService.whileProc(ZtProcType.DZTJ20,
//                bgid,
//                bbid,
//                null);
//
//        ztglService.whileProc(ZtProcType.SYKS30,
//                bgid,
//                bbid,
//                null);
//
//        ztglService.whileProc(ZtProcType.SYTJ40,
//                bgid,
//                bbid,
//                null);
//
//        ztglService.whileProc(ZtProcType.SYWC50,
//                bgid,
//                bbid,
//                null);
//
//        ztglService.whileProc(ZtProcType.DZDG70,
//                bgid,
//                bbid,
//                null);
//
//
//        return R.status(b);
//    }
}
