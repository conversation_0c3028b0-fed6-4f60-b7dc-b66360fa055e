package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-版本文件内容实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BBWJNR")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bbwjnr对象", description = "教学评价-自评报告-版本文件内容")
public class Bbwjnr extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 文件内容
	*/
	@ExcelProperty("文件内容")
	@ApiModelProperty(value = "文件内容")
	@TableField("WJNR")
	private String wjnr;
}
