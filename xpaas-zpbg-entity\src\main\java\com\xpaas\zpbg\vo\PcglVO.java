package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Pcgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-批次管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PcglVO对象", description = "教学评价-自评报告-批次管理")
public class PcglVO extends Pcgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    /**
     * 关联报告名称
     */
    @ApiModelProperty(value = "关联报告名称")
    private String glbgmc;

}
