package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.zpbg.dto.TreeBuilder;
import com.xpaas.zpbg.dto.TreeData;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zygzdgl;
import com.xpaas.zpbg.mapper.MkglMapper;
import com.xpaas.zpbg.service.IMkglService;
import com.xpaas.zpbg.vo.MkglVO;
import com.xpaas.zpbg.vo.TreeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-模块管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@Service
public class MkglServiceImpl extends BaseServiceImpl<MkglMapper, Mkgl> implements IMkglService {

	@Override
	public IPage<MkglVO> selectMkglPage(IPage<MkglVO> page, MkglVO mkgl, TreeVO tree) {
		List<MkglVO> mkglList = baseMapper.selectMkglPage(page, mkgl, tree);
		return page.setRecords(mkglList);
	}

	@Override
	public List<TreeData> bztxOptions(String mklxid) {
		List<TreeData> data = baseMapper.bztxOptions(mklxid);
		TreeBuilder<TreeData> treeBuilder = new TreeBuilder<TreeData>();
		List<TreeData> make = treeBuilder.make(data);

		// 删除空节点
		deleteNoChildren(make);
		// 删除三级
		deleteThreeChildren(make);

		return make;
	}

	private void deleteNoChildren(List<TreeData> make) {
		if(make == null || make.size() == 0) {
			return;
		}
		for(int i = 0; i < make.size(); i++) {
			if("3".equals(make.get(i).getCj())||"2".equals(make.get(i).getCj())){
				return;
			}

			// 递归调用，删除没有子节点的数据
			deleteNoChildren(make.get(i).getChildren());

			if(make.get(i).getChildren() == null || make.get(i).getChildren().size() == 0) {
				make.remove(i);
				i--;
			}
		}
	}

	private void deleteThreeChildren(List<TreeData> make) {
		if(make == null || make.size() == 0) {
			return;
		}
		for(int i = 0; i < make.size(); i++) {
			// 递归调用，先判断子节点，3级不用判断
			if("2".equals(make.get(i).getCj())){
				make.get(i).setChildren(null);
			} else {
				deleteThreeChildren(make.get(i).getChildren());
			}
		}
	}

	@Override
	public String getBgmc(String mkid) {
		LoginUser user = AuthUtil.getUser();
		String ryid = user.getUserId().toString();
		return baseMapper.getBgmc(mkid, ryid);
	}

	@Override
	public MkglVO getSaveMkgl(Long zygzdid) {
		return baseMapper.getSaveMkgl(zygzdid);
	}

	@Override
	public boolean saveMkgl(MkglVO mkglVO) {
		return baseMapper.saveMkgl(mkglVO);
	}

	@Override
	public boolean updateMkgl(Zygzdgl zygzdgl) {
		return baseMapper.updateMkgl(zygzdgl);
	}

	@Override
	public List<Mkgl> getSaveMkglZd() {
		return baseMapper.getSaveMkglZd();
	}

	@Override
	public boolean updateMkglZd() {
		boolean mkmc = baseMapper.mkglUpdateMkmc();
		boolean yjzb = baseMapper.mkglUpdateYjzb();
		boolean ejzb = baseMapper.mkglUpdateEjzb();
		return mkmc || yjzb || ejzb;
	}
}
