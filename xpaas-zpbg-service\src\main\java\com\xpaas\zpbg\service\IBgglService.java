package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.vo.BgglVO;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-报告管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IBgglService extends BaseService<Bggl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bggl
	 * @return
	 */
	IPage<BgglVO> selectBgglPage(IPage<BgglVO> page, BgglVO bggl);

	/**
	 * 查询报告进度信息
	 *
	 * @param bggl
	 * @return
	 */
	Map<String, Object> selectBgjd(Bggl bggl);

	/**
	 * 新建报告管理、版本管理和报告模块
	 *
	 * @param bgglVO
	 * @return
	 */
	boolean saveBgAndMk(BgglVO bgglVO);

	/**
	 * 报告查询
	 *
	 * @param bgglVO
	 * @return
	 */
	List<BgglVO> searchBggl(BgglVO bgglVO);

	/**
	 * 同步报告管理、版本管理和报告模块
	 */
	void syncBgAndMk(Long bgglId);

}
