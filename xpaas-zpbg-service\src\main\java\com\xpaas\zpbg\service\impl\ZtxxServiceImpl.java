package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.entity.Jdmkgl;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.entity.Syzj;
import com.xpaas.zpbg.mapper.*;
import com.xpaas.zpbg.service.IZtxxService;
import com.xpaas.zpbg.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * 教学评价-自评报告-状态信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class ZtxxServiceImpl implements IZtxxService {

    @Autowired
    BgglMapper bgglMapper;
    @Autowired
    BbglMapper bbglMapper;
    @Autowired
    BgmkMapper bgmkMapper;
    @Autowired
    JdglMapper jdglMapper;
    @Autowired
    JdmkglMapper jdmkglMapper;
    @Autowired
    RwfgMapper rwfgMapper;
    @Autowired
    SyzjMapper syzjMapper;
    @Autowired
    BgmkztjlMapper bgmkztjlMapper;


    /**
     * 获取状态信息
     */
    @Override
    public ZtxxResultVO ztxx(ZtxxParamVO param) {

        // 参数获取
        Long bbid = param.getBbid();
        if (bbid == null || bbid < 0) {
            throw new ServiceException("未指定版本");
        }
        ZtxxRoleType roleReq = param.getRole();

        // 数据获取
        ZtxxContext ctx = new ZtxxContext();
        initContext(ctx, bbid);

        // 返回数据定义
        ZtxxModeType mode = ZtxxModeType.view;
        ZtxxRoleType role = ZtxxRoleType.zxr; // 非法

        // 分角色判断
        if (roleReq == ZtxxRoleType.gly) {
            // 教评管理员
            // 管理员啥都能看，看啥都是只读
            mode = ZtxxModeType.view;
            role = ZtxxRoleType.gly;
        } else if (roleReq == ZtxxRoleType.zj) {
            // 专家
            if (ctx.syzj == null) {
                // 非该版本的相关专家
                mode = ZtxxModeType.view;
                role = ZtxxRoleType.zxr; // 非法
            } else {
                // 该版本的相关专家
                if (ctx.mainBb) {
                    // 该版本的相关专家.主线版本
                    if (ctx.mainActiveBb) {
                        // 该版本的相关专家.主线版本.且版本活跃中
                        if (ctx.bzjSyz) {
                            // 该版本的相关专家.主线版本.且版本活跃中.本人审阅中
                            mode = ZtxxModeType.audit;
                            role = ZtxxRoleType.zj;
                        } else if (ctx.bzjSy) {
                            // 该版本的相关专家.主线版本.且版本活跃中.审阅阶段且本人负责审阅
                            mode = ZtxxModeType.view;
                            role = ZtxxRoleType.zj;
                        } else {
                            // 该版本的相关专家.主线版本.且版本活跃中.非审阅阶段或非本人负责审阅
                            mode = ZtxxModeType.view;
                            role = ZtxxRoleType.zxr;
                        }
                    } else {
                        // 该版本的相关专家.主线版本.且非版本活跃中
                        mode = ZtxxModeType.view;
                        role = ZtxxRoleType.zxr; // 非法
                    }
                } else {
                    // 该版本的相关专家.历史版本
                    if (ctx.zjBb) {
                        // 该版本的相关专家.历史版本.专家所属
                        mode = ZtxxModeType.view;
                        role = ZtxxRoleType.zj;
                    } else {
                        // 该版本的相关专家.历史版本.撰写所属
                        mode = ZtxxModeType.view;
                        role = ZtxxRoleType.zxr; // 非法
                    }
                }
            }
        } else if (roleReq == ZtxxRoleType.zxrOrDz) {
            // 撰写人或点长
            boolean needCheckMode = true; // 模式

            // 分工检查
            if (ctx.rwfgList != null && ctx.rwfgList.stream().anyMatch(item ->
                    item.getRyid().equals(ctx.userId) && item.getFglx().equals(1)
            )) {
                // 点长
                role = ZtxxRoleType.dz;
            } else if (ctx.rwfgList != null && ctx.rwfgList.stream().anyMatch(item ->
                    item.getRyid().equals(ctx.userId) && item.getFglx().equals(2)
            )) {
                // 撰写人
                role = ZtxxRoleType.zxr;
            } else {
                // 无关人员
                mode = ZtxxModeType.view;
                role = ZtxxRoleType.zxr;
                // 不必再判断模式
                needCheckMode = false;
            }

            // 活跃模块检查
            if (needCheckMode) {
                if (!ctx.mainActiveBb) {
                    // 版本非活跃中
                    mode = ZtxxModeType.view;
                    // 不必再判断模式
                    needCheckMode = false;
                }
            }

            // 版本状态检查
            if (needCheckMode) {
                if (!(ctx.procStatus == ZtProcStatus.WKS0 || // 0:未开始
                        ctx.procStatus == ZtProcStatus.ZXZ10 || // 10:撰写中
                        ctx.procStatus == ZtProcStatus.YYQ20 || // 20:已延期
                        ctx.procStatus == ZtProcStatus.DXG60 ||// 60:待修改
                        (ctx.procStatus == ZtProcStatus.YWC80 && role == ZtxxRoleType.dz) // 80:已完成且是点长
                )) {
                    // 状态非编辑中
                    mode = ZtxxModeType.view;
                    // 不必再判断模式
                    needCheckMode = false;
                }
            }

            // 提交次数
            if (needCheckMode) {
                if (ctx.tjcs > 0) {
                    mode = ZtxxModeType.edit;
                } else {
                    mode = ZtxxModeType.write;
                }
            }
        } else {
            mode = ZtxxModeType.view;
            role = ZtxxRoleType.zxr; // 非法
        }

        // 返回结果
        ZtxxResultVO result = new ZtxxResultVO();
        result.setBbid(bbid);
        result.setRole(role);
        result.setMode(mode);
        result.setCtx(ctx);

        return result;
    }

    private void initContext(ZtxxContext ctx, Long bbid) {
        // 参数存储
        ctx.bbid = bbid;

        // 操作者信息
        LoginUser user = AuthUtil.getUser();
        if (user != null) {
            ctx.userId = user.getUserId();
            ctx.userName = user.getUserName();
        }

        // 版本信息
        ctx.bbgl = bbglMapper.selectById(bbid);
        if (ctx.bbgl == null) {
            throw new ServiceException("指定的版本不存在");
        }
        // 版本信息详情
        Integer bgmkzt = ctx.bbgl.getBgmkzt();
        if (bgmkzt == null) {
            bgmkzt = ZtProcStatus.WKS0.getValue();
        }
        ctx.procStatus = ZtProcStatus.fromValue(bgmkzt);
        if (ctx.bbgl.getBblx() == 1) {
            ctx.mainBb = true; // 主线版本
        } else if (ctx.bbgl.getBblx() == 2) {
            // 正式版本
            if (Func.isNotEmpty(ctx.bbgl.getSsjs()) && 2 == ctx.bbgl.getSsjs()) {
                ctx.zjBb = true; // 专家版本
            }
        } else {
            // 暂存版本
        }

        // 报告信息
        ctx.bgid = ctx.bbgl.getBgid();
        long t0 = new Date().getTime();
        ctx.bggl = bgglMapper.selectById(ctx.bgid);
        log.info("############ WP ZTXX.BGGL：" + (new Date().getTime() - t0));
        if (ctx.bggl == null) {
            throw new ServiceException("报告不存在");
        }

        // 报告模块信息
        ctx.bgmkid = ctx.bbgl.getBgmkid();
        ctx.bgmk = bgmkMapper.selectById(ctx.bgmkid);
        if (ctx.bgmk == null) {
            throw new ServiceException("报告模块不存在");
        }

        // 进度信息
        ctx.jdglid = ctx.bbgl.getJdglid();
        if (ctx.jdglid != null) {
            ctx.jdgl = jdglMapper.selectById(ctx.jdglid);
            if (ctx.jdgl != null) {
                // 进度详情
                ctx.kssj = ctx.jdgl.getKssj();
                ctx.zxjssj = nextDay(ctx.jdgl.getZxjssj());
                ctx.syjssj = nextDay(ctx.jdgl.getSyjssj());

                // 报告模块进度关联列表
                LambdaQueryWrapper<Jdmkgl> jdmkglWrapper = new LambdaQueryWrapper<>();
                jdmkglWrapper.eq(Jdmkgl::getJdglid, ctx.jdglid);
                ctx.jdmkglList = jdmkglMapper.selectList(jdmkglWrapper);

                if (ctx.mainBb) {
                    Date now = new Date();
                    if (now.compareTo(ctx.kssj) >= 0 && now.compareTo(ctx.syjssj) < 0 // 当前进度
                            && ctx.jdmkglList != null && !ctx.jdmkglList.isEmpty()
                            && ctx.jdmkglList.stream().anyMatch(item -> item.getBgmkid().equals(ctx.bgmkid)) // 当前进度关联了此模块
                    ) {
                        ctx.mainActiveBb = true;
                    }
                }
            }
        }

        // 分工信息
        LambdaQueryWrapper<Rwfg> rwfgWrapper = new LambdaQueryWrapper<>();
        rwfgWrapper.eq(Rwfg::getBgmkid, ctx.bgmkid);
        ctx.rwfgList = rwfgMapper.selectList(rwfgWrapper);

        // 专家信息
        LambdaQueryWrapper<Syzj> syzjWrapper = new LambdaQueryWrapper<>();
        syzjWrapper.eq(Syzj::getBbid, ctx.bbid);
        ctx.syzjList = syzjMapper.selectList(syzjWrapper);

        // 专家遍历
        if (ctx.syzjList != null) {
            for (Syzj item : ctx.syzjList) {
                // 状态初始化
                if (item.getSyzt() == null) {
                    item.setSyzt(1);
                }

                // 查找自己
                if (item.getZjid().equals(ctx.userId)) {
                    ctx.syzj = item;
                    if (ctx.procStatus == ZtProcStatus.YTJ30 ||
                            ctx.procStatus == ZtProcStatus.SYZ40 ||
                            ctx.procStatus == ZtProcStatus.DXG60 ||
                            ctx.procStatus == ZtProcStatus.YWC80) { // 版本 已提交(待审阅)、审阅中、待修改(已审阅)、已完成
                        ctx.bzjSy = true;
                        if (item.getSyzt() < 3 &&  // 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
                                (ctx.procStatus == ZtProcStatus.YTJ30 || ctx.procStatus == ZtProcStatus.SYZ40) // 版本 待审阅或审阅中
                        ) {
                            ctx.bzjSyz = true;
                        }
                    }
                }
            }
        }

        // 操作记录
        LambdaQueryWrapper<Bgmkztjl> ztjlWrapper = new LambdaQueryWrapper<>();
        ztjlWrapper
                .eq(Bgmkztjl::getBgmkid, ctx.bgmkid)
                .orderByAsc(Bgmkztjl::getCjrq);
        ctx.ztjlList = bgmkztjlMapper.selectList(ztjlWrapper);
        if (ctx.ztjlList != null) {
            for (Bgmkztjl item : ctx.ztjlList) {
                String czlx = item.getCzlx();
                if ("20".equals(czlx)) {
                    ctx.tjcs++;
                }
            }
        }

    }

    // 时间改为次日凌晨0点
    private Date nextDay(Date day) {
        if (day == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(day);

        // 将时间设置为次日凌晨零点
        calendar.add(Calendar.DATE, 1); // 增加一天
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0

        return calendar.getTime();
    }
}
