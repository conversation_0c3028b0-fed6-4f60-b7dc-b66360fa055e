<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.DownloadFilesMapper">

    <!-- 获取文件列表 -->
    <select id="getFileUrlList" resultType="map">
        SELECT
        t.wjlj fileAddr,
        IFNULL(NULLIF(t.cmm, ''), t.clmc) fileTitle
        FROM t_dt_jxpj_zpbg_zzclgl t
        INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.id = t.bbid
        INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = t.bgmkid
        INNER JOIN t_dt_jxpj_zpbg_bggl bggl ON bggl.id = t.bgid
        INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on t.BBID = bbpz.BBID and t.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE t.scbj = 0
        AND bbgl.scbj = 0
        AND bgmk.scbj = 0
        AND bggl.scbj = 0
        <if test="bgid != null and bgid != ''">and t.bgid = #{bgid}</if>
        <if test="bgmkid != null and bgmkid != ''">and t.bgmkid = #{bgmkid}</if>
        <if test="bbid != null and bbid != ''">and t.bbid = #{bbid}</if>
        <if test="bbid == null or bbid &lt; 1 or bbid == ''">and bbgl.bblx = 1</if>

        UNION ALL

        SELECT
        t.wjlj fileAddr,
        t.clmc fileTitle
        FROM t_dt_jxpj_zpbg_zcclgl t
        INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.id = t.bbid
        INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = t.bgmkid
        INNER JOIN t_dt_jxpj_zpbg_bggl bggl ON bggl.id = t.bgid
        INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on t.BBID = bbpz.BBID and t.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE t.scbj = 0 AND t.wjlj != '' and t.wjlj IS NOT	null
        AND bbgl.scbj = 0
        AND bgmk.scbj = 0
        AND bggl.scbj = 0
        <if test="bgid != null and bgid != ''">and t.bgid = #{bgid}</if>
        <if test="bgmkid != null and bgmkid != ''">and t.bgmkid = #{bgmkid}</if>
        <if test="bbid != null and bbid != ''">and t.bbid = #{bbid}</if>
        <if test="bbid == null or bbid &lt; 1 or bbid == ''">and bbgl.bblx = 1</if>

<!--        UNION ALL-->
<!--        SELECT-->
<!--        sjcl.cldz fileAddr,-->
<!--        t.sjbmc fileTitle-->
<!--        FROM t_dt_jxpj_zpbg_sjbgl t-->
<!--        INNER JOIN t_dt_jxpj_zpbg_sjcl sjcl ON sjcl.id = t.sjclid-->
<!--        INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.id = t.bbid-->
<!--        INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = t.bgmkid-->
<!--        INNER JOIN t_dt_jxpj_zpbg_bggl bggl ON bggl.id = t.bgid-->
<!--        WHERE t.sjly = 2-->
<!--        AND t.scbj = 0-->
<!--        AND sjcl.scbj = 0-->
<!--        AND bbgl.scbj = 0-->
<!--        AND bgmk.scbj = 0-->
<!--        AND bggl.scbj = 0-->
<!--        <if test="bgid != null and bgid != ''">and t.bgid = #{bgid}</if>-->
<!--        <if test="bgmkid != null and bgmkid != ''">and t.bgmkid = #{bgmkid}</if>-->
<!--        <if test="bbid != null and bbid != ''">and t.bbid = #{bbid}</if>-->
<!--        <if test="bbid == null or bbid &lt; 1 or bbid == ''">and bbgl.bblx = 1</if>-->
    </select>

</mapper>
