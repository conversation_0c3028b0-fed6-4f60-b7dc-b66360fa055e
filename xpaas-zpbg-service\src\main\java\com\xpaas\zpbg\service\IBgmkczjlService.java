package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bgmkczjl;
import com.xpaas.zpbg.vo.BgmkczjlVO;

/**
 * 教学评价-自评报告-报告模块操作记录 服务类
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface IBgmkczjlService extends BaseService<Bgmkczjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmkczjl
	 * @return
	 */
	IPage<BgmkczjlVO> selectBgmkczjlPage(IPage<BgmkczjlVO> page, BgmkczjlVO bgmkczjl);

}
