package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-关键词管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_GJCGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Gjcgl对象", description = "教学评价-自评报告-关键词管理")
public class Gjcgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 关键词名称
	*/
	@ExcelProperty("关键词名称")
	@ApiModelProperty(value = "关键词名称")
	@TableField("GJCMC")
	private String gjcmc;

	/**
	* 关键词类型
	*/
	@ExcelProperty("关键词类型")
	@ApiModelProperty(value = "关键词类型")
	@TableField("GJCLX")
	private Integer gjclx;

	/**
	* 关联范围
	*/
	@ExcelProperty("关联范围")
	@ApiModelProperty(value = "关联范围")
	@TableField("GLFW")
	private Integer glfw;

	/**
	* 频次
	*/
	@ExcelProperty("频次")
	@ApiModelProperty(value = "频次")
	@TableField("PC")
	private Integer pc;

	/**
	 * 指标来源
	 */
	@ExcelProperty("指标来源")
	@ApiModelProperty(value = "指标来源")
	@TableField("ZBLY")
	private Integer zbly;



}
