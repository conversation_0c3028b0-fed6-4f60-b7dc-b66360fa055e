package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Rwry;
import com.xpaas.zpbg.vo.RwryVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-自评报告-任务人员包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Component
public class RwryWrapper extends BaseEntityWrapper<Rwry, RwryVO>  {


	@Override
	public RwryVO entityVO(Rwry rwry) {
		RwryVO rwryVO = Objects.requireNonNull(BeanUtil.copy(rwry, RwryVO.class));
		//User cjr = UserCache.getUser(rwfg.getCjr());
		//if (cjr != null){
		//	rwfgVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(rwfg.getGxr());
		//if (gxr != null){
		//	rwfgVO.setGxrName(gxr.getName());
		//}
		return rwryVO;
	}

    @Override
    public RwryVO wrapperVO(RwryVO rwryVO) {
		//User cjr = UserCache.getUser(rwfgVO.getCjr());
		//if (cjr != null){
		//	rwfgVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(rwfgVO.getGxr());
		//if (gxr != null){
		//	rwfgVO.setGxrName(gxr.getName());
		//}
        return rwryVO;
    }

}
