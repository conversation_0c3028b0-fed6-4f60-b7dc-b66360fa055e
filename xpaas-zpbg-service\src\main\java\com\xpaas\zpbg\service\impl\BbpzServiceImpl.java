package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.mapper.BbpzMapper;
import com.xpaas.zpbg.service.*;
import com.xpaas.zpbg.vo.BbpzGlCopyInfoVO;
import com.xpaas.zpbg.vo.BbpzGlCopyVO;
import com.xpaas.zpbg.vo.BbpzVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * 教学评价-自评报告-版本批注 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Slf4j
@Service
public class BbpzServiceImpl extends BaseServiceImpl<BbpzMapper, Bbpz> implements IBbpzService {

    @Autowired
    ISjzbglService sjzbglService;
    @Autowired
    ISjbglService sjbglService;
    @Autowired
    IZzclglService zzclglService;
    @Autowired
    IZcclglService zcclglService;
    @Autowired
    IZjyjService zjyjService;
    @Autowired
    BbpzMapper bbpzMapper;

    /**
     * 自定义分页
     */
    @Override
    public IPage<BbpzVO> selectBbpzPage(IPage<BbpzVO> page, BbpzVO bbpz) {
        return page.setRecords(baseMapper.selectBbpzPage(page, bbpz));
    }

    /**
     * 复制批注关联引用
     */
    @Override
    public boolean bbpzGlCopy(List<BbpzGlCopyVO> params) {
        // 参数校验
        if (params == null || params.isEmpty()) {
            return true;
        }

        // 复制源、目的字典
        Map<String, BbpzGlCopyInfoVO> copyMap = new LinkedHashMap<>();

        // 遍历参数处理
        for (BbpzGlCopyVO bbpzGlCopyVO : params) {

            // 参数校验
            if (bbpzGlCopyVO == null ||
                    bbpzGlCopyVO.getBbId() == null ||
                    bbpzGlCopyVO.getBbIdNew() == null ||
                    StringUtils.isBlank(bbpzGlCopyVO.getCommentId()) ||
                    StringUtils.isBlank(bbpzGlCopyVO.getCommentIdNew())
            ) {
                log.error("版本关联拷贝参数错误");
                continue;
            }

            // 维护复制源、目的字典
            String keySrc = makeBbpzKey(bbpzGlCopyVO.getBbId(), bbpzGlCopyVO.getCommentId());
            String keyTar = makeBbpzKey(bbpzGlCopyVO.getBbIdNew(), bbpzGlCopyVO.getCommentIdNew());
            BbpzGlCopyInfoVO src = copyMap.get(keySrc);
            if (src == null) {
                src = new BbpzGlCopyInfoVO();
                src.setTarMap(new LinkedHashMap<>());
                src.setBbId(bbpzGlCopyVO.getBbId());
                src.setCommentId(bbpzGlCopyVO.getCommentId());

                copyMap.put(keySrc, src);
            }
            BbpzGlCopyInfoVO tar = new BbpzGlCopyInfoVO();
            tar.setBbId(bbpzGlCopyVO.getBbIdNew());
            tar.setCommentId(bbpzGlCopyVO.getCommentIdNew());

            src.getTarMap().put(keyTar, tar);
        }

        if(copyMap.isEmpty()){
            log.error("版本关联拷贝空");
            return true;
        }

        // 专家意见.关联信息ID 数据转换定义
        Map<String, Map<Long, Long>> zjyjGlxxDic = new HashMap<>();

        // 【数据指标引用】
        processGlCopy(
                sjzbglService,
                Sjzbgl::getId,
                Sjzbgl::getBbid,
                Sjzbgl::getGlkey,
                Sjzbgl::setId,
                Sjzbgl::setZhid,
                Sjzbgl::setBbid,
                Sjzbgl::setGlkey,
                copyMap,
                zjyjGlxxDic,
                1
        );
        // 【数据表引用】
        processGlCopy(
                sjbglService,
                Sjbgl::getId,
                Sjbgl::getBbid,
                Sjbgl::getGlkey,
                Sjbgl::setId,
                Sjbgl::setZhid,
                Sjbgl::setBbid,
                Sjbgl::setGlkey,
                copyMap,
                zjyjGlxxDic,
                2
        );
        // 【佐证材料引用】
        processGlCopy(
                zzclglService,
                Zzclgl::getId,
                Zzclgl::getBbid,
                Zzclgl::getGlkey,
                Zzclgl::setId,
                Zzclgl::setZhid,
                Zzclgl::setBbid,
                Zzclgl::setGlkey,
                copyMap,
                zjyjGlxxDic,
                3
        );
        // 【备查材料引用】
        processGlCopy(
                zcclglService,
                Zcclgl::getId,
                Zcclgl::getBbid,
                Zcclgl::getGlkey,
                Zcclgl::setId,
                Zcclgl::setZhid,
                Zcclgl::setBbid,
                Zcclgl::setGlkey,
                copyMap,
                zjyjGlxxDic,
                4
        );
        // 【专家意见】这个要最后执行，需要填充zjyjGlxxDic数据
        processGlCopy(
                zjyjService,
                Zjyj::getId,
                Zjyj::getBbid,
                Zjyj::getYjkey,
                Zjyj::setId,
                Zjyj::setZhid,
                Zjyj::setBbid,
                Zjyj::setYjkey,
                copyMap,
                zjyjGlxxDic,
                0
        );

        return true;

    }

    /**
     * T数据关联引用复制
     */
    private <T> void processGlCopy(
            IService<T> service,
            SFunction<T, Long> getId,
            SFunction<T, Long> getBbId,
            SFunction<T, String> getGlkey,
            BiConsumer<T, Long> setId,
            BiConsumer<T, String> setZhid,
            BiConsumer<T, Long> setBbId,
            BiConsumer<T, String> setGlkey,
            Map<String, BbpzGlCopyInfoVO> copyMap,
            Map<String, Map<Long, Long>> zjyjGlxxDic,
            int glcllx // 0:专家意见，1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
    ) {
        // 查询原版本、批注相关数据 - 条件合成
        LambdaQueryWrapper<T> wrapper = new LambdaQueryWrapper<>();
        for (BbpzGlCopyInfoVO src : copyMap.values()) {
            wrapper.or(w -> w.eq(getBbId, src.getBbId())
                    .eq(getGlkey, src.getCommentId()));
        }

        // 查询原版本、批注相关数据 - 执行
        List<T> entityList = service.list(wrapper);
        if (entityList == null || entityList.isEmpty()) {
            return;
        }

        // 遍历结果
        for (T item : entityList) {
            // 获取老ID (专家意见不需要此数据)
            Long glxxidOld = getId.apply(item);

            // 获取目标批注
            String srcKey = makeBbpzKey(getBbId.apply(item), getGlkey.apply(item));
            BbpzGlCopyInfoVO src = copyMap.get(srcKey);
            if (src == null) {
                continue;
            }

            // 遍历目标批注
            for (BbpzGlCopyInfoVO tar : src.getTarMap().values()) {
                // 拷贝数据
                setZhid.accept(item, null);
                setId.accept(item, null);
                setBbId.accept(item, tar.getBbId());
                setGlkey.accept(item, tar.getCommentId());

                // 若是专家意见，挂接到新的数据材料关联
                if (glcllx == 0) {
                    fixZjyjGlxx(zjyjGlxxDic, (Zjyj) item);
                }

                service.save(item);

                // 记忆新旧关联ID关系，除了专家意见
                if (glcllx > 0) {
                    // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
                    addZjyjGlxx(zjyjGlxxDic, glcllx, tar.getCommentId(), glxxidOld, getId.apply(item));
                }
            }
        }
    }
//        上面代码的非泛型形式
//        LambdaQueryWrapper<Sjzbgl> sjzbglWrapper = new LambdaQueryWrapper<>();
//        for (BbpzGlCopyInfoVO src : copyMap.values()) {
//            sjzbglWrapper.or(wrapper -> wrapper.eq(Sjzbgl::getBbid, src.getBbId())
//                    .eq(Sjzbgl::getGlkey, src.getCommentId()));
//        }
//        List<Sjzbgl> sjzbglList = sjzbglService.list(sjzbglWrapper);
//        if (sjzbglList != null && !sjzbglList.isEmpty()) {
//            for (Sjzbgl item : sjzbglList) {
//                Long glxxidOld = item.getId();
//                String srcKey = makeBbpzKey(item.getBbid(), item.getGlkey());
//                BbpzGlCopyInfoVO src = copyMap.get(srcKey);
//                if (src == null) {
//                    continue;
//                }
//                for (BbpzGlCopyInfoVO tar : src.getTarMap().values()) {
//                    item.setZhid(null);
//                    item.setId(null);
//                    item.setBbid(tar.getBbId());
//                    item.setGlkey(tar.getCommentId());
//                    sjzbglService.save(item);
//                    addZjyjGlxx(zjyjGlxxDic, 1, tar.getCommentId(), glxxidOld, item.getId()); // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
//                }
//            }
//        }

    /**
     * 创建版本批注key
     */
    private String makeBbpzKey(Long bbId, String commentId) {
        return bbId + ":" + commentId;
    }

    // 记忆 专家意见 关联信息ID的旧新值
    private void addZjyjGlxx(Map<String, Map<Long, Long>> zjyjGlxxDic, Integer glcllx, String glkeyNew, Long idOld, Long idNew) {
        if (glcllx == null || glcllx < 1 || idOld == null || idOld < 0 || idNew == null || idNew < 0 || StringUtils.isBlank(glkeyNew)) {
            return;
        }
        String key = glcllx + ":" + glkeyNew;
        Map<Long, Long> valueMap = zjyjGlxxDic.computeIfAbsent(key, k -> new HashMap<>());
        valueMap.put(idOld, idNew);
    }

    // 获取 专家意见 关联信息ID的新值
    private void fixZjyjGlxx(Map<String, Map<Long, Long>> zjyjGlxxDic, Zjyj zjyj) {
        Integer glcllx = zjyj.getGlcllx();
        Long glxxid = zjyj.getGlxxid();
        String glkeyNew = zjyj.getYjkey();
        if (glcllx == null || glcllx < 1 || glxxid == null || glxxid < 0 || StringUtils.isBlank(glkeyNew)) {
            return;
        }
        String key = glcllx + ":" + glkeyNew;
        Map<Long, Long> valueMap = zjyjGlxxDic.get(key);
        if (valueMap == null) {
            return;
        }
        Long idNew = valueMap.get(glxxid);
        if (idNew != null) {
            zjyj.setGlxxid(idNew);
        }
    }
}
