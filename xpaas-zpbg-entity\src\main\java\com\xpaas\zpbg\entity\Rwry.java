package com.xpaas.zpbg.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-任务人员实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Rwry对象", description = "教学评价-自评报告-任务人员")
public class Rwry extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "报告id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	@ApiModelProperty(value = "报告名称")
	private String bgmc;

	@ApiModelProperty(value = "报告模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	@ApiModelProperty(value = "报告年度")
	private String nd;

	@ApiModelProperty(value = "开始时间")
	private Date kssj;

	@ApiModelProperty(value = "模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;

	@ApiModelProperty(value = "模块名称")
	private String mkmc;

	@ApiModelProperty(value = "分工类型")
	private Integer fglx;

	@ApiModelProperty(value = "单位ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long dwid;

	@ApiModelProperty(value = "单位名称")
	private String dwmc;

	@ApiModelProperty(value = "人员ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long ryid;

	@ApiModelProperty(value = "人员姓名")
	private String ryxm;
}
