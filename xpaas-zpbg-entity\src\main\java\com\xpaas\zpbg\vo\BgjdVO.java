package com.xpaas.zpbg.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-进度管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bgjd对象", description = "教学评价-自评报告-报告进度")
public class BgjdVO extends TenantEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报告id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "报告名称")
    private String bgmc;

    @ApiModelProperty(value = "报告年度")
    private String nd;

    @ApiModelProperty(value = "进度id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jdglid;

    @ApiModelProperty(value = "进度名称")
    private String jdmc;

    @ApiModelProperty(value = "开始时间")
    private Date kssj;

    @ApiModelProperty(value = "撰写结束时间")
    private Date zxjssj;

    @ApiModelProperty(value = "审阅结束时间")
    private Date syjssj;

    @ApiModelProperty(value = "任务分工数量")
    private Integer rwfgsl;

    @ApiModelProperty(value = "关联模块数量")
    private Integer glmksl;

    @ApiModelProperty(value = "是否展示显示FLG")
    private boolean sfzsShowFlg;

    @ApiModelProperty(value = "报告状态")
    private Integer bgzt;

    @ApiModelProperty(value = "模块类型")
    private Integer mklx;

    @ApiModelProperty(value = "演示报告")
    private Integer ysbg;


    @ApiModelProperty(value = "批次管理ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pcid;

    @ApiModelProperty(value = "批次管理名称")
    private String pcmc;

    @ExcelProperty("是否归档:1-是;0-否;")
    private Integer sfgd;

}
