package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-关键词关联模块实体类
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_GJCGLMK")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Gjcglmk对象", description = "教学评价-自评报告-关键词关联模块")
public class Gjcglmk extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 关键词ID
	*/
	@ExcelProperty("关键词ID")
	@ApiModelProperty(value = "关键词ID")
	@TableField("GJCID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long gjcid;

	/**
	* 模块ID
	*/
	@ExcelProperty("模块ID")
	@ApiModelProperty(value = "模块ID")
	@TableField("MKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;



}
