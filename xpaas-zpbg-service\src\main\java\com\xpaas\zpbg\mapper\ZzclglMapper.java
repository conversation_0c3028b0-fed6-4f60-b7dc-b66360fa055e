package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.vo.ZzclglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface ZzclglMapper extends BaseMapper<Zzclgl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param zzclgl
     * @return
     */
    List<ZzclglVO> selectZzclglPage(IPage page, ZzclglVO zzclgl);

    /**
     * 不同的内容引用了相同的佐证材料
     *
     * @param zzclgl
     * @return
     */
    List<ZzclglVO> sameGl(Zzclgl zzclgl);

    /**
     * 获取最大排序树数
     *
     *
     * @return
     */
    int getMaxPx();

    /**
     * 该材料是否引用了
     *
     * @param zzclgl
     * @return
     */
    List<ZzclglVO> getQuote(Zzclgl zzclgl);

    /**
     * 获取模块id
     *
     * @param bgmkid
     * @return
     */
    ZzclglVO getMkId(Long bgmkid);

    /**
     * 获取引用个数
     *
     * @param zzclglVO
     * @return
     */
    int checkGlkey(ZzclglVO zzclglVO);

    /**
     * 批注用
     *
     * @param bbid
     * @param glkeyList
     * @return
     */
    List<ZzclglVO> getzzclList(String bbid,  List<String> glkeyList);

    /**
     * 获取排序后的数据
     * @param zzclgl
     * @return
     */
    @SqlParser(filter=true)
    List<ZzclglVO> getZzlglList(Zzclgl zzclgl);

    /**
     * 更新排序顺序
     * @param zcid
     * @param newpx
     * @return
     */
    boolean updataPx(String zcid,String newpx);

}
