package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.vo.RwfgVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-任务分工包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Component
public class RwfgWrapper extends BaseEntityWrapper<Rwfg, RwfgVO>  {


	@Override
	public RwfgVO entityVO(Rwfg rwfg) {
		RwfgVO rwfgVO = Objects.requireNonNull(BeanUtil.copy(rwfg, RwfgVO.class));
		//User cjr = UserCache.getUser(rwfg.getCjr());
		//if (cjr != null){
		//	rwfgVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(rwfg.getGxr());
		//if (gxr != null){
		//	rwfgVO.setGxrName(gxr.getName());
		//}
		return rwfgVO;
	}

    @Override
    public RwfgVO wrapperVO(RwfgVO rwfgVO) {
		//User cjr = UserCache.getUser(rwfgVO.getCjr());
		//if (cjr != null){
		//	rwfgVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(rwfgVO.getGxr());
		//if (gxr != null){
		//	rwfgVO.setGxrName(gxr.getName());
		//}
        return rwfgVO;
    }

}
