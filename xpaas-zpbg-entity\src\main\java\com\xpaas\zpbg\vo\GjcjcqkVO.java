package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Gjcjcqk;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-关键词检测情况视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GjcjcqkVO对象", description = "教学评价-自评报告-关键词检测情况")
public class GjcjcqkVO extends Gjcjcqk {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "关键词类型")
    private Integer gjclx;
}
