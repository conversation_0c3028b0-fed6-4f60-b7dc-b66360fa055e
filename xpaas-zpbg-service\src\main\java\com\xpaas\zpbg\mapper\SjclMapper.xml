<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.SjclMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sjclResultMap" type="com.xpaas.zpbg.entity.Sjcl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="MKID" property="mkid"/>
        <result column="SSMKMC" property="ssmkmc"/>
        <result column="PX" property="px"/>
        <result column="CLDZ" property="cldz"/>
        <result column="YYZT" property="yyzt"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="sjclResultMapVO" type="com.xpaas.zpbg.vo.SjclVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="MKID" property="mkid"/>
        <result column="SSMKMC" property="ssmkmc"/>
        <result column="PX" property="px"/>
        <result column="CLDZ" property="cldz"/>
        <result column="YYZT" property="yyzt"/>
    </resultMap>

    <select id="selectSjclPage" resultType="com.xpaas.zpbg.vo.SjclVO">
        SELECT
        sjcl.*,
        GROUP_CONCAT( CASE WHEN mkgl.CMM IS NOT NULL AND mkgl.CMM != '' THEN mkgl.CMM ELSE mkgl.MKMC END ) AS MKMC,
        IFNULL(yyxx.YYCS,0) AS YYCS
        FROM
        T_DT_JXPJ_ZPBG_SJCL sjcl
        LEFT JOIN T_DT_JXPJ_ZPBG_SJCLSSMK sjclssmk ON sjcl.ID = sjclssmk.SJCLID
        LEFT JOIN T_DT_JXPJ_ZPBG_MKGL mkgl ON sjclssmk.MKID = mkgl.ID
        LEFT JOIN (
        SELECT
        sjbgl.SJCLID,
        sjbgl.BBID,
        bgmk.MKMC,
        count( bbgl.ID ) AS YYCS
        FROM
        T_DT_JXPJ_ZPBG_SJBGL sjbgl
        LEFT JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON sjbgl.BBID = bbgl.ID
        LEFT JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bbgl.BGMKID = bgmk.ID
        WHERE
        sjbgl.scbj = 0
        AND bbgl.scbj = 0
        AND bgmk.scbj = 0
        AND bbgl.BBLX = 1
        <if test="sjcl.ssmkid != null and sjcl.ssmkid != ''">
            AND bgmk.MKID = #{sjcl.ssmkid}
        </if>
        GROUP BY
        sjbgl.SJCLID
        ) yyxx ON sjcl.ID = yyxx.SJCLID
        WHERE
        sjcl.scbj = 0
        AND sjclssmk.scbj = 0
        AND mkgl.scbj = 0
        <if test="sjcl.mklx != null and sjcl.mklx != 0">
            and mkgl.mklx = #{sjcl.mklx}
        </if>
        <if test="sjcl.clmc != null and sjcl.clmc != ''">
            and sjcl.clmc like concat('%', #{sjcl.clmc}, '%')
        </if>
        <if test="sjcl.yyzt != null">
            and sjcl.yyzt = #{sjcl.yyzt}
        </if>
        <if test="sjcl.ssmkid != null and sjcl.ssmkid != ''">
            AND sjcl.id in (select sjclid from t_dt_jxpj_zpbg_sjclssmk where mkid = #{sjcl.ssmkid} )
            AND sjclssmk.MKID = #{sjcl.ssmkid}
        </if>
        GROUP BY
        sjcl.ID
        ORDER BY
        YYCS DESC,sjcl.PX ASC
    </select>

</mapper>
