package com.xpaas.zpbg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.oss.model.XpaasFile;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.secure.utils.SecureUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.dto.HeaderValueDTO;
import com.xpaas.zpbg.dto.RwryDTO;
import com.xpaas.zpbg.dto.ZjyjExcelDTO;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bbpz;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.mapper.*;
import com.xpaas.zpbg.service.*;
import com.xpaas.zpbg.vo.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * onlyOffice 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@Service
public class OnlyOfficeServiceImpl implements IOnlyOfficeService {
    @Resource
    private OnlyOfficeMapper onlyOfficeMapper;

    @Resource
    private BbglMapper bbglMapper;

    @Autowired
    IOssClient client;

    @Autowired
    private IMkglService mkglService;

    @Resource
    private IBbwjnrService bbwjnrService;

    @Resource
    private IBbglService bbglService;

    private Map<String, LocalDateTime> commitKeys = new ConcurrentHashMap<>();

    @Resource
    private BgglMapper bgglMapper;
    @Resource
    private ZjyjMapper baseMapper;
    @Resource
    private IOoCommandService ooCommandService;

    @Resource
    private IRwfgService rwfgService;

    @Resource
    IZjyjService zjyjService;

    @Resource
    IZzclglService zzclglService;

    @Resource
    IZcclglService zcclglService;

    @Resource
    ISjzbglService sjzbglService;

    @Resource
    IUserClient userClient;

    @Resource
    BbpzMapper bbpzMapper;

    @Resource
    IBbpzService bbpzService;

    /**
     * 撰写人可更新状态
     */
    List<Integer> writeStatusList = Arrays.asList(10, 20, 40, 50, 60, 80);

    @Autowired
    private BgglServiceImpl bgglServiceImpl;

    /**
     * 通过版本管理ID获取进度管理ID
     *
     * @param bbglId 版本管理ID
     * @return
     */
    @Override
    public HeaderValueDTO getJdglIdValue(String bbglId) {
        String bbglIdNew = bbglId;
        HeaderValueDTO dto = onlyOfficeMapper.getJdglMkIdValue(bbglIdNew);
        return dto;

    }

    /**
     * 报告头部信息
     *
     * @param bbglId 版本管理ID
     * @param bgid   报告ID
     * @param bgmkid 报告模块ID
     * @return
     */
    @Override
    public HeaderValueDTO getHeaderValue(String bbglId, String bgid, String bgmkid) {
        String bbglIdNew = bbglId;
        HeaderValueDTO result = new HeaderValueDTO();
        HeaderValueDTO headerValueOther = null;
        result.setZjyjIsNull(true);
        List<ZjyjExcelDTO> zjyjExcelDTO = baseMapper.getZjyjExcel(bgid, bbglId, bgmkid);
        if (zjyjExcelDTO.size() == 0) {
            result.setZjyjIsNull(false);
        }
        //查询报告是否有进度
        BgglVO bgdata = bgglMapper.selectBgData(bgid);
        if (bgdata == null) {
            return result;
        }
        if (bgdata.getJdglid() == null) {
            headerValueOther = onlyOfficeMapper.getGlHeader(bbglId);
            if (headerValueOther == null) {
                return result;
            }
            result.setBgmc(headerValueOther.getBgmc());
            result.setMkmc(headerValueOther.getMkmc());
        } else {
            // 取 开始时间，撰写结束时间，审阅结束时间，报告名，模块名
            headerValueOther = onlyOfficeMapper.getHeaderValueOther(bbglId);
            if (headerValueOther == null) {
                return result;
            }
            //撰写平台擦看
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String startTime = headerValueOther.getKssj().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter);
            String zxEndTime = headerValueOther.getZxjssj().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter);
            String syEndTime = headerValueOther.getSyjssj().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter);
            result.setStartTime(startTime);
            result.setZxEndTime(zxEndTime);
            result.setSyEndTime(syEndTime);
            result.setJdmc(headerValueOther.getJdmc());
            result.setBgmc(headerValueOther.getBgmc());
            result.setMkmc(headerValueOther.getMkmc());
        }
        String dwmcdz = "";
        String ryxmdz = "";
        String dwmczxr = "";
        String ryxmzxr = "";
        String zjxm = "";

        List<Map<String, String>> list = new ArrayList<>();

        // 取责任单位，责任人，撰写单位，撰写人
        List<HeaderValueDTO> getHeaderValueDtoList = onlyOfficeMapper.getHeaderValue(headerValueOther.getBgid(), headerValueOther.getBgmkid());
        if (!getHeaderValueDtoList.isEmpty()) {
            for (HeaderValueDTO dto : getHeaderValueDtoList) {
                if (dto.getFglx() == 1) {
                    dwmcdz = dwmcdz + " / " + dto.getDwmc();
                    ryxmdz = ryxmdz + " / " + dto.getRyxm();
                } else {
                    Map<String, String> map = new HashMap<>();
                    dwmczxr = dwmczxr + " / " + dto.getDwmc();
                    ryxmzxr = ryxmzxr + " / " + dto.getRyxm();
                    map.put("clzt", dto.getClzt().equals("1") ? "撰写中" : "已提交");
                    map.put("name", dto.getRyxm());
                    list.add(map);
                }
            }
        }
        result.setRyxmZxrLsit(list);
        LoginUser user = AuthUtil.getUser();
        // 获取当前登录人员的身份
        String userid = user.getUserId().toString();

        List<HeaderValueDTO> userClztLsit = onlyOfficeMapper.getUserClzt(bgid, bgmkid, userid);
        if (userClztLsit.size() != 0) {
            if (userClztLsit.get(0).getClzt().equals("1")) {
                result.setZxrclzt("1");
            } else {
                result.setZxrclzt("2");
            }
        } else {
            result.setZxrclzt("1");
        }

        if (!dwmcdz.equals("")) {
            String dwmcdzNew = dwmcdz.substring(2);
            result.setDwmcDz(dwmcdzNew);
        }
        ;
        if (!ryxmdz.equals("")) {
            String ryxmdzNew = ryxmdz.substring(2);
            result.setRyxmDz(ryxmdzNew);
        }
        ;
        if (!dwmczxr.equals("")) {
            String dwmczxrNew = dwmczxr.substring(2);
            result.setDwmcZxr(dwmczxrNew);
        }
        ;
        if (!ryxmzxr.equals("")) {
            String ryxmzxrNew = ryxmzxr.substring(2);
            result.setRyxmZxr(ryxmzxrNew);
        }

        // 取专家名
        List<HeaderValueDTO> getHeaderValueDtozjList = onlyOfficeMapper.getHeaderValuezj(headerValueOther.getBgid(), headerValueOther.getBgmkid(), bbglIdNew);
        if (!getHeaderValueDtozjList.isEmpty()) {
            for (HeaderValueDTO dto : getHeaderValueDtozjList) {
                if (dto != null) {
                    zjxm = zjxm + " / " + dto.getZjxm();
                }
            }
        }

        if (!zjxm.equals("")) {
            String zjxmNew = zjxm.substring(2);
            result.setZjxm(zjxmNew);
        }

        return result;
    }

    /**
     * 获取模块树
     *
     * @param dto  参数
     * @param role 角色
     * @return
     */
    @Override
    public List<OnlyOfflceTreeVO> getMkTreeValue(HeaderValueDTO dto, String role) {


//        List<OnlyOfflceTreeVO> listTree = getMkChildData(dto, role);
//
//        return listTree;

        return getMkTreeData(dto, role);

    }

    /**
     * 判断是否disabled
     *
     * @param bbglId        版本管理ID
     * @param role          角色
     * @param currentUserId 当前用户ID
     * @return boolean
     */
    private boolean isDisabled(String bbglId, String role, String currentUserId) {

        if ("zxrOrDz".equals(role)) {

            int count = onlyOfficeMapper.selectCountZxr(bbglId, currentUserId);
            return count <= 0;

        } else if ("zj".equals(role)) {

            int count = onlyOfficeMapper.selectCountZj(bbglId, currentUserId);
            return count <= 0;

        } else if ("gly".equals(role)) {
            return false;
        } else {
            return true;
        }
    }

    public List<OnlyOfflceTreeVO> getMkTreeData(HeaderValueDTO param, String role) {

        if ("gly".equals(role)) {
            return this.getMkChildData(param, role, false);
        } else if ("zxrOrDz".equals(role)) {
            return getZxrMkTree(param);
        } else if ("zj".equals(role)) {
            return getZjMkTree(param);
        } else if ("zm".equals(role)) {
            // 卓马用
            return this.getMkChildData(param, role, true);
        }

        return null;
    }

    /**
     * 查询撰写人、点长模块
     *
     * @param param
     * @return
     */
    private List<OnlyOfflceTreeVO> getZxrMkTree(HeaderValueDTO param) {
        String userId = SecureUtil.getUserId().toString();
        List<OnlyOfficeTreeList> list = onlyOfficeMapper.getZxrMkList(param.getJdglid(), userId);
        return listToTree(list, param.getBgmkid());
    }

    /**
     * 查询专家模块
     *
     * @param param
     * @return
     */
    private List<OnlyOfflceTreeVO> getZjMkTree(HeaderValueDTO param) {
        String userId = SecureUtil.getUserId().toString();
        List<OnlyOfficeTreeList> list = null;

        if (param.getJdglid() != null) {
            list = onlyOfficeMapper.getZjMkList(param.getJdglid(), userId);
        } else if (param.getBgid() != null) {
            // 获取当前报告进度
            Bggl bggl = new Bggl();
            bggl.setId(Long.parseLong(param.getBgid()));
            Bggl one = bgglServiceImpl.getOne(Condition.getQueryWrapper(bggl));
            param.setJdglid(String.valueOf(one.getJdglid()));
            list = onlyOfficeMapper.getZjMkList(param.getJdglid(), userId);
        }

        return listToTree(list, param.getBgmkid());
    }

    private List<OnlyOfflceTreeVO> listToTree(List<OnlyOfficeTreeList> list, String bgmkid) {
        if (list == null) {
            return null;
        }
        // 1. 按照 px 和 id 排序
        list.sort(Comparator.comparingInt(OnlyOfficeTreeList::getPx)
                .thenComparingLong(OnlyOfficeTreeList::getId));

        boolean hasSelected = false;
        List<OnlyOfflceTreeVO> result = new ArrayList<>();
        for (OnlyOfficeTreeList item : list) {
            if (null == item.getYjzbid()) {
                OnlyOfflceTreeVO vo = new OnlyOfflceTreeVO();
                vo.setValue(String.valueOf(item.getId()));
                vo.setLabel(item.getMkmc());
                vo.setIsLeaf(true);
                vo.setDisabled(false);
                boolean selected = bgmkid != null && bgmkid.equals(String.valueOf(item.getId()));
                vo.setIsSelected(selected);
                vo.setBbglId(item.getBbglId());
                vo.setBblx(item.getBblx());
                if (!hasSelected) {
                    hasSelected = selected;
                }
                result.add(vo);
            } else {
                // 一级
                OnlyOfflceTreeVO has = result.stream().filter(i -> i.getValue().equals(String.valueOf(item.getYjzbid()))).findFirst().orElse(null);
                if (has == null) {
                    OnlyOfflceTreeVO vo = new OnlyOfflceTreeVO();
                    vo.setValue(String.valueOf(item.getYjzbid()));
                    vo.setLabel(item.getYjzb());
                    vo.setIsLeaf(false);
                    vo.setDisabled(false);
                    vo.setIsSelected(false);
                    result.add(vo);
                    has = vo;
                }

                if (has.getChildren() == null) {
                    has.setChildren(new ArrayList<>());
                }
                // 二级
                OnlyOfflceTreeVO erVo = has.getChildren().stream().filter(i -> i.getValue().equals(String.valueOf(item.getEjzbid()))).findFirst().orElse(null);
                OnlyOfflceTreeVO vo = new OnlyOfflceTreeVO();
                vo.setValue(String.valueOf(item.getEjzbid()));
                vo.setLabel(item.getEjzb());
                vo.setIsLeaf(false);
                vo.setDisabled(false);
                vo.setIsSelected(false);
                if (erVo == null) {
                    has.getChildren().add(vo);
                    erVo = vo;
                }

                // 三级
                if (erVo.getChildren() == null) {
                    erVo.setChildren(new ArrayList<>());
                }
                OnlyOfflceTreeVO voSan = new OnlyOfflceTreeVO();
                voSan.setValue(String.valueOf(item.getId()));
                voSan.setLabel(item.getMkmc());
                voSan.setBbglId(item.getBbglId());
                voSan.setIsLeaf(true);
                voSan.setDisabled(false);
                voSan.setBblx(item.getBblx());
                boolean selected = bgmkid != null && bgmkid.equals(String.valueOf(item.getId()));
                voSan.setIsSelected(selected);
                if (!hasSelected) {
                    hasSelected = selected;
                }
                erVo.getChildren().add(voSan);
            }
        }

        if (!hasSelected) {
            for (OnlyOfflceTreeVO tree : result) {
                if (tree.getChildren() == null && !tree.isDisabled()) {
                    tree.setIsSelected(true);
                    break;
                } else {
                    boolean has = false;
                    if (tree.getChildren() != null) {
                        for (OnlyOfflceTreeVO child : tree.getChildren()) {
                            for (OnlyOfflceTreeVO childChild : child.getChildren()) {
                                if (!childChild.isDisabled()) {
                                    childChild.setIsSelected(true);
                                    has = true;
                                    break;
                                }
                            }
                            if (has) {
                                break;
                            }
                        }
                    }

                }
            }
        }

        return result;

    }

    private List<OnlyOfflceTreeVO> getMkChildData(HeaderValueDTO dto, String role, boolean zhuoma) {
        boolean flag = false;
        List<OnlyOfflceTreeVO> listTree = new ArrayList<>();
        LoginUser user = AuthUtil.getUser();
        String currenUserId = String.valueOf(user.getUserId());

        List<OnlyOfflceTreeVO> listTree1 = new ArrayList<>();
        if (!StringUtil.isEmpty(dto.getJdglid())) {
            listTree1 = onlyOfficeMapper.getMkTree1(dto.getJdglid());
        } else if (!StringUtil.isEmpty(dto.getBgid())) {
            listTree1 = onlyOfficeMapper.getMkTreeByBgid(dto.getBgid());
        }

        // 查询该报告所有进度关联的模块

        List<String> bgmkIds = new ArrayList<>();
        if (!StringUtil.isEmpty(dto.getBgid())) {
            bgmkIds = onlyOfficeMapper.getJdmkIds(dto.getBgid());
        }

        if (listTree1 != null) {
            for (int i = 0; i < listTree1.size(); i++) {

                OnlyOfflceTreeVO tree = new OnlyOfflceTreeVO();
                // 无叶子节点
                if (listTree1.get(i).getHasChildren() == 0L) {

                    if (zhuoma) {
                        // 查询报告状态
                        String bgmkzt = onlyOfficeMapper.getBgmkzt(listTree1.get(i).getValue());
                        if (!"90".equals(bgmkzt)) {
                            continue;
                        }
                    }

                    if (dto.getBgmkid().equals(listTree1.get(i).getValue())) {
                        tree.setIsSelected(true);
                        flag = true;
                    } else {
                        tree.setIsSelected(false);
                    }
                    tree.setIsLeaf(true);
                    tree.setLabel(listTree1.get(i).getLabel());
                    tree.setValue(listTree1.get(i).getValue());
                    tree.setBbglId(listTree1.get(i).getBbglId());
                    tree.setBblx(listTree1.get(i).getBblx());
                    tree.setZygzdId("");
                    tree.setDisabled(isDisabled(listTree1.get(i).getBbglId(), role, currenUserId));
                    String bgmkId = tree.getValue();
                    Map<String, String> stringStringMap = onlyOfficeMapper.selectZygzdByBgmkId(bgmkId);
                    tree.setZygzdId(MapUtils.getString(stringStringMap, "bztxId", ""));
                    tree.setYjzb(MapUtils.getString(stringStringMap, "yjzb", ""));
                    tree.setYjzbId(MapUtils.getString(stringStringMap, "yjzbId", ""));
                    tree.setEjzb(MapUtils.getString(stringStringMap, "ejzb", ""));
                    tree.setEjzbId(MapUtils.getString(stringStringMap, "ejzbId", ""));
                    // 卓马 取点长列表
                    List<Map<String, String>> dzList = onlyOfficeMapper.getDzList(listTree1.get(i).getValue());

                    tree.setDzList(dzList);
                    tree.setWjlj(listTree1.get(i).getWjlj());

                    listTree.add(tree);
                } else {
                    //一级指标赋值
                    tree.setLabel(listTree1.get(i).getLabel());
                    tree.setValue(listTree1.get(i).getValue());
                    // 取二级指标
                    List<OnlyOfflceTreeVO> listTreeChild = new ArrayList<>();
                    if (!StringUtil.isEmpty(dto.getJdglid())) {
                        listTreeChild = onlyOfficeMapper.getMkTree2(dto.getJdglid(), listTree1.get(i).getValue());
                    } else if (!StringUtil.isEmpty(dto.getBgid())) {
                        listTreeChild = onlyOfficeMapper.getMkTree2ByBgid(dto.getBgid(), listTree1.get(i).getValue());
                    }


                    if (listTreeChild != null && listTreeChild.size() > 0) {
                        for (OnlyOfflceTreeVO treeChild : listTreeChild) {
                            if (!StringUtil.isEmpty(treeChild.getValue())) {
                                treeChild.setIsSelected(false);
                                // 取模块id
                                List<OnlyOfflceTreeVO> listTreeChildMk = new ArrayList<>();
                                if (!StringUtil.isEmpty(dto.getJdglid())) {
                                    listTreeChildMk = onlyOfficeMapper.getMkTree3(dto.getJdglid(), listTree1.get(i).getValue(), treeChild.getValue());
                                } else if (!StringUtil.isEmpty(dto.getBgid())) {
                                    listTreeChildMk = onlyOfficeMapper.getMkTree3ByBgid(dto.getBgid(), listTree1.get(i).getValue(), treeChild.getValue());
                                }


                                if (zhuoma) {
                                    listTreeChildMk = listTreeChildMk.stream().filter(j -> "90".equals(j.getBgmkzt())).collect(Collectors.toList());
                                }

                                if (!listTreeChildMk.isEmpty()) {
                                    for (OnlyOfflceTreeVO treeChildMk : listTreeChildMk) {
                                        // 对应卓马 添加主要关注点
                                        String zygzdId = "";
                                        String bgmkId = treeChildMk.getValue();
                                        Map<String, String> stringStringMap = onlyOfficeMapper.selectZygzdByBgmkId(bgmkId);
                                        zygzdId = MapUtils.getString(stringStringMap, "bztxId", "");
                                        treeChildMk.setZygzdId(zygzdId);
                                        treeChildMk.setYjzb(MapUtils.getString(stringStringMap, "yjzb", ""));
                                        treeChildMk.setYjzbId(MapUtils.getString(stringStringMap, "yjzbId", ""));
                                        treeChildMk.setEjzb(MapUtils.getString(stringStringMap, "ejzb", ""));
                                        treeChildMk.setEjzbId(MapUtils.getString(stringStringMap, "ejzbId", ""));

                                        // 卓马 取点长列表
                                        List<Map<String, String>> dzList = onlyOfficeMapper.getDzList(treeChildMk.getValue());


                                        treeChildMk.setDzList(dzList);
                                        if (dto.getBgmkid().equals(treeChildMk.getValue())) {
                                            treeChildMk.setIsSelected(true);
                                            flag = true;
                                        } else {
                                            treeChildMk.setIsSelected(false);
                                        }
                                        treeChildMk.setIsLeaf(true);
                                        treeChildMk.setDisabled(isDisabled(treeChildMk.getBbglId(), role, currenUserId));
                                    }

                                }

                                // 附二级指标下所有的模块id
                                treeChild.setChildren(listTreeChildMk);
                            }
                        }
                        // 附一级指标下的二级指标
                        tree.setChildren(listTreeChild);
                    } else {
                        tree.setChildren(new ArrayList<>());
                    }
                    listTree.add(tree);
                }
            }
        } else {
            return new ArrayList<>();
        }

        filterTree(listTree, bgmkIds);

        if (!flag) {
            for (OnlyOfflceTreeVO tree : listTree) {
                if (tree.getChildren() == null && !tree.isDisabled()) {
                    tree.setIsSelected(true);
                    return listTree;
                } else {
                    if (tree.getChildren() != null) {
                        for (OnlyOfflceTreeVO child : tree.getChildren()) {
                            for (OnlyOfflceTreeVO childChild : child.getChildren()) {
                                if (!childChild.isDisabled()) {
                                    childChild.setIsSelected(true);
                                    return listTree;
                                }
                            }

                        }
                    }

                }
            }
        }

        return listTree;
    }

    /**
     * 过滤未关联的模块
     *
     * @param listTree
     * @param targetValues
     */
    public static void filterTree(List<OnlyOfflceTreeVO> listTree, List<String> targetValues) {
        if (listTree == null || listTree.isEmpty()) {
            return;
        }

        List<OnlyOfflceTreeVO> filteredTree = new ArrayList<>();
        for (OnlyOfflceTreeVO level1Node : listTree) {
            // 处理只有一级节点的情况
            if (level1Node.getChildren() == null || level1Node.getChildren().isEmpty()) {
                if (targetValues.contains(level1Node.getValue())) {
                    filteredTree.add(level1Node);
                }
            } else { // 处理多级节点的情况
                List<OnlyOfflceTreeVO> filteredLevel2Children = new ArrayList<>();
                for (OnlyOfflceTreeVO level2Node : level1Node.getChildren()) {
                    if (level2Node.getChildren() != null && !level2Node.getChildren().isEmpty()) {
                        List<OnlyOfflceTreeVO> filteredLevel3Children = level2Node.getChildren().stream()
                                .filter(level3Node -> targetValues.contains(level3Node.getValue()))
                                .collect(Collectors.toList());

                        if (!filteredLevel3Children.isEmpty()) {
                            level2Node.setChildren(filteredLevel3Children);
                            filteredLevel2Children.add(level2Node);
                        }
                    }
                }

                if (!filteredLevel2Children.isEmpty()) {
                    level1Node.setChildren(filteredLevel2Children);
                    filteredTree.add(level1Node);
                }
            }
        }

        listTree.clear();
        listTree.addAll(filteredTree);
    }

    /**
     * onlyoffice回调
     *
     * @param param   参数
     * @param request 请求
     * @return
     */
    @Override
    public Map<String, Object> callBack(Map<String, String> param, HttpServletRequest request) {
        boolean b = true;
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("error", 0);
        try {
            Scanner scanner = new Scanner(request.getInputStream()).useDelimiter("\\A");
            String body = scanner.hasNext() ? scanner.next() : "";
            JSONObject jsonObj = JSONObject.parseObject(body);

            log.info("word文件，返回状态--------------------" + jsonObj);
            /*
                0 - no document with the key identifier could be found,
                1 - document is being edited,
                2 - document is ready for saving,fds
                3 - document saving error has occurred,
                4 - document is closed with no changes,
                6 - document is being edited, but the current document state is saved,
                7 - error has occurred while force saving the document.
             * */
            int status = (int) jsonObj.get("status");

            // 判断userdata
            if (jsonObj.containsKey("userdata")) {
                // drop
                JSONObject userData = jsonObj.getJSONObject("userdata");
                if (userData.containsKey("operate")) {
                    String operate = userData.getString("operate");
                    if ("drop".equals(operate)) {
                        if (jsonObj.containsKey("users")) {
                            List users = jsonObj.getObject("users", List.class);
                            if (users != null && users.size() > 0) {

                                String tjUser = userData.getString("tjUser");
                                if (!StringUtil.isEmpty(tjUser)) {
                                    users.remove(tjUser);
                                }

                                Map<String, Object> params = new HashMap<>();
                                params.put("c", "drop");
                                params.put("key", jsonObj.getString("key"));
                                params.put("users", users);
                                try {
                                    ooCommandService.commandRequest(params, null);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    return result;
                                }
                            }
                        }
                    }

                }
            }

            String mode = param.get("mode");
            if (status == 2 || status == 6) {
                /*
                 * 当咱们关闭编辑窗口后，十秒钟左右onlyoffice会将它存储的咱们的编辑后的文件，，此时status = 2，经过request发给咱们，咱们须要作的就是接收到文件而后回写该文件。
                 * */
                /*
                 * 定义要与文档存储服务保存的编辑文档的连接。当状态值仅等于2或3时，存在链路。
                 * */
                String downloadUri = (String) jsonObj.get("url");
                log.info("word文件，下载地址--------------------" + downloadUri);

                //解析得出文件名
//                String fileName = downloadUri.substring(downloadUri.lastIndexOf('/') + 1);

                URL url = new URL(downloadUri);
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                InputStream stream = connection.getInputStream();

                // 上传附件
                MultipartFile multipartFile = new MockMultipartFile("file", "test.docx", null, stream);
                R<XpaasFile> xpaasFile = client.putFile(multipartFile);

                // 文件服务的URL
                String severUrl = xpaasFile.getData().getName();

                // 关闭连接
                connection.disconnect();


                if ("mbgl".equals(mode)) {
                    // 模板管理
                    MkglVO mkglVO = new MkglVO();
                    mkglVO.setId(Long.valueOf(param.get("mkid")));
//                    Mkgl detail = mkglService.getOne(Condition.getQueryWrapper(mkglVO));
//                    String mbwjkey = "";
//                    if ("".equals(detail.getMbwjkey())) {
//                        mbwjkey = UUID.randomUUID().toString();
//                    } else {
//                        mbwjkey = detail.getMbwjkey();
//                    }
                    String mbwjkey = UUID.randomUUID().toString();
                    mkglVO.setMbwjkey(mbwjkey);
                    mkglVO.setMbwjlj(severUrl);
                    b = mkglService.updateById(mkglVO);
                } else if ("zx".equals(mode)) {

                    String forceSaveType = ""; // 0强制保存
                    if (jsonObj.containsKey("forcesavetype")) {
                        forceSaveType = jsonObj.getString("forcesavetype");
                    }
                    if (status == 6 && ("0".equals(forceSaveType))) {

                        if (jsonObj.containsKey("userdata")) {

                            JSONObject userData = jsonObj.getJSONObject("userdata");
                            if (userData.containsKey("operate")) {
                                String operate = userData.getString("operate");

                                Bbgl newBbgl = new Bbgl();
                                newBbgl.setWjlj(severUrl);

                                if ("zc".equals(operate)) {
                                    log.info("word文件，返回状态" + "  暂存");
                                    // 暂存
                                    newBbgl.setId(Long.parseLong(userData.getString("newBbglid")));
                                    bbglMapper.updateById(newBbgl);
                                } else if ("tj".equals(operate)) {
                                    // 提交审核 更新提交版本
                                    newBbgl.setId(Long.parseLong(userData.getString("newBbglid")));
                                    newBbgl.setWjkey(UUID.randomUUID().toString());
                                    bbglMapper.updateById(newBbgl);

                                    commitKeys.put(jsonObj.getString("key"), LocalDateTime.now());

                                    // 更新主线版本
                                    newBbgl.setId(Long.parseLong(userData.getString("bbglid")));
                                    newBbgl.setWjkey(UUID.randomUUID().toString());
                                    bbglMapper.updateById(newBbgl);

                                    // 踢掉提交者之外的人
                                    Map<String, Object> dropParam = new HashMap<>();
                                    dropParam.put("c", "info");
                                    dropParam.put("key", jsonObj.getString("key"));
                                    Map<String, String> userdata = new HashMap<>();
                                    userdata.put("operate", "drop");
                                    userdata.put("tjUser", jsonObj.getJSONArray("users").getString(0));
                                    try{
                                        ooCommandService.commandRequest(dropParam, userdata);
                                    } catch (Exception e){
                                        log.error(e.getMessage());
                                    }

                                    // 关键词检测
                                    bbwjnrService.saveWjnr(Long.parseLong(userData.getString("newBbglid")), severUrl);
                                    log.info("word文件，返回状态" + "  提交");
                                } else if ("zjsy".equals(operate)) {
                                    // 审核提交
                                    newBbgl.setId(Long.parseLong(userData.getString("newBbglid")));
                                    bbglMapper.updateById(newBbgl);
                                    log.info("word文件，返回状态" + "  专家审批");
                                } else if ("removeExpertSuggests".equals(operate)) {
                                    // 删除专家意见
                                    newBbgl.setId(Long.parseLong(userData.getString("bbglid")));
                                    bbglMapper.updateById(newBbgl);
                                    log.info("word文件，返回状态" + "  删除专家意见");
                                }

                            }
                        }

                    } else if (status == 2) {

                        String officeMode = param.get("officeMode");
                        if ("view".equals(officeMode)) {
                            log.info("word文件，返回状态" + "查看模式 直接返回");
                            return result;
                        }

                        String bbglId = param.get("bbglId");

                        // 校验报告是否可编辑
                        Bbgl bbglInfo = bbglService.getById(bbglId);

                        Bbgl bbgl = new Bbgl();
                        bbgl.setId(Long.valueOf(bbglId));
                        bbgl.setWjkey(UUID.randomUUID().toString());
                        if (writeStatusList.contains(bbglInfo.getBgmkzt()) ||
                                commitKeys.containsKey(jsonObj.getString("key"))) {
                            commitKeys.remove(jsonObj.getString("key"));
                            log.info("word文件，返回状态 更新主线版本url:" + bbglId);
                            bbgl.setWjlj(severUrl);
                            // 关键词检测
                            bbwjnrService.saveWjnr(bbglInfo.getId(), severUrl);
                        } else {
                            log.info("word文件，返回状态 更新主线版本key:" + bbglId);
                        }
                        // 更新暂存版本文件路径
                        bbglMapper.updateById(bbgl);
                    } else {
                        String bbglId = param.get("bbglId");
                        Bbgl bbgl = new Bbgl();
                        bbgl.setId(Long.valueOf(bbglId));
                        bbgl.setWjkey(UUID.randomUUID().toString());
                        bbglMapper.updateById(bbgl);
                    }

                }
            } else if (status == 3 || status == 7) {
                if ("zx".equals(mode)) {
                    String bbglId = param.get("bbglId");
                    Bbgl bbgl = new Bbgl();
                    bbgl.setId(Long.valueOf(bbglId));
                    bbgl.setWjkey(UUID.randomUUID().toString());
                    bbglMapper.updateById(bbgl);
                } else if ("mbgl".equals(mode)) {
                    MkglVO mkglVO = new MkglVO();
                    mkglVO.setId(Long.valueOf(param.get("mkid")));
                    String mbwjkey = UUID.randomUUID().toString();
                    mkglVO.setMbwjkey(mbwjkey);
                    mkglService.updateById(mkglVO);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return result;
        }


        return result;
    }

    /**
     * 历史版本模块树
     *
     * @param bbglId 版本管理ID
     * @return
     */
    @Override
    public List<OnlyOfflceTreeVO> getBbAsMkTree(String bbglId) {
        OnlyOfflceTreeVO onlyOfflceTree = onlyOfficeMapper.selectHistoryVersion(bbglId);
        onlyOfflceTree.setIsSelected(true);
        onlyOfflceTree.setIsLeaf(true);
        List<OnlyOfflceTreeVO> list = new ArrayList<>();
        list.add(onlyOfflceTree);
        return list;
    }

    /**
     * 踢掉onlyoffice在线人
     *
     * @param list
     */
    @Override
    public void dropUsers(List<Map<String, Object>> list) {

        if (list != null && !list.isEmpty()) {
            list.forEach(item -> {
                if (item.containsKey("bgmkid") && item.containsKey("users")) {
                    String bgmkid = MapUtils.getString(item, "bgmkid");

                    List<String> userList = (List<String>) item.get("users");
                    if (userList != null && userList.size() > 0) {
                        String wjkey = onlyOfficeMapper.getWjkeyByBgmkid(bgmkid);
                        Map<String, Object> params = new HashMap<>();
                        params.put("c", "drop");
                        params.put("key", wjkey);
                        params.put("users", userList);
                        try {
                            ooCommandService.commandRequest(params, null);
                        } catch (Exception ex ) {
                            log.error(ex.getMessage());
                        }
                    }
                }
            });
        }
    }

    /**
     * 获取踢人提示信息
     *
     * @param params
     * @return
     */
    @Override
    public String getDropMsg(Map<String, Object> params) {

        String role = MapUtils.getString(params, "role");
        String bgmkid = MapUtils.getString(params, "bgmkid");
        if ("zxr".equals(role) || "dz".equals(role)) {
            // 判断是否已提交

            Bbgl bbglParam = new Bbgl();
            bbglParam.setBgmkid(Long.parseLong(bgmkid));
            bbglParam.setScbj(0);
            bbglParam.setBblx(1);
            Bbgl bbglInfo = bbglService.getOne(Condition.getQueryWrapper(bbglParam));
            Integer bgmkzt = bbglInfo.getBgmkzt();
            if (bgmkzt == 30) {
                return "点长提交了文档，页面将跳转到首页";
            }
        }

        // 判断是否任务分工变化
        RwryDTO rwfgParam = new RwryDTO();
        if ("zxr".equals(role)) {
            rwfgParam.setFglx(2);
        } else if ("dz".equals(role)) {
            rwfgParam.setFglx(1);
        } else if ("zj".equals(role)) {
            rwfgParam.setFglx(3);
        } else {
            rwfgParam.setFglx(-1);
        }
        rwfgParam.setScbj(0);
        rwfgParam.setRyid(SecureUtil.getUserId());
        rwfgParam.setBgmkid(Long.parseLong(bgmkid));
        List<RwryDTO> rwryDTOS = rwfgService.selectMkryByWrapper(Condition.getQueryWrapper(rwfgParam));
        if (rwryDTOS == null || rwryDTOS.size() == 0) {
            if ("zj".equals(role)) {
                return "您已无该关注点审阅权限，页面将跳转到首页";
            } else if ("zxr".equals(role)) {
                return "您已无该关注点操作权限，页面将跳转到首页";
            } else if ("dz".equals(role)) {
                return "您已无该关注点操作权限，页面将跳转到首页";
            } else {
                return "";
            }

        }

        return "";
    }

    /**
     * 获取批注内容
     *
     * @param bbid          版本ID
     * @param commentIdList 批注ID
     * @return
     */
    @Override
    public String getCommentText(String bbid, List<String> commentIdList) {

        Long bbId = Long.parseLong(bbid);

        // 数据引用
        SjzbglVO sjzbglVO = new SjzbglVO();
        sjzbglVO.setBbid(bbId);
        sjzbglVO.setGlkeyList(commentIdList);
        List<SjzbglVO> sjzbglVOList = sjzbglService.getGlAllList(sjzbglVO);
        sjzbglVOList.removeAll(Collections.singleton(null));
        // 佐证材料
        List<ZzclglVO> zzclglVOList = zzclglService.getzzclList(bbid, commentIdList);
        zzclglVOList.removeAll(Collections.singleton(null));
        // 备查材料
        List<ZcclglVO> zcclglVOList = zcclglService.getzcclList(bbid, commentIdList);
        zcclglVOList.removeAll(Collections.singleton(null));
        // 专家意见
        List<ZjyjVO> zjyjVOList = zjyjService.getzjyjList(bbid, commentIdList);
        zjyjVOList.removeAll(Collections.singleton(null));

        List<CommentInfo> commentList = new ArrayList<>();

        List<String> resultParts = new ArrayList<>();

        int index = 0;

        if (!sjzbglVOList.isEmpty()) {
            resultParts.add("【引用数据】：" + sjzbglVOList.size() + "\n");
        }

        if (!zzclglVOList.isEmpty()) {
            resultParts.add("【佐证材料】：" + zzclglVOList.size() + "\n");
        }

        if (!zcclglVOList.isEmpty()) {
            resultParts.add("【备查材料】：" + zcclglVOList.size() + "\n");
        }

        // 专家意见
        if (!zjyjVOList.isEmpty()) {
            for (ZjyjVO vo : zjyjVOList) {
                CommentInfo commentInfo = new CommentInfo();
                commentInfo.setUserId(String.valueOf(vo.getCjr()));
                commentInfo.setUserName(userClient.userInfoById(vo.getCjr()).getData().getName());
                commentInfo.setCreateDate(vo.getCjrq());
                commentInfo.setText(vo.getGlwz());
                commentInfo.setCommentText(vo.getPjnr());
                commentInfo.setType("【专家意见】");
                commentList.add(commentInfo);
            }
        }

        if (!commentList.isEmpty()) {
            resultParts.add("【专家意见】：" + commentList.size() + "\n");
        }

        // 根据意见日期对专家姓名排序
        List<String> sortedUserNames = commentList.stream()
                .collect(Collectors.groupingBy(CommentInfo::getUserName, Collectors.maxBy(Comparator.comparing(CommentInfo::getCreateDate))))
                .entrySet().stream()
                .sorted(Comparator.comparing((Map.Entry<String, Optional<CommentInfo>> entry) -> entry.getValue().get().getCreateDate()).reversed()) // reversed() 表示降序排序
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());


        for (String userName : sortedUserNames) {

            // 筛选当前专家的意见并排序
            List<CommentInfo> userComments = commentList.stream()
                    .filter(i -> userName.equals(i.getUserName()))
                    .sorted(Comparator.comparing(CommentInfo::getCreateDate).reversed()) // 倒序排序
                    .collect(Collectors.toList());
            StringBuilder userSb = new StringBuilder(); // 用于存储每个userName的结果

            // 专家姓名
            if (index == 0) {
                userSb.append(userName).append("\n");
                index++;
            } else {
                userSb.append("\n").append(userName).append("\n");
            }

            // 意见日期
            if (!userComments.isEmpty()) {
                userSb.append(formatCreateDate(userComments.get(0).getCreateDate())).append("\n");

                for (int i = 0; i < userComments.size(); i++) {
                    userSb.append(i + 1).append(".").append(userComments.get(i).getCommentText()).append("\n");
                }
//                userSb.append("\n");
            }

            resultParts.add(userSb.toString()); // 将userName的结果添加到列表中
        }


        // 6. 合并结果字符串
        StringBuilder finalSb = new StringBuilder();
        for (String part : resultParts) {
            finalSb.append(part);
        }
        return finalSb.toString();
    }

    /**
     * 格式化为年月日
     *
     * @param createDate 日期
     * @return
     */
    private static String formatCreateDate(Date createDate) {
        LocalDate localDate = createDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 更新新版本
     *
     * @param params
     * @return
     */
    @Override
    public boolean updateNewBb(Map<String, String> params) {
        return onlyOfficeMapper.updateNewBb(params);
    }

    /**
     * 缓存提交版本KEY
     *
     * @param key
     */
    @Override
    public void addTjCache(String key) {
        Iterator<Map.Entry<String, LocalDateTime>> iterator = commitKeys.entrySet().iterator();
        LocalDateTime currentTime = LocalDateTime.now();
        while (iterator.hasNext()) {
            Map.Entry<String, LocalDateTime> entry = iterator.next();
            LocalDateTime commitTime = entry.getValue();

            // 判断 commitTime 是否比当前时间小于一天
            if (ChronoUnit.DAYS.between(commitTime, currentTime) >= 1) {
                iterator.remove(); // 删除该条目
            }
        }

        commitKeys.put(key, LocalDateTime.now());
    }


    /**
     * 添加批注顺序
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addCommentOrder(Map<String, Object> params) {
        String bbId = MapUtils.getString(params, "bbId", "");
        bbpzMapper.lockBb(bbId);
        List<Map<String, String>> orderList = new ArrayList<>();
        if (params.containsKey("orderList")) {
            orderList = (List<Map<String, String>>) params.get("orderList");
        }

        // 删除原来的批注
        bbpzMapper.deleteByBbId(bbId);

        if (!StringUtil.isEmpty(bbId) && orderList != null && !orderList.isEmpty()) {

            for (int i = 0; i < orderList.size(); i++) {
                Map<String, String> item = orderList.get(i);
                String commentId = MapUtils.getString(item, "commentId", "");
                if (StringUtil.isEmpty(commentId)) {
                    continue;
                }
                String commentText = MapUtils.getString(item, "commentText", "");
                Bbpz bbpz = new Bbpz();
                bbpz.setCommentId(commentId);
                bbpz.setBbid(Long.valueOf(bbId));
                bbpz.setCommentText(commentText);
                bbpz.setPx(i + 1);
                bbpzService.save(bbpz);
            }
        }
        return "";
    }

    /**
     * 删除批注
     *
     * @param params 批注、版本ID
     * @return
     */
    @Override
    public String delComment(Map<String, String> params) {
        String bbId = MapUtils.getString(params, "bbId", "");
        String commentId = MapUtils.getString(params, "commentId", "");
        if (StringUtil.isEmpty(bbId) || StringUtil.isEmpty(commentId)) {
            return "";
        }
        String[] split = commentId.split(",");
        Bbpz bbpz = new Bbpz();
        bbpz.setBbid(Long.valueOf(bbId));
        for (String s : split) {
            bbpz.setCommentId(s);
            bbpzMapper.delete(Condition.getQueryWrapper(bbpz));
        }

        return "";
    }

    @Data
    private static class CommentInfo {
        String userId;
        String userName;
        Date createDate;
        String text;
        String commentText;
        String type;
    }

    /**
     * 获取批注信息（卓马用）
     *
     * @param bgmkId 报告模块ID
     * @param list 批注ID
     * @return
     */
    @Override
    public String getCommentTextZm(String bgmkId, List<String> list) {

        // 获取主线版本ID
        String mainBbIdByBgId = onlyOfficeMapper.getMainIdByBgmkId(bgmkId);
        if (mainBbIdByBgId != null && !StringUtil.isEmpty(mainBbIdByBgId) && !StringUtil.isBlank(mainBbIdByBgId)) {

            Long bbId = null;
            try {
                bbId = Long.valueOf(mainBbIdByBgId);
            } catch (NumberFormatException e) {
                log.error(e.getMessage());
                return "";
            }

            // 数据引用
            SjzbglVO sjzbglVO = new SjzbglVO();
            sjzbglVO.setBbid(bbId);
            sjzbglVO.setGlkeyList(list);
            List<SjzbglVO> sjzbglVOList = sjzbglService.getGlAllList(sjzbglVO);
            sjzbglVOList.removeAll(Collections.singleton(null));
            // 佐证材料
            List<ZzclglVO> zzclglVOList = zzclglService.getzzclList(mainBbIdByBgId, list);
            zzclglVOList.removeAll(Collections.singleton(null));
            // 备查材料
            List<ZcclglVO> zcclglVOList = zcclglService.getzcclList(mainBbIdByBgId, list);
            zcclglVOList.removeAll(Collections.singleton(null));

            List<String> resultParts = new ArrayList<>();

            if (!sjzbglVOList.isEmpty()) {
                resultParts.add("【引用数据】：" + sjzbglVOList.size() + "\n");
            }

            if (!zzclglVOList.isEmpty()) {
                resultParts.add("【佐证材料】：" + zzclglVOList.size() + "\n");
            }

            if (!zcclglVOList.isEmpty()) {
                resultParts.add("【备查材料】：" + zcclglVOList.size() + "\n");
            }

            // 6. 合并结果字符串
            StringBuilder finalSb = new StringBuilder();
            for (String part : resultParts) {
                finalSb.append(part);
            }
            return finalSb.toString();
        } else {
            return "";
        }
    }
}
