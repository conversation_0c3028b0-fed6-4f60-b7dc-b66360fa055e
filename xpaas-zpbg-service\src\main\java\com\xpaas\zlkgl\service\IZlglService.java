package com.xpaas.zlkgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;

/**
 * 教学评价-资料库平台-资料管理表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface IZlglService extends BaseService<Zlgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zlgl 教学评价-资料库平台-资料管理表 实体
	 * <AUTHOR>
	 * @since 2025-07-25
	 * @return
	 */
	IPage<ZlglVO> selectZlglPage(IPage<ZlglVO> page, ZlglVO zlgl);
}
