package com.xpaas.zlkgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;

/**
 * 教学评价-资料库平台-资料管理表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface IZlglService extends BaseService<Zlgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zlgl 教学评价-资料库平台-资料管理表 实体
	 * <AUTHOR>
	 * @since 2025-07-25
	 * @return
	 */
	IPage<ZlglVO> selectZlglPage(IPage<ZlglVO> page, ZlglVO zlgl);

	/**
	 * 复制文件
	 *
	 * @param sourceId 源文件ID
	 * @param targetFolderId 目标文件夹ID
	 * @param newName 新名称（可选，为空时自动生成"副本"名称）
	 * @return 复制是否成功
	 */
	boolean copyFile(String sourceId, String targetFolderId, String newName);
}
