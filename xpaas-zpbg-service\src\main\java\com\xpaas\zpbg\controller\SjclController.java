package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Sjcl;
import com.xpaas.zpbg.service.ISjclService;
import com.xpaas.zpbg.service.ISjclssmkService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.SjclVO;
import com.xpaas.zpbg.vo.SjclssmkVO;
import com.xpaas.zpbg.wrapper.SjclWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-数据材料 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sjcl")
@Api(value = "教学评价-自评报告-数据材料", tags = "教学评价-自评报告-数据材料接口")
public class SjclController extends BaseController {

	private SjclWrapper sjclWrapper;
	private ISjclService sjclService;
	private ISjclssmkService sjclssmkService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入sjcl")
	@ApiLog("数据材料-详情")
	public R<SjclVO> detail(Sjcl sjcl) {
		Sjcl detail = sjclService.getOne(Condition.getQueryWrapper(sjcl));
		return R.data(sjclWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-数据材料 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入sjcl")
	@ApiLog("数据材料-分页")
	public R<IPage<SjclVO>> list(Sjcl sjcl, Query query) {
		IPage<Sjcl> pages = sjclService.page(Condition.getPage(query), Condition.getQueryWrapper(sjcl));
		return R.data(sjclWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-数据材料
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入sjcl")
	@ApiLog("数据材料-自定义分页")
    public R<IPage<SjclVO>> page(SjclVO sjcl, Query query) {
        IPage<SjclVO> pages = sjclService.selectSjclPage(Condition.getPage(query), sjcl);
        return R.data(sjclWrapper.wrapperPageVO(pages));
    }



	/**
	 * 新增 教学评价-自评报告-数据材料
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入sjcl")
	@ApiLog("数据材料-新增")
	public R save(@Valid @RequestBody SjclVO sjclVO) {
		boolean b = sjclService.save(sjclVO);

		//数据材料所属模块保存
		SjclssmkVO sjclssmkVO = new SjclssmkVO();
		sjclssmkVO.setMkidList(sjclVO.getMkidList());
		sjclssmkVO.setSjclid(sjclVO.getId());
		sjclssmkService.sjclssmkSave(sjclssmkVO);

		return R.data(String.valueOf(sjclVO.getId()));
	}

	/**
	 * 修改 教学评价-自评报告-数据材料
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入sjcl")
	@ApiLog("数据材料-修改")
	public R update(@Valid @RequestBody SjclVO sjclVO) {
		boolean b = sjclService.updateById(sjclVO);

		//数据材料所属模块保存
		SjclssmkVO sjclssmkVO = new SjclssmkVO();
		sjclssmkVO.setMkidList(sjclVO.getMkidList());
		sjclssmkVO.setSjclid(sjclVO.getId());
		sjclssmkService.sjclssmkSave(sjclssmkVO);

		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-数据材料 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入sjcl")
	@ApiLog("数据材料-新增或修改")
	public R submit(@Valid @RequestBody Sjcl sjcl) {
		return R.status(sjclService.saveOrUpdate(sjcl));
	}

	
	/**
	 * 删除 教学评价-自评报告-数据材料
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("数据材料-逻辑删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = sjclService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("数据材料-高级查询")
	public R<IPage<SjclVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Sjcl> queryWrapper = Condition.getQueryWrapper(map, Sjcl.class);
		IPage<Sjcl> pages = sjclService.page(Condition.getPage(query), queryWrapper);
		return R.data(sjclWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("数据材料-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Sjcl> queryWrapper = Condition.getQueryWrapper(map, Sjcl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Sjcl> list = sjclService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Sjcl.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("数据材料-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Sjcl> list = ExcelUtil.read(file, Sjcl.class);
		//TODO 此处需要根据具体业务添加代码
		sjclService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("数据材料-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Sjcl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Sjcl> list = sjclService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Sjcl导入模板", "Sjcl导入模板",columnFiledNames, list, Sjcl.class);
	}

	/**
	 * 撰写端新增 教学评价-自评报告-数据材料
	 */
	@PostMapping("/saveFromSjzx")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入sjclVO")
	@ApiLog("数据材料-撰写端新增")
	public R saveFromSjzx(@Valid @RequestBody SjclVO sjclVO) {
		boolean b = sjclService.saveFromSjzx(sjclVO);

		return R.data(String.valueOf(sjclVO.getId()));
	}

	/**
	 * 所属模块查询 教学评价-自评报告-数据材料
	 */
	@GetMapping("/selectSjclssmkList")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "所属模块查询", notes = "传入sjclssmkVO")
	@ApiLog("数据材料-所属模块查询")
	public List<SjclssmkVO>  selectSjclssmkList(SjclssmkVO sjclssmkVO) {
		return sjclssmkService.selectSjclssmkList(sjclssmkVO);
	}
}
