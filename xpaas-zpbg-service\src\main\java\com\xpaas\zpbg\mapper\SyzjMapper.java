package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Syzj;
import com.xpaas.zpbg.vo.SyzjVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-审阅专家 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Repository
public interface SyzjMapper extends BaseMapper<Syzj> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param syzj
	 * @return
	 */
	List<SyzjVO> selectSyzjPage(IPage page, SyzjVO syzj);

}
