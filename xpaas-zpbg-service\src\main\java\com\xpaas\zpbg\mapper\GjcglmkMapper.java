package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Gjcglmk;
import com.xpaas.zpbg.vo.GjcglmkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-关键词关联模块 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Repository
public interface GjcglmkMapper extends BaseMapper<Gjcglmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcglmk
	 * @return
	 */
	List<GjcglmkVO> selectGjcglmkPage(IPage page, GjcglmkVO gjcglmk);

	/**
	 * 关键词关联模块查询
	 *
	 * @param gjcglmk
	 * @return
	 */
	List<GjcglmkVO> selectGjcglmkList(GjcglmkVO gjcglmk);

	/**
	 * 关键词关联模块删除
	 *
	 * @param gjcglmk
	 * @return
	 */
	boolean gjcglmkDelete(GjcglmkVO gjcglmk);

	/**
	 * 删除关键词智能检测表中该关键词数据
	 *
	 * @param gjcid
	 * @param mkid
	 * @return
	 */
	boolean removeByGjcidMkid(Long gjcid, Long mkid);

	/**
	 * 复原关键词智能检测表中该关键词数据
	 *
	 * @param gjcid
	 * @param mkid
	 * @return
	 */
	boolean restoreByGjcidMkid(Long gjcid, Long mkid);

}
