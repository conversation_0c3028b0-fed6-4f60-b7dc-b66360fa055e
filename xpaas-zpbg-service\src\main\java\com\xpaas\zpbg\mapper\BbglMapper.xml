<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BbglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bbglResultMap" type="com.xpaas.zpbg.entity.Bbgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BBLX" property="bblx"/>
        <result column="BBMC" property="bbmc"/>
        <result column="BBSM" property="bbsm"/>
        <result column="BBSJ" property="bbsj"/>
        <result column="TJRID" property="tjrid"/>
        <result column="TJR" property="tjr"/>
        <result column="SSJS" property="ssjs"/>
        <result column="BGMKZT" property="bgmkzt"/>
        <result column="BJDZSBC" property="bjdzsbc"/>
        <result column="WJKEY" property="wjkey"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="BBXX" property="bbxx"/>
        <result column="DGBB" property="dgbb"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bbglResultMapVO" type="com.xpaas.zpbg.vo.BbglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="JDMC" property="jdmc"/>
        <result column="BBLX" property="bblx"/>
        <result column="BBMC" property="bbmc"/>
        <result column="BBSM" property="bbsm"/>
        <result column="BBSJ" property="bbsj"/>
        <result column="TJRID" property="tjrid"/>
        <result column="TJR" property="tjr"/>
        <result column="SSJS" property="ssjs"/>
        <result column="BGMKZT" property="bgmkzt"/>
        <result column="BJDZSBC" property="bjdzsbc"/>
        <result column="WJKEY" property="wjkey"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="BBXX" property="bbxx"/>
        <result column="DGBB" property="dgbb"/>
    </resultMap>

    <!-- 版本管理数据查询 -->
    <select id="selectBbglPage" resultMap="bbglResultMapVO">
        select bbgl.ID ,bbgl.BGMKID,bbgl.BBLX,bbgl.BGMKZT,bbgl.JDGLID,jdgl.JDMC,bbgl.BBMC,bbgl.BBSJ,bbgl.TJR,bbgl.SSJS,bbgl.DGBB from T_DT_JXPJ_ZPBG_BBGL bbgl
        left join T_DT_JXPJ_ZPBG_JDGL jdgl on bbgl.JDGLID = jdgl.ID and jdgl.SCBJ = 0
        where bbgl.scbj = 0 and bbgl.bblx = 2
        <if test="bbgl.bgid != null and bbgl.bgid != ''"> and bbgl.bgid = #{bbgl.bgid} </if>
        <if test="bbgl.bbmc != null and bbgl.bbmc != ''"> and bbgl.bbmc like concat('%', #{bbgl.bbmc}, '%') </if>
        <if test="bbgl.jdmc != null and bbgl.jdmc != ''"> and jdgl.jdmc like concat('%', #{bbgl.jdmc}, '%') </if>
        <if test="bbgl.bgmkid != null and bbgl.bgmkid != ''"> and bbgl.bgmkid = #{bbgl.bgmkid} </if>
        ORDER BY bbgl.BBSJ DESC
    </select>

    <!-- 版本管理数据查询 -->
    <select id="listByParams" resultMap="bbglResultMapVO">
        select bbgl.JDGLID,jdgl.JDMC,bbgl.BBMC,bbgl.BBSJ,bbgl.TJR,bbgl.SSJS,bbgl.DGBB from T_DT_JXPJ_ZPBG_BBGL bbgl
        left join T_DT_JXPJ_ZPBG_JDGL jdgl on bbgl.JDGLID = jdgl.ID and jdgl.SCBJ = 0
        where bbgl.scbj = 0 and bbgl.bblx = 2
        <if test="bbgl.bgid != null and bbgl.bgid != ''"> and bbgl.bgid = #{bbgl.bgid} </if>
        and bgmkid in
        <foreach collection="listId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 历史版本数据查询 -->
    <select id="selectLsbbPage" resultMap="bbglResultMapVO">
        select bbgl.ID ,bbgl.BGMKID,bbgl.BBLX,bbgl.BGMKZT,bbgl.JDGLID,jdgl.JDMC,bbgl.BBMC,bbgl.BBSM,bbgl.BBSJ,bbgl.TJR,bbgl.SSJS,bbgl.DGBB from T_DT_JXPJ_ZPBG_BBGL bbgl
        left join T_DT_JXPJ_ZPBG_JDGL jdgl on bbgl.JDGLID = jdgl.ID and jdgl.SCBJ = 0
        where bbgl.scbj = 0
        <if test="bbgl.bgid != null and bbgl.bgid != ''"> and bbgl.bgid = #{bbgl.bgid} </if>
        <if test="bbgl.jdglid != null and bbgl.jdglid != ''"> and bbgl.jdglid = #{bbgl.jdglid} </if>
        <if test="bbgl.bgmkid != null and bbgl.bgmkid != ''"> and bbgl.bgmkid = #{bbgl.bgmkid} </if>
        <if test="bbgl.role != null and (bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@dz
        or bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@zj
        or bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@gly)"> and bbgl.bblx = 2 </if>
        <if test="bbgl.role != null and (bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@zxr
        or bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@zxrOrDz)">
          and (bbgl.bblx = 2 or bbgl.bblx = 3)
        </if>
        ORDER BY bbgl.ID DESC
    </select>

    <!-- 历史版本数据查询 -->
    <select id="selectLsbbjpPage" resultMap="bbglResultMapVO">
        select bbgl.ID ,bbgl.BGMKID,bbgl.BBLX,bbgl.BGMKZT,bbgl.JDGLID,jdgl.JDMC,syzj.ZJXM,syzj.CJRQ,bbgl.BBMC,bbgl.BBSJ,bbgl.TJR,bbgl.SSJS,bbgl.DGBB from T_DT_JXPJ_ZPBG_BBGL bbgl
        left join T_DT_JXPJ_ZPBG_JDGL jdgl on bbgl.JDGLID = jdgl.ID and jdgl.SCBJ = 0
        left join T_DT_JXPJ_ZPBG_SYZJ syzj on bbgl.id = syzj.bbid and syzj.SCBJ =0
        where bbgl.scbj = 0
        <if test="bbgl.bgid != null and bbgl.bgid != ''"> and bbgl.bgid = #{bbgl.bgid} </if>
        <if test="bbgl.jdglid != null and bbgl.jdglid != ''"> and bbgl.jdglid = #{bbgl.jdglid} </if>
        <if test="bbgl.bgmkid != null and bbgl.bgmkid != ''"> and bbgl.bgmkid = #{bbgl.bgmkid} </if>
        <if test="bbgl.role != null and (bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@dz
        or bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@zj
        or bbgl.role == @com.xpaas.zpbg.vo.ZtxxRoleType@gly)"> and bbgl.bblx = 2 </if>
    </select>

    <!-- 历史意见用数据查询 -->
    <select id="getLsyjList" resultMap="bbglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BBGL bbgl
        where bbgl.scbj = 0 and bbgl.bgmkid = #{bgmkid} and bbgl.BBLX =2
        ORDER BY bbgl.CJRQ DESC
    </select>

</mapper>
