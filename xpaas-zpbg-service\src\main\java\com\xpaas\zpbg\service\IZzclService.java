package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料 服务类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface IZzclService extends BaseService<Zzcl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param zzcl
     * @return
     */
    IPage<ZzclVO> selectZzclPage(IPage<ZzclVO> page, ZzclVO zzcl);

    /**
     * 验证材料数据
     *
     * @param zzcl
     * @return
     */
    Boolean checkZzcl(ZzclVO zzcl);

    /**
     * 保存数据
     *
     * @param zzclVO
     * @return
     */
    Boolean saveZZCLSSMK(ZzclVO zzclVO);

    /**
     * 获取材料数据
     *
     * @param zzcl
     * @return
     */
    ZzclVO getZzclDetail(Zzcl zzcl);

    /**
     * 获取材料数据
     *
     * @param zzcl
     * @return
     */
    List<ZzclVO> getZzclId(ZzclVO zzcl);

    /**
     * 获取材料数据
     *
     * @param page
     * @param zzclVO
     * @return
     */
    IPage<ZzclVO> getZzclList(ZzclglVO zzclVO, IPage<ZzclVO> page);

    /**
     * 专家进院接口
     *
     * @param clid
     * @param wjlj
     * @param name
     * @param wjmc
     * @param updataOrsave
     * @return
     */
    void requestZjjyApi(Long clid, String wjlj, String name, String wjmc, int updataOrsave);

    /*
     *获取关联材料
     */
    List<ZzclglVO> getQuote(Zzclgl zzclgl);

    /**
     * 根据老的批次名称和新的批次名称更改佐证材料的批次信息
     * @param oldPcid
     * @param newPcid
     */
    void updateZzclByPcid(Long oldPcid,Long newPcid);

    /**
     * 根据材料名称查询所有的相同名称的材料
     */
    List<ZzclVO> listByClmc(Zzcl zzcl);
}
