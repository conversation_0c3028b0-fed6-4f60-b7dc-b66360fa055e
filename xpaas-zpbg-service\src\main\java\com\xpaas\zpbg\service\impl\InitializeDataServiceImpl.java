package com.xpaas.zpbg.service.impl;

import com.xpaas.core.tool.api.R;
import com.xpaas.elasticsearch.dto.SearchDTO;
import com.xpaas.elasticsearch.feign.IZjjyClient;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.mapper.MkglMapper;
import com.xpaas.zpbg.mapper.ZcclMapper;
import com.xpaas.zpbg.mapper.ZcclssmkMapper;
import com.xpaas.zpbg.mapper.ZzclssmkMapper;
import com.xpaas.zpbg.service.IInitializeDataServiceImpl;
import com.xpaas.zpbg.service.IZzclssmkService;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZcclssmkVO;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclssmkVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-佐证材料 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class InitializeDataServiceImpl  implements IInitializeDataServiceImpl {
    @Autowired
    public IOssClient ossClient;
    @Autowired
    IZzclssmkService iZzclssmkService;
    @Autowired
    ZzclssmkMapper zzclssmkMapper;

    @Autowired
    MkglMapper mkglMapper;

    @Autowired
    ZcclMapper zcclMapper;

    @Autowired
    IZjjyClient zjjyClient;

    @Autowired
    ZcclssmkMapper zcclssmkMapper;


    @Override
    public boolean setData(List<ZzclVO> zzList, List<ZcclVO> zcList){
        List<SearchDTO> searchList = new ArrayList<>();
        if(zzList.size()>0){
            for (ZzclVO item:zzList) {
                SearchDTO  searchDTO = getZzData(item.getId(), item.getWjlj(), item.getCmm(),item.getClmc());
                searchList.add(searchDTO);
            }
        }
        if(zcList.size()>0){
            for (ZcclVO item:zcList) {
                SearchDTO  searchDTO = getZcData(item.getId(), item.getWjlj(), item.getClmc(),item.getClmc());
                searchList.add(searchDTO);
            }
        }

        zjjyClient.createDocuments(searchList);
        return true;
    }
    /*
     * 获取佐证材料数据
     */
    public SearchDTO getZzData(Long clid,String wjlj,String name,String wjmc){
        String pdfData = "";
        List<String> yjzbidList = new ArrayList<>();
        List<String> ejzbidList  = new ArrayList<>();
        List<String> zygzjList  = new ArrayList<>();
        List<String> yjzbname = new ArrayList<>();
        List<String> ejzbname  = new ArrayList<>();
        SearchDTO searchDTO = new SearchDTO();
        //材料id
        searchDTO.setId(clid.toString());
        // 文件路径
        searchDTO.setUrl(wjlj);
        // 3佐证材料
        searchDTO.setLx(3);
        //标题
        searchDTO.setTitle(name);
        if(!"".equals(wjlj)){

            pdfData = zjjyESApi(wjlj);
            // 内容
            searchDTO.setIntro(pdfData);
        }
        List<ZzclssmkVO> zzclssmkVOList = zzclssmkMapper.selectZzclssmkList(clid);
        if(zzclssmkVOList.size()>0){
            List<String> idlist = new ArrayList();
            for (ZzclssmkVO zzclssmkVO : zzclssmkVOList) {
                idlist.add(zzclssmkVO.getMkid().toString());
            }
            List<Mkgl> mkglList =  mkglMapper.getMkglIdDate(idlist);
            if(mkglList.size()>0){
                for (Mkgl mkgl : mkglList) {
                    yjzbidList.add(mkgl.getYjzbid()==null?"":mkgl.getYjzbid().toString());
                    ejzbidList.add(mkgl.getEjzbid()==null?"":mkgl.getEjzbid().toString());
                    yjzbname.add(mkgl.getYjzb()==null?"":mkgl.getYjzb());
                    ejzbname.add(mkgl.getEjzb()==null?"":mkgl.getEjzb());
                    zygzjList.add(mkgl.getBztxid()==null?"":mkgl.getBztxid().toString());
                }
                List<String> zygzdNamelist = zcclMapper.getZygzdName(zygzjList);
                searchDTO.setYjzbid( String.join(",", yjzbidList));
                searchDTO.setEjzbid( String.join(",", ejzbidList));
                searchDTO.setZygzdid(String.join(",", zygzjList));
                searchDTO.setYjzbmc(String.join(",", yjzbname));
                searchDTO.setEjzbmc(String.join(",", ejzbname));
                searchDTO.setZygzdmc(String.join(",", zygzdNamelist));
                searchDTO.setWjmc(wjmc);
            }

        }
        return  searchDTO;
    }
    /*
    * 获取备查材料数据
    */
    public SearchDTO getZcData(Long clid,String wjlj,String name,String wjmc){
        String pdfData = "";
        List<String> yjzbidList = new ArrayList<>();
        List<String> ejzbidList  = new ArrayList<>();
        List<String> zygzjList  = new ArrayList<>();
        List<String> yjzbname = new ArrayList<>();
        List<String> ejzbname  = new ArrayList<>();
        SearchDTO searchDTO = new SearchDTO();
        //材料id
        searchDTO.setId(clid.toString());
        // 文件路径
        searchDTO.setUrl(wjlj);
        // 4备查材料
        searchDTO.setLx(4);
        //标题
        searchDTO.setTitle(name);
        if(!"".equals(wjlj)){
            pdfData = zjjyESApi(wjlj);
            // 内容
            searchDTO.setIntro(pdfData);
        }
        List<ZcclssmkVO> zcclssmkVOList = zcclssmkMapper.selectZcclssmkList(clid);
        if(zcclssmkVOList.size()>0){
            List<String> idlist = new ArrayList();
            for (ZcclssmkVO zcclssmkVO : zcclssmkVOList) {
                idlist.add(zcclssmkVO.getMkid().toString());
            }
            List<Mkgl> mkglList =  mkglMapper.getMkglIdDate(idlist);
            if(mkglList.size()>0){
                for (Mkgl mkgl : mkglList) {
                    yjzbidList.add(mkgl.getYjzbid()==null?"":mkgl.getYjzbid().toString());
                    yjzbname.add(mkgl.getYjzb()==null?"":mkgl.getYjzb());
                    ejzbidList.add(mkgl.getEjzbid()==null?"":mkgl.getEjzbid().toString());
                    ejzbname.add(mkgl.getEjzb()==null?"":mkgl.getEjzb());
                    zygzjList.add(mkgl.getBztxid()==null?"":mkgl.getBztxid().toString());
                }
                List<String> zygzdNamelist = zcclMapper.getZygzdName(zygzjList);
                searchDTO.setYjzbid( String.join(",", yjzbidList));
                searchDTO.setEjzbid( String.join(",", ejzbidList));
                searchDTO.setZygzdid(String.join(",", zygzjList));
                searchDTO.setYjzbmc(String.join(",", yjzbname));
                searchDTO.setEjzbmc(String.join(",", ejzbname));
                searchDTO.setZygzdmc(String.join(",", zygzdNamelist));
                searchDTO.setWjmc(wjmc);
            }


        }
        return  searchDTO;
    }

    /**
     * 读取PDF文件
     */
    public String zjjyESApi(String wjlj) {
        //读取pdf文件
        try {
            String pdfBody = "";
            R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj, "minio11");
            if (inputR.isSuccess()) {
                log.info("取得文件流成功");
                byte[] bytes = inputR.getData();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                PDDocument document = PDDocument.load(inputStream);
                // 获取文档的目录
                PDDocumentCatalog catalog = document.getDocumentCatalog();
                // 获取文档的所有页面
                PDPageTree pages = catalog.getPages();
                for (int i = 1; i <= pages.getCount(); i++) {
                    PDFTextStripper stripper = new PDFTextStripper();
                    stripper.setStartPage(i);
                    stripper.setEndPage(i);
                    String content = stripper.getText(document);
                    content = content.replaceAll(" ", "").replaceAll(" +", "").replaceAll("[\\t\\n\\r]", "");
                    pdfBody += content;
                }
            }
            return pdfBody;
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }
}
