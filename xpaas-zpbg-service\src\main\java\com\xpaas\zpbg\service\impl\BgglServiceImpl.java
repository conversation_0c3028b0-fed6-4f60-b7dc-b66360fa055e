package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseEntity;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.core.tool.utils.DateUtil;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.system.feign.IParamClient;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.mapper.BgglMapper;
import com.xpaas.zpbg.service.*;
import com.xpaas.zpbg.vo.BgglVO;
import com.xpaas.zpbg.vo.BgjdVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 教学评价-自评报告-报告管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@Service
public class BgglServiceImpl extends BaseServiceImpl<BgglMapper, Bggl> implements IBgglService {

	@Autowired
	private IMkglService mkglService;
	@Autowired
	private IBgmkService bgmkService;
	@Autowired
	private IJdmkglService jdmkglService;
	@Autowired
	private IBbglService bbglService;

	@Autowired
	private BgglMapper bgglMapper;

	@Autowired
	IParamClient paramClient;

	// 教学评价-超级管理员-角色ID
	@Value("${xpaas.roles.adminRoleId}")
	private String[] adminRoleId;

	// # 自评报告撰写平台-教评中心管理员-角色ID
	@Value("${xpaas.roles.zpbgzxptGlyRoleId}")
	private String[] zpbgzxptGlyRoleId;

	// 主要关注点负责人-角色ID
	@Value("${xpaas.roles.zygzdfzrRoleId}")
	private String[] zygzdfzrRoleId;

	/**
	 * 自定义分页
	 */
	@Override
	public IPage<BgglVO> selectBgglPage(IPage<BgglVO> page, BgglVO bggl) {
		return page.setRecords(baseMapper.selectBgglPage(page, bggl));
	}

	/**
	 *查询报告进度信息
	 */
	@Override
	public Map<String, Object> selectBgjd(Bggl bggl){
		Map<String, Object> ret = new HashMap<String, Object>();
		List<BgjdVO> list = bgglMapper.selectBgjd(bggl);
		for (BgjdVO bgjdVO : list) {
			// 根据报告模块状态判断是否显示展示按钮
			LambdaQueryWrapper<Bgmk> wrapper = new LambdaQueryWrapper<Bgmk>();
			wrapper.eq(Bgmk::getBgid, bgjdVO.getId());
			wrapper.eq(Bgmk::getBgmkzt, 90);
			List<Bgmk> res = bgmkService.list(wrapper);
			if (res == null || res.size() == 0) {
				bgjdVO.setSfzsShowFlg(false);
			} else {
				bgjdVO.setSfzsShowFlg(true);
			}

			// 根据报告id判断是否已展示
			LambdaQueryWrapper<Bggl> wrapperBggl = new LambdaQueryWrapper<Bggl>();
			wrapperBggl.eq(Bggl::getId, bgjdVO.getId());
			Bggl resBggl = getOne(wrapperBggl);
			if (resBggl != null) {
				bgjdVO.setBgzt(resBggl.getBgzt());
			}
		}

		ret.put("list", list);
		ret.put("roleType", getRoleType());

		return ret;
	}

	/**
	 *新建报告管理、版本管理和报告模块
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBgAndMk(BgglVO bgglVO) {
		Integer count = baseMapper.selectCount(new LambdaQueryWrapper<Bggl>().eq(Bggl::getBgmc, bgglVO.getBgmc()).eq(Bggl::getRwlx, bgglVO.getRwlx()).eq(Bggl::getNd, bgglVO.getNd()));
		if(count>0){
			throw new ServiceException("相同年度任务类型报告已存在");
		}
		bgglVO.setId(IdWorker.getId(bgglVO));
		this.resolveEntity(bgglVO);

		QueryWrapper<Mkgl> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("MKLX",bgglVO.getMklx());
		List<Mkgl> mkglList = mkglService.list(queryWrapper);
		List<Bgmk> bgmkList = new ArrayList<>();
		List<Bbgl> bbglList = new ArrayList<>();

		LoginUser loginUser = AuthUtil.getUser();

		String wjljDefult = paramClient.getByParamKey("env.onlyOfficeEmptyTemplate").getData().getParamValue();
		for(Mkgl mkgl : mkglList){
			Bgmk bgmk = new Bgmk();
			bgmk.setId(IdWorker.getId(bgmk));
			this.resolveEntity(bgmk);

			Bbgl bbgl = new Bbgl();
			bbgl.setId(IdWorker.getId(bbgl));
			this.resolveEntity(bbgl);

			// 报告模块赋值
			bgmk.setBgid(bgglVO.getId());
			bgmk.setMkid(mkgl.getId());
			bgmk.setMkmc(mkgl.getMkmc());
			bgmk.setMklx(bgglVO.getMklx());
			bgmk.setCmm(mkgl.getCmm());
			bgmk.setSfwfm(mkgl.getSfwfm());
			bgmk.setZbly(mkgl.getZbly());
			bgmk.setYjzbid(mkgl.getYjzbid());
			bgmk.setYjzb(mkgl.getYjzb());
			bgmk.setEjzbid(mkgl.getEjzbid());
			bgmk.setEjzb(mkgl.getEjzb());
			bgmk.setPx(mkgl.getPx());
			bgmk.setSjly(mkgl.getSjly());
			bgmk.setMbwjkey(mkgl.getMbwjkey());
			bgmk.setMbwjlj(mkgl.getMbwjlj());
			bgmk.setBgmkzt(0);
			bgmkList.add(bgmk);

			// 版本文件路径
			String wjlj= mkgl.getMbwjlj();
			if (StringUtil.isBlank(wjlj)){
				wjlj = wjljDefult;
			}

			// 版本管理赋值
			bbgl.setBgid(bgglVO.getId());
			bbgl.setBgmkid(bgmk.getId());
			bbgl.setJdglid(null);
			bbgl.setBblx(1);
			bbgl.setBbmc("当前版本");
			bbgl.setBbsm(null);
			bbgl.setBbsj(new Date());
			bbgl.setTjrid(loginUser.getUserId());
			bbgl.setTjr(loginUser.getUserName());
			bbgl.setSsjs(1);
			bbgl.setBgmkzt(0);
			bbgl.setBjdzsbc(1);
			bbgl.setWjkey(UUID.randomUUID().toString());
			bbgl.setWjlj(wjlj);
			bbgl.setBbxx(null);
			bbglList.add(bbgl);

		}
		boolean b1 = bgmkService.saveBatch(bgmkList);
		boolean b2 = bbglService.saveBatch(bbglList);

		return this.save(bgglVO);
	}

	private void resolveEntity(BaseEntity entity){
		LoginUser user = AuthUtil.getUser();
		Date now = DateUtil.now();
		if (user != null) {
			entity.setCjr(user.getUserId());
			entity.setCjbm((Long) Func.toLongList(user.getDeptId()).iterator().next());
			entity.setCjdw(user.getUnitId());
			entity.setGxr(user.getUserId());
		}

		if (entity.getZt() == null) {
			entity.setZt(1);
		}

		entity.setCjrq(now);
	}

	/**
	 *报告查询
	 */
	@Override
	public List<BgglVO> searchBggl(BgglVO bgglVO){
		return bgglMapper.searchBggl(bgglVO);
	}

	private int getRoleType() {
		LoginUser user = AuthUtil.getUser();

		// admin
		if("admin".equals(user.getAccount()) || "admin_root".equals(user.getAccount())) {
			return 1;
		}

		// 超级管理员
		if(checkRole(adminRoleId, user.getRoleId()) == true) {
			return 1;
		}

		// 教评中心管理员
		if(checkRole(zpbgzxptGlyRoleId, user.getRoleId()) == true) {
			return 1;
		}

		// 主要关注点负责人
		if(checkRole(zygzdfzrRoleId, user.getRoleId()) == true) {
			return 2;
		}

		return 9;
	}

	private boolean checkRole(String[] roleIds, String myRoleIds) {
		if(StringUtil.isNotBlank(myRoleIds)) {
			List<String> myRoleList = Arrays.asList(myRoleIds.split(","));

			for(int i = 0; i < roleIds.length; i++) {
				if(myRoleList.indexOf(roleIds[i]) >= 0) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 同步报告管理、版本管理和报告模块
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void syncBgAndMk(Long bgglId){
		Bggl bggl = baseMapper.selectById(bgglId);
		if (bggl == null) {
			throw new ServiceException("报告不存在");
		}
		LoginUser loginUser = AuthUtil.getUser();
		//模版文件
		String wjljDefult = paramClient.getByParamKey("env.onlyOfficeEmptyTemplate").getData().getParamValue();
		//模块列表
		Map<Long, Mkgl> mkglMap = mkglService.list(Wrappers.lambdaQuery(Mkgl.class).eq(Mkgl::getMklx, bggl.getMklx())).stream().collect(Collectors.toMap(Mkgl::getId, item -> item));
		//报告模块列表
		Map<Long, Bgmk> bgmkMap = bgmkService.list(Wrappers.lambdaQuery(Bgmk.class).eq(Bgmk::getBgid, bggl.getId())).stream().collect(Collectors.toMap(Bgmk::getMkid, item -> item));
		//模块列表∩报告模块列表 更新
		Set<Long> updateSet = mkglMap.keySet().stream().filter(bgmkMap::containsKey).collect(Collectors.toSet());
		for (Long id : updateSet) {
			Bgmk bgmk = bgmkMap.get(id);
			Mkgl mkgl = mkglMap.get(id);
			if (!Objects.equals(mkgl.getMkmc(), bgmk.getMkmc()) || !Objects.equals(mkgl.getCmm(), bgmk.getCmm()) || !Objects.equals(mkgl.getPx(), bgmk.getPx())) {
				bgmk.setMkmc(mkgl.getMkmc());
				bgmk.setCmm(mkgl.getCmm());
				bgmk.setPx(mkgl.getPx());
				bgmkService.updateById(bgmk);
			}
		}
		//模块列表-报告模块列表 新增
		Set<Long> addSet = mkglMap.keySet().stream().filter(key -> !bgmkMap.containsKey(key)).collect(Collectors.toSet());
		for (Long id : addSet) {
			Mkgl mkgl = mkglMap.get(id);
			Bgmk bgmk = new Bgmk();
			BeanUtil.copy(mkgl, bgmk);
			bgmk.setZhid(null);
			bgmk.setId(IdWorker.getId(bgmk));
			bgmk.setBgid(bggl.getId());
			bgmk.setMkid(mkgl.getId());
			bgmk.setBgmkzt(0);
			this.resolveEntity(bgmk);
			bgmkService.save(bgmk);

			Bbgl bbgl = new Bbgl();
			bbgl.setId(IdWorker.getId(bbgl));
			this.resolveEntity(bbgl);
			// 版本文件路径
			String wjlj = mkgl.getMbwjlj();
			if (StringUtil.isBlank(wjlj)) {
				wjlj = wjljDefult;
			}
			bbgl.setBgid(bggl.getId());
			bbgl.setBgmkid(bgmk.getId());
			bbgl.setJdglid(null);
			bbgl.setBblx(1);
			bbgl.setBbmc("当前版本");
			bbgl.setBbsm(null);
			bbgl.setBbsj(new Date());
			bbgl.setTjrid(loginUser.getUserId());
			bbgl.setTjr(loginUser.getUserName());
			bbgl.setSsjs(1);
			bbgl.setBgmkzt(0);
			bbgl.setBjdzsbc(1);
			bbgl.setWjkey(UUID.randomUUID().toString());
			bbgl.setWjlj(wjlj);
			bbgl.setBbxx(null);
			bbglService.save(bbgl);
		}
		//报告模块列表-模块列表 删除
		Set<Long> delSet = bgmkMap.keySet().stream().filter(key -> !mkglMap.containsKey(key)).collect(Collectors.toSet());
		for (Long id : delSet) {
			bgmkService.removeById(id);
		}
	}
}
