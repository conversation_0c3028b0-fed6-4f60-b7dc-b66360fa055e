package com.xpaas.zpbg.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.tool.api.R;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.zpbg.entity.Bbwjnr;
import com.xpaas.zpbg.mapper.BbwjnrMapper;
import com.xpaas.zpbg.service.IBbwjnrService;
import com.xpaas.zpbg.vo.BbwjnrVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 教学评价-自评报告-版本文件内容 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class BbwjnrServiceImpl extends BaseServiceImpl<BbwjnrMapper, Bbwjnr> implements IBbwjnrService {

	@Autowired
	public IOssClient client;

	@Override
	public IPage<BbwjnrVO> selectBbwjnrPage(IPage<BbwjnrVO> page, BbwjnrVO bbwjnr) {
		return page.setRecords(baseMapper.selectBbwjnrPage(page, bbwjnr));
	}

	@Override
	public void saveWjnr(Long bbid, String wjlj) {
		try {
			R<byte[]> inputR = client.getFileBuffer("/" + wjlj,"minio11");
			if (inputR.isSuccess()) {
				log.info("取得文件流成功");
				byte[] bytes = inputR.getData();
				InputStream inputStream = new ByteArrayInputStream(bytes);
				XWPFDocument document = new XWPFDocument(inputStream);
				String wjnr = "";

				for (XWPFParagraph p : document.getParagraphs()) {
					wjnr += p.getText();
				}

				Bbwjnr bbwjnr = baseMapper.getByBbid(bbid);
				if(bbwjnr == null) {
					bbwjnr = new Bbwjnr();
					bbwjnr.setBbid(bbid);
					bbwjnr.setWjnr(wjnr);
					super.save(bbwjnr);
				} else {
					bbwjnr.setWjnr(wjnr);
					super.updateById(bbwjnr);
				}
			} else {
				log.info("取得文件流失败");
				log.info(JSON.toJSONString(inputR));

				throw new ServiceException("文件服务器下载文件失败");
			}
		} catch (IOException e) {
			throw new ServiceException("文件读取失败");
		}
	}

}
