package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.system.cache.DeptCache;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.mapper.*;
import com.xpaas.zpbg.service.*;
import com.xpaas.zpbg.vo.XxfbUserVO;
import com.xpaas.zpbg.vo.ZtProcInfoVO;
import com.xpaas.zpbg.vo.ZtProcStatus;
import com.xpaas.zpbg.vo.ZtProcType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教学评价-自评报告-状态管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class ZtglServiceImpl implements IZtglService {

    @Autowired
    ZtglMapper ztglMapper;
    @Autowired
    BgglMapper bgglMapper;
    @Autowired
    BbglMapper bbglMapper;
    @Autowired
    BgmkMapper bgmkMapper;
    @Autowired
    JdglMapper jdglMapper;
    @Autowired
    JdmkglMapper jdmkglMapper;
    @Autowired
    RwfgMapper rwfgMapper;
    @Autowired
    SyzjMapper syzjMapper;
    @Autowired
    BgmkztjlMapper bgmkztjlMapper;


    @Autowired
    IBgmkztjlService bgmkztjlService;
    @Autowired
    IBbglService bbglService;
    @Autowired
    ISyzjService syzjService;
    @Autowired
    IZjyjService zjyjService;
    @Autowired
    ISjzbglService sjzbglService;
    @Autowired
    ISjbglService sjbglService;
    @Autowired
    IZzclglService zzclglService;
    @Autowired
    IZcclglService zcclglService;
    @Autowired
    IGjcznjcService gjcznjcService;
    @Autowired
    IBgmkczjlService bgmkczjlService;
    @Autowired
    IBbpzService bbpzService;
    @Autowired
    IXxfbService xxfbService;

    @Resource
    private IUserClient userClient;

    /////////////////////////////////////////////////////////////////
    // 加锁处理
    /////////////////////////////////////////////////////////////

    /**
     * 【通用处理】加锁报告记录
     */
    @Override
    public void lockBg(Long bgid) {
        int ret = ztglMapper.lockBg(bgid);
        if (ret != 1) {
            throw new ServiceException("指定的报告不存在");
        }
    }


    /////////////////////////////////////////////////////////////////
    // 报告变化处理、日期更替处理 - 公共
    /////////////////////////////////////////////////////////////////

    /**
     * 【场景处理】报告创建、报告进度维护
     */
    @Override
    public void whileJdChange(Long bgid) {
        // 【加锁】加锁报告记录，应由调用者调用
        // 应由调用者调用 lockBg(bgid);

        // 【轮次更新】更新报告、各模块的主线版本当前进度ID
        procJdCalcBg(bgid);

        // 【状态更新】更新模块、主线版本状态
        procZtCalcBg(bgid);
    }


    /**
     * 【场景处理】日期更替，活跃报告ID获取
     */
    @Override
    public List<Long> whileDayChangeBgidList() {
        return ztglMapper.selectBgidList();
    }

    /**
     * 【场景处理】日期更替，报告状态重算处理
     */
    @Override
    @Transactional
    public void whileDayChange(Long bgid) {
        // 【加锁】加锁报告记录
        lockBg(bgid);

        // 【轮次更新】更新报告、各模块的主线版本当前进度ID
        procJdCalcBg(bgid);

        // 【状态更新】更新模块、主线版本状态
        procZtCalcBg(bgid);
    }

    /**
     * 【场景处理】日期更替，消息发送处理
     */
    @Override
    public void whileDayChangeMessage() {

        List<Map<String, Object>> dataList;
        List<XxfbUserVO> userList = new ArrayList<>();
        Long lastBbid = -999L;
        // 1.
        // 超管创建新进度，时间达到开始时间
        // 该进度下关联的所有模块的点长和撰写人都收到消息
        // 管理员发布了一条自评报告撰写任务，请您及时处理。
        dataList = ztglMapper.selectMessageZxks();
        if (dataList != null) {
            userList.clear();
            lastBbid = -999L;

            for (Map<String, Object> map : dataList) {
                Long bbid = MapUtils.getLong(map, "bbid");
                if (!bbid.equals(lastBbid)) {
                    if (!userList.isEmpty()) {
                        xxfbService.sendMessageYw(
                                "管理员发布了一条自评报告撰写任务，请您及时处理。",
                                "zxrOrDz",
                                userList,
                                "撰写人或点长",
                                lastBbid);
                        userList.clear();
                    }
                }
                String userId = MapUtils.getString(map, "userId");
                XxfbUserVO user = new XxfbUserVO();
                userList.add(user);
                user.setUserId(userId);
                lastBbid = bbid;
            }
            if (!userList.isEmpty()) {
                xxfbService.sendMessageYw(
                        "管理员发布了一条自评报告撰写任务，请您及时处理。",
                        "zxrOrDz",
                        userList,
                        "撰写人或点长",
                        lastBbid);
                userList.clear();
            }
        }

        // 2.
        // 每轮进度下，距离撰写结束时间还有1天、2天时
        // 该进度下未提交的点长和撰写人收到消息
        // 1.1.1内涵内容自评报告距离撰写结束还有XX天，请您及时处理。
        dataList = ztglMapper.selectMessageZxjs();
        if (dataList != null) {
            userList.clear();
            lastBbid = -999L;
            String mkmc = null;
            int diffDay = 1;
            for (Map<String, Object> map : dataList) {
                Long bbid = MapUtils.getLong(map, "bbid");

                if (!bbid.equals(lastBbid)) {
                    if (!userList.isEmpty()) {
                        xxfbService.sendMessageYw(
                                mkmc + "自评报告距离撰写结束还有" + String.valueOf(diffDay) + "天，请您及时处理。",
                                "zxrOrDz",
                                userList,
                                "撰写人或点长",
                                lastBbid);
                        userList.clear();
                    }

                    mkmc = MapUtils.getString(map, "mkmc");
                    String cmm = MapUtils.getString(map, "cmm");
                    if (StringUtils.isNotBlank(cmm)) {
                        mkmc = cmm;
                    }
                    diffDay = 1;
                    Date zxjssj = trancateDay((Date) MapUtils.getObject(map, "zxjssj"));
                    Date today = trancateDay(new Date());
                    if (zxjssj.getTime() - today.getTime() > 3600 * 1000) {
                        diffDay = 2;
                    }
                }
                String userId = MapUtils.getString(map, "userId");
                XxfbUserVO user = new XxfbUserVO();
                userList.add(user);
                user.setUserId(userId);
                lastBbid = bbid;
            }
            if (!userList.isEmpty()) {
                xxfbService.sendMessageYw(
                        mkmc + "自评报告距离撰写结束还有" + String.valueOf(diffDay) + "天，请您及时处理。",
                        "zxrOrDz",
                        userList,
                        "撰写人或点长",
                        lastBbid);
                userList.clear();
            }
        }

        // 3.
        // 每轮进度下，晚于撰写结束时间未提交的模块
        // 该进度下，晚于撰写结束时间未提交模块的点长和撰写人收到消息
        // 1.1.1内涵内容自评报告撰写任务已延期，请您及时完成报告的提交。
        dataList = ztglMapper.selectMessageZxtj(); // 为啥要发给撰写人..TODO
        if (dataList != null) {
            userList.clear();
            lastBbid = -999L;
            String mkmc = null;
            for (Map<String, Object> map : dataList) {
                Long bbid = MapUtils.getLong(map, "bbid");

                if (!bbid.equals(lastBbid)) {
                    if (!userList.isEmpty()) {
                        xxfbService.sendMessageYw(
                                mkmc + "自评报告撰写任务已延期，请您及时完成报告的提交。",
                                "zxrOrDz",
                                userList,
                                "撰写人或点长",
                                lastBbid);
                        userList.clear();
                    }

                    mkmc = MapUtils.getString(map, "mkmc");
                    String cmm = MapUtils.getString(map, "cmm");
                    if (StringUtils.isNotBlank(cmm)) {
                        mkmc = cmm;
                    }
                }
                String userId = MapUtils.getString(map, "userId");
                XxfbUserVO user = new XxfbUserVO();
                userList.add(user);
                user.setUserId(userId);
                lastBbid = bbid;
            }
            if (!userList.isEmpty()) {
                xxfbService.sendMessageYw(
                        mkmc + "自评报告撰写任务已延期，请您及时完成报告的提交。",
                        "zxrOrDz",
                        userList,
                        "撰写人或点长",
                        lastBbid);
                userList.clear();
            }
        }
        // 1.1.1内涵内容自评报告撰写任务已延期。
        dataList = ztglMapper.selectMessageZxtjGly();
        if (dataList != null) {
            for (Map<String, Object> map : dataList) {
                String mkmc = null;
                mkmc = MapUtils.getString(map, "mkmc");
                String cmm = MapUtils.getString(map, "cmm");
                if (StringUtils.isNotBlank(cmm)) {
                    mkmc = cmm;
                }
                xxfbService.sendMessageJp(mkmc + "自评报告撰写任务已延期");
            }
        }
    }


    //---------------------------------------------------------------
    // 报告变化处理、日期更替处理 - 内部处理
    //---------------------------------------------------------------

    /**
     * 【轮次更新】重算并更新报告、各模块的主线版本当前进度ID
     */
    private void procJdCalcBg(Long bgid) {
        // 获取报告当前进度管理ID
        Long jdglid = ztglMapper.selectCurJdglid(bgid);

        // 更新报告进度管理ID
        ztglMapper.updateBgJdglid(bgid, jdglid);
        // 更新报告各模块主线版本进度管理ID
        ztglMapper.updateBbJdglid(bgid, jdglid);
    }

    /**
     * 【状态更新】重算并更新模块、主线版本状态 - 报告级
     */
    private void procZtCalcBg(Long bgid) {

        // 统一初始化报告模块
        LambdaUpdateWrapper<Bgmk> bgmkUpdWrapper = new LambdaUpdateWrapper<>();
        bgmkUpdWrapper
                .eq(Bgmk::getBgid, bgid)
                //.eq(Bgmk::getScbj, 0)
                .set(Bgmk::getBgmkzt, 0); // 状态
        bgmkMapper.update(null, bgmkUpdWrapper);
        // 统一初始化版本
        LambdaUpdateWrapper<Bbgl> bbglUpdWrapper = new LambdaUpdateWrapper<>();
        bbglUpdWrapper
                .eq(Bbgl::getBgid, bgid)
                .eq(Bbgl::getBblx, 1)
                //.eq(Bbgl::getScbj, 0)
                .set(Bbgl::getBgmkzt, 0) // 状态
                .set(Bbgl::getBjdzsbc, 1); // 版次
        bbglMapper.update(null, bbglUpdWrapper);


        // 获取操作记录，按报告或版本,掺如了各进度开始点
        List<Map<String, Object>> ztjlListDb = ztglMapper.selectZtjl(bgid, null);
        if (ztjlListDb == null || ztjlListDb.isEmpty()) {
            return;
        }

        // 按报告模块拆组
        List<List<Map<String, Object>>> ztjlLists = new ArrayList<>();
        List<Map<String, Object>> curZtjlList = null;
        Long curBgmkid = -999L;
        for (Map<String, Object> item : ztjlListDb) {
            Long bgmkid = MapUtils.getLong(item, "bgmkid");
            if (!curBgmkid.equals(bgmkid)) {
                curBgmkid = bgmkid;
                curZtjlList = new ArrayList<>();
                ztjlLists.add(curZtjlList);
            }
            curZtjlList.add(item);
        }

        // 处理各模块
        for (List<Map<String, Object>> ztjlList : ztjlLists) {
            // 处理模块
            procZtCalcUpdateBgmkBbStatus(ztjlList, null);
        }

    }

    /**
     * 【状态更新】重算并更新模块、主线版本状态 - 版本级（用于“版本各场景操作处理”）
     */
    private void procZtCalcBb(Long bgid, Long bbid, ZtglStatusContext ctx) {
        // 获取操作记录，按报告或版本,掺如了各进度开始点
        List<Map<String, Object>> ztjlListDb = ztglMapper.selectZtjl(bgid, bbid);
        if (ztjlListDb == null || ztjlListDb.isEmpty()) {
            return;
        }

        // 处理模块
        procZtCalcUpdateBgmkBbStatus(ztjlListDb, ctx);
    }

    /**
     * 计算并更新 某模块及主线版本 当前 报告模块状态 和 撰审版次
     */
    private void procZtCalcUpdateBgmkBbStatus(List<Map<String, Object>> ztjlList, ZtglStatusContext ctx) {
        Long bgmkid = null;
        Long bbid = null;
        ZtProcStatus procStatus = ZtProcStatus.WKS0; // 模块状态枚举

        ZtProcType sbProcType = null; // 升版触发处理
        int bjdzsbc = 1; // 本轮次撰审版次

        Date zxjssj = null; // 最后一轮撰写结束时间
        int tjcs = 0; // 本轮次撰写提交次数;

        // 获取当前状态
        for (Map<String, Object> ztjl : ztjlList) {
            log.info(ztjl.toString());
            bgmkid = MapUtils.getLong(ztjl, "bgmkid");
            bbid = MapUtils.getLong(ztjl, "bbid");

            Date sj = (Date) MapUtils.getObject(ztjl, "sj");
            int czlx = MapUtils.getInteger(ztjl, "czlx");

            zxjssj = nextDay((Date) MapUtils.getObject(ztjl, "zxjssj"));

            // 切进度轮次处理
            if (czlx < 0) {
                // 若是进度轮次切换
                sbProcType = null; // 本轮次升版触发处理 - 复原
                bjdzsbc = 1; // 本轮次撰审版次 - 复原
                tjcs = 0; // 本轮次撰写提交次数 - 复原;
                continue;
            }

            // 获取操作结果字典
            ZtProcType procType = ZtProcType.fromValue(czlx);
            ZtglProcResult procResult = ZtglStatusDic.getProcResult(procType);
            if (procResult == null) {
                throw new ServiceException("不支持的操作");
            }

            // 处理后状态
            procStatus = procResult.procStatus;

            // 本批次升版触发处理
            if (sbProcType == null && procResult.sbProcType != null) {
                sbProcType = procResult.sbProcType;
            }

            // 本轮次撰审版次
            if (sbProcType != null && procType == sbProcType) {
                bjdzsbc++;
            }

            // 本轮次撰写提交次数
            if (procType == ZtProcType.DZTJ20) {
                tjcs++;
            }

            // 是否延期
            if (tjcs == 0 && procStatus == ZtProcStatus.ZXZ10 && zxjssj != null) {
                if (sj.getTime() >= zxjssj.getTime()) {
                    procStatus = ZtProcStatus.YYQ20;
                }
            }
            log.info("#####报告模块ID：" + bgmkid + " 版本ID：" + bbid + " 操作：" + procType.toString() + " 状态：" + procStatus.toString() + " 撰审版次：" + bjdzsbc + " 提交次数：" + tjcs);
        }

        // 记录新的状态
        if (ctx != null) {
            ctx.newBjdzsbc = bjdzsbc;
            ctx.newProcStatus = procStatus;
        }

        // 更新数据库
        if (bgmkid != null) {
            LambdaUpdateWrapper<Bgmk> bgmkUpdWrapper = new LambdaUpdateWrapper<>();
            bgmkUpdWrapper
                    .eq(Bgmk::getId, bgmkid)
                    .set(Bgmk::getBgmkzt, procStatus.getValue()); // 状态
            bgmkMapper.update(null, bgmkUpdWrapper);
        }
        if (bbid != null) {
            LambdaUpdateWrapper<Bbgl> bbglUpdWrapper = new LambdaUpdateWrapper<>();
            bbglUpdWrapper
                    .eq(Bbgl::getId, bbid)
                    .set(Bbgl::getBgmkzt, procStatus.getValue()) // 状态
                    .set(Bbgl::getBjdzsbc, bjdzsbc); // 版次
            bbglMapper.update(null, bbglUpdWrapper);
        }
    }

    /////////////////////////////////////////////////////////////////
    // 版本各场景操作处理 - 公共
    /////////////////////////////////////////////////////////////////

    /**
     * 【场景处理】撰写开始、点长提交、审阅开始、审阅提交、审阅完成、点长定稿处理、撰写暂存、撰写提交、等
     */
    @Override
    @Transactional
    public ZtProcInfoVO whileProc(ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo) {
        // 参数校验
        if (procType == null || bgid == null || bbid == null) {
            throw new ServiceException("未指定操作类型、报告ID或版本ID");
        }

        // 【加锁】加锁报告记录
        lockBg(bgid);

        // 数据定义
        ZtglStatusContext ctx = new ZtglStatusContext();

        // 各类型处理区分
        boolean needUpdateZt = false;
        if (procType == ZtProcType.ZXKS10) { // 撰写开始

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

        } else if (procType == ZtProcType.DZTJ20) { // 点长提交

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    true,
                    true);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 版本创建
            procCreateBb(ctx, procType, bgid, bbid, procInfo);

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 专家分工初始化
            procResetSyzj(ctx, procType, bgid, bbid, procInfo);

            // 专家意见审阅数据删除
            procClearZjyj(ctx, procType, bgid, bbid, procInfo);

            // 报告模块操作记录追加、任务分工撰写状态更新
            procSaveCzjl(ctx, procType, bgid, bbid);

            // 消息提醒
            procMessageBb(ctx, procType, bgid, bbid, procInfo);

        } else if (procType == ZtProcType.SYKS30) { // 审阅开始

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    false,
                    true,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

        } else if (procType == ZtProcType.SYTJ40) { // 审阅提交

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    true,
                    true);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 版本创建
            procCreateBb(ctx, procType, bgid, bbid, procInfo);

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 消息提醒
            procMessageBb(ctx, procType, bgid, bbid, procInfo);

        } else if (procType == ZtProcType.SYWC50) { // 审阅完成

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    true,
                    true);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 消息提醒
            procMessageBb(ctx, procType, bgid, bbid, procInfo);

        } else if (procType == ZtProcType.DZDG70) { // 点长定稿

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    true);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 操作记录(必要时)
            needUpdateZt = procSaveZtjlAndZjsy(ctx, procType, bgid, bbid);

            // 版本创建
            procInfo =  procCreateBb(ctx, procType, bgid, bbid, procInfo); //原型没说要建版本  这个版本需要和其他的区分下

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 专家意见审阅数据删除
            // procClearZjyj(ctx, procType, bgid, bbid, procInfo); 原型没说要建版本

            // 报告定稿状态变化
            procUpdateBgzt(ctx, 2);

            // 消息提醒
            procMessageBb(ctx, procType, bgid, bbid, procInfo);


        } else if (procType == ZtProcType.ZXZC91) { // 撰写暂存

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 版本创建
            procCreateBb(ctx, procType, bgid, bbid, procInfo);

        } else if (procType == ZtProcType.ZXZC92) { // 撰写提交

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 报告模块操作记录追加、任务分工处理状态更新
            procSaveCzjl(ctx, procType, bgid, bbid);

        } else if (procType == ZtProcType.ZXZC93) { // 撰写编辑

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 报告模块操作记录追加、任务分工撰写状态更新
            procSaveCzjl(ctx, procType, bgid, bbid);

        } else if (procType == ZtProcType.DZCH94) { // 点长撤回

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    true,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 回退操作记录
            procDeleteDzchZtjl(ctx, procType, bgid, ctx.bgmkid);

            // 操作记录(必要时)
            needUpdateZt = true;

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 消息提醒(暂无)
            // procMessageBb(ctx, procType, bgid, bbid, procInfo);

        } else if (procType == ZtProcType.QXDG95) { // 取消定稿

            // 获取数据
            procInitContext(ctx, bgid, bbid,
                    false,
                    false,
                    false);

            // 状态校验
            procCheckStatus(ctx, procType, bgid, bbid);

            // 回退操作记录
            procDeleteQxdgZtjl(ctx, procType, bgid, ctx.bgmkid);

            // 操作记录(必要时)
            needUpdateZt = true;

            // 状态更新
            if (needUpdateZt) {
                procZtCalcBb(bgid, bbid, ctx);
            }

            // 报告定稿状态变化
            procUpdateBgzt(ctx, 1);

            // 消息提醒(暂无)
            // procMessageBb(ctx, procType, bgid, bbid, procInfo);

        } else { // 不识别
            throw new ServiceException("未指定报告ID或版本ID");
        }
        return procInfo;
    }


    /**
     * 报告取消定稿
     */
    @Transactional
    @Override
    public void qxdg(Long bgid) {
        ZtProcInfoVO procInfo = new ZtProcInfoVO();
        procInfo.setBgid(bgid);

        LambdaQueryWrapper<Bbgl> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(Bbgl::getScbj, 0)
                .eq(Bbgl::getBgid, bgid)
                .eq(Bbgl::getBblx, 1)
                .eq(Bbgl::getBgmkzt, 90);
        List<Bbgl> list = bbglMapper.selectList(wrapper);
        if (list == null) {
            return;
        }

        for (Bbgl bbgl : list) {
            Long bbid = bbgl.getId();
            procInfo.setBbid(bbid);
            whileProc(ZtProcType.QXDG95, bgid, bbid, procInfo);
        }

    }

    /**
     * 单个版本  报告取消定稿
     */
    @Transactional
    @Override
    public void qxdgD(Long bgid,Long bbid) {
        ZtProcInfoVO procInfo = new ZtProcInfoVO();
        procInfo.setBgid(bgid);
        procInfo.setBbid(bbid);
        whileProc(ZtProcType.QXDG95, bgid, bbid, procInfo);
    }

    /**
     * 根据专家意见数检查是否可定稿
     */
    @Override
    public int selectZjyjCnt(Long bbid) {
        return ztglMapper.selectZjyjCnt(bbid);
    }


    //---------------------------------------------------------------
    // 版本各场景操作处理 - 内部处理
    //---------------------------------------------------------------

    /**
     * 【内部处理】获取数据
     */
    private void procInitContext(ZtglStatusContext ctx, Long bgid, Long bbid,
                                 boolean needFgInfo,
                                 boolean needZjInfo,
                                 boolean needMkInfo
    ) {

        // 参数存储
        ctx.bgid = bgid;
        ctx.bbid = bbid;

        // 操作者信息
        LoginUser user = AuthUtil.getUser();
        if (user != null) {
            ctx.userId = user.getUserId();
            ctx.userName = user.getUserName();
            ctx.deptId = Long.parseLong(user.getDeptId());
            ctx.deptName = DeptCache.getDeptName(ctx.deptId);
        }

        // 报告信息
        ctx.bggl = bgglMapper.selectById(bgid);
        if (ctx.bggl == null) {
            throw new ServiceException("指定的报告不存在");
        }

        // 版本信息
        ctx.bbgl = bbglMapper.selectById(bbid);
        if (ctx.bbgl == null) {
            throw new ServiceException("指定的版本不存在");
        }

        // 版本信息详情
        Integer bgmkzt = ctx.bbgl.getBgmkzt();
        if (bgmkzt == null) {
            bgmkzt = ZtProcStatus.WKS0.getValue();
        }
        ctx.procStatus = ZtProcStatus.fromValue(bgmkzt);
        ctx.bgmkid = ctx.bbgl.getBgmkid();
        ctx.jdglid = ctx.bbgl.getJdglid();

        // 进度信息
        if (ctx.jdglid != null) {
            ctx.jdgl = jdglMapper.selectById(ctx.jdglid);
            if (ctx.jdgl == null) {
                throw new ServiceException("指定的进度不存在");
            }

            // 进度详情
            ctx.kssj = ctx.jdgl.getKssj();
            ctx.zxjssj = nextDay(ctx.jdgl.getZxjssj());
            ctx.syjssj = nextDay(ctx.jdgl.getSyjssj());

            // 报告模块进度关联列表
            LambdaQueryWrapper<Jdmkgl> wrapper = new LambdaQueryWrapper<>();
            wrapper
                    //.eq(Jdmkgl::getScbj, 0)
                    .eq(Jdmkgl::getJdglid, ctx.jdglid);
            ctx.jdmkglList = jdmkglMapper.selectList(wrapper);
        }

        // 分工信息
        if (needFgInfo) {
            LambdaQueryWrapper<Rwfg> wrapper = new LambdaQueryWrapper<>();
            wrapper
                    //.eq(Rwfg::getScbj, 0)
                    .eq(Rwfg::getBgmkid, ctx.bgmkid);
            ctx.rwfgList = rwfgMapper.selectList(wrapper);
        }

        // 专家信息
        if (needZjInfo) {
            LambdaQueryWrapper<Syzj> wrapper = new LambdaQueryWrapper<>();
            wrapper
                    //.eq(Syzj::getScbj, 0)
                    .eq(Syzj::getBbid, ctx.bbid);
            ctx.syzjList = syzjMapper.selectList(wrapper);

            // 专家遍历
            if (ctx.syzjList != null) {
                for (Syzj item : ctx.syzjList) {
                    // 查找自己
                    if (item.getZjid().equals(ctx.userId)) {
                        ctx.syzj = item;
                    }

                    // 一点点初始化
                    if (item.getSyzt() == null) {
                        item.setSyzt(1);
                    }
                }
            }
        }

        // 报告模块信息
        if (needMkInfo) {
            ctx.bgmk = bgmkMapper.selectById(ctx.bgmkid);
            if (ctx.bgmk == null) {
                throw new ServiceException("指定的报告模块不存在");
            }
        }

        // 存储操作前状态
        ctx.newBjdzsbc = ctx.bbgl.getBjdzsbc();
        ctx.newProcStatus = ctx.procStatus;
    }

    /**
     * 【内部处理】状态校验
     */
    private void procCheckStatus(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid) {

        // 基本状态下可用操作校验
        boolean valid = ZtglStatusDic.checkProcValid(ctx.procStatus, procType);
        if (!valid) {
            String procStatusStr = "不明";
            String procTypeStr = "不明";
            if (ctx.procStatus != null) {
                procStatusStr = ctx.procStatus.toString();
            }
            if (procType != null) {
                procTypeStr = procType.toString();
            }

            // throw new ServiceException("当前报告模块状态不支持此处理：状态：" + procStatusStr + "、操作：" + procTypeStr);
            throw new ServiceException("当前报告模块状态不支持此处理");
        }

        // 除了取消定稿，要做进度校验
        if (procType != ZtProcType.QXDG95) {
            // 当前进度校验
            if (ctx.jdgl == null) {
                throw new ServiceException("当前报告进度不支持此处理：没有当前进度");
            }

            // 进度起止时间校验
            Date now = new Date();
            if (now.compareTo(ctx.kssj) < 0) {
                throw new ServiceException("“" + ctx.jdgl.getJdmc() + "”尚未开始，不可操作");
            }
            if (now.compareTo(ctx.syjssj) >= 0) {
                throw new ServiceException("“" + ctx.jdgl.getJdmc() + "”已经结束，不可操作");
            }

            // 进度模块关联校验
            if (ctx.jdmkglList == null || ctx.jdmkglList.isEmpty()) {
                throw new ServiceException("“" + ctx.jdgl.getJdmc() + "”未关联任何模块，不可操作");
            }
            if (!ctx.jdmkglList.stream().anyMatch(item -> item.getBgmkid().equals(ctx.bgmkid))) {
                throw new ServiceException("“" + ctx.jdgl.getJdmc() + "”未关联本该模块，不可操作");
            }
        }

        // 操作人校验
        switch (procType) {
            case ZXKS10:
            case ZXZC91:
            case ZXZC93:
                // 撰写开始、撰写暂存、撰写编辑
                if (ctx.rwfgList == null || !ctx.rwfgList.stream().anyMatch(item ->
                        item.getRyid().equals(ctx.userId) && item.getFglx() != null && (item.getFglx().equals(1) || item.getFglx().equals(2))
                )) {
                    throw new ServiceException("您不是该模块的撰写负责人或关注点负责人，不可操作");
                }
                break;

            case ZXZC92:
                // 撰写编辑
                if (ctx.rwfgList == null || !ctx.rwfgList.stream().anyMatch(item ->
                        item.getRyid().equals(ctx.userId) && item.getFglx() != null && (item.getFglx().equals(1) || item.getFglx().equals(2))
                )) {
                    throw new ServiceException("您不是该模块的撰写负责人或关注点负责人，不可操作");
                }
                if (ctx.rwfgList == null || !ctx.rwfgList.stream().anyMatch(item ->
                        item.getFglx().equals(1)
                )) {
                    throw new ServiceException("该关注点未关联点长，无法进行提交");
                }
                break;

            case DZTJ20:
            case DZDG70:
            case DZCH94:
                // 点长提交、点长定稿、点长撤回
                if (ctx.rwfgList == null || !ctx.rwfgList.stream().anyMatch(item ->
                        item.getRyid().equals(ctx.userId) && item.getFglx() != null & item.getFglx().equals(1)
                )) {
                    throw new ServiceException("您不是该模块的关注点负责人，不可操作");
                }
                break;

            case SYKS30:
            case SYTJ40:
            case SYWC50:
                // 审阅开始、审阅提交、审阅完成
                if (ctx.syzj == null) {
                    throw new ServiceException("您不是该模块本次提交的审阅人，不可操作");
                }
                break;
            case QXDG95:
                // 取消定稿由管理员操作
                break;

            default:
        }

        // 专家审阅个人状态校验
        // -------------------------------------------
        // 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        // -------------------------------------------
        if (procType == ZtProcType.SYKS30) {
            // 审阅开始 WARN 调用方要加try catch
            if (!ctx.syzj.getSyzt().equals(1)) {
                throw new ServiceException("审阅已经开始，不必再审阅开始");
            }

        } else if (procType == ZtProcType.SYTJ40) {
            // 审阅提交
            if (ctx.syzj.getSyzt() == 1) {
                throw new ServiceException("尚未开始审阅，不可提交");
            }
            if (ctx.syzj.getSyzt() >= 3) {
                throw new ServiceException("已经提交过，不可再次提交");
            }

        } else if (procType == ZtProcType.SYWC50) {
            // 审阅完成
            if (ctx.syzj.getSyzt() < 3) {
                throw new ServiceException("尚未提交审阅，不可完成");
            }
            if (ctx.syzj.getSyzt() == 4) {
                throw new ServiceException("已经完成过，不可再次完成");
            }
        }
    }

    /**
     * 【内部处理】操作记录(必要时)
     * 实质性操作，存操作记录
     * 专家操作，改专家审阅状态
     */
    private boolean procSaveZtjlAndZjsy(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid) {

        // 参见《5 状态变化表》【4】各状态下各事件（及动作）后状态变化

        // 撰写开始操作，若 状态 是 已经开始、已延期，则后续不必做任何操作
        if (procType == ZtProcType.ZXKS10) {
            if (ctx.procStatus == ZtProcStatus.ZXZ10 || ctx.procStatus == ZtProcStatus.YYQ20) {
                return false;
            }
        }

        // 审阅开始操作，若 状态 是 审阅中，则后续不必做任何操作
        // 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        if (procType == ZtProcType.SYKS30 || procType == ZtProcType.SYTJ40 || procType == ZtProcType.SYWC50) {
            // 判断操作后专家审阅状态
            int newSyzt = 1;
            if (procType == ZtProcType.SYKS30) {
                // 审阅开始
                newSyzt = 2;
            } else if (procType == ZtProcType.SYTJ40) {
                // 审阅提交
                newSyzt = 3;
            } else if (procType == ZtProcType.SYWC50) {
                // 审阅完成
                newSyzt = 4;
            }

            // 专家状态数据更新
            procUpdateSyzt(ctx.syzj.getId(), newSyzt);

            // 计算审阅相关新的版本状态
            ZtProcStatus newProcStatus = procCalcSyStatus(ctx.bggl, ctx.syzjList, ctx.syzj, newSyzt);

            // 判断是否插入操作记录
            if (newProcStatus == null || ctx.procStatus == newProcStatus) {
                return false;
            }
        }

        // 插入操作记录
        Bgmkztjl bgmkztjl = new Bgmkztjl();
        bgmkztjl.setBgid(ctx.bgid);
        bgmkztjl.setBgmkid(ctx.bgmkid);
        bgmkztjl.setCzlx(String.valueOf(procType.getValue()));
        bgmkztjlService.save(bgmkztjl);
        return true;
    }

    /**
     * 【内部处理】点长撤回.删除最后操作记录
     */
    private void procDeleteDzchZtjl(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bgmkid) {

        if (procType != ZtProcType.DZCH94) {
            return;
        }

        // 找到最后一条操作记录
        LambdaQueryWrapper<Bgmkztjl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Bgmkztjl::getBgid, bgid)
                .eq(Bgmkztjl::getBgmkid, bgmkid)
                .eq(Bgmkztjl::getScbj, 0)
                .orderByDesc(Bgmkztjl::getCjrq)
                .last("LIMIT 1");
        List<Bgmkztjl> list = bgmkztjlMapper.selectList(wrapper);
        Bgmkztjl bgmkztjl = null;
        if (list != null && !list.isEmpty()) {
            bgmkztjl = list.get(0);
        }

        // 若不是“点长提交”，报错
        if (bgmkztjl == null || !"20".equals(bgmkztjl.getCzlx())) {
            throw new ServiceException("未找到相关的操作记录");
        }

        // 删除相关数据
        bgmkztjlService.removeById(bgmkztjl.getId());
    }

    /**
     * 【内部处理】取消定稿.删除最后操作记录
     */
    private void procDeleteQxdgZtjl(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bgmkid) {

        if (procType != ZtProcType.QXDG95) {
            return;
        }

        // 找到最后一条操作记录
        LambdaQueryWrapper<Bgmkztjl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Bgmkztjl::getBgid, bgid)
                .eq(Bgmkztjl::getBgmkid, bgmkid)
                .eq(Bgmkztjl::getScbj, 0)
                .orderByDesc(Bgmkztjl::getCjrq)
                .last("LIMIT 1");
        List<Bgmkztjl> list = bgmkztjlMapper.selectList(wrapper);
        Bgmkztjl bgmkztjl = null;
        if (list != null && !list.isEmpty()) {
            bgmkztjl = list.get(0);
        }

        // 若不是“点长提交”，报错
        if (bgmkztjl == null || !"70".equals(bgmkztjl.getCzlx())) {
            throw new ServiceException("未找到相关的操作记录");
        }

        // 删除相关数据
        bgmkztjlService.removeById(bgmkztjl.getId());
    }

    /**
     * 【内部处理】专家状态数据更新
     */
    private void procUpdateSyzt(Long id, int syzt) {
        LambdaUpdateWrapper<Syzj> wrapper = new LambdaUpdateWrapper<>();
        wrapper
                .eq(Syzj::getId, id)
                .set(Syzj::getSyzt, syzt) // 状态
                .setSql("gxrq=curtime()"); // 更新日期
        syzjMapper.update(null, wrapper);
    }

    /**
     * 【内部处理】计算审阅相关新的版本状态
     */
    private ZtProcStatus procCalcSyStatus(Bggl bggl, List<Syzj> syzjList, Syzj mySyzj, int myNewSyzt) {
        // 设置本人新状态
        mySyzj.setSyzt(myNewSyzt);

        // 计算最小最大专家状态
        int minSyzt = 4;
        int maxSyzt = 1;
        for (Syzj Syzj : syzjList) {
            int syzt = Syzj.getSyzt();
            if (syzt < minSyzt) {
                minSyzt = syzt;
            }
            if (syzt > maxSyzt) {
                maxSyzt = syzt;
            }
        }

        // 【演示报告 - 保留】
        //// 对演示报告
        //Integer ysbg = bggl.getYsbg();
        //if (ysbg != null && ysbg == 1) {
        //    // 【演示报告】  任何专家提交或完成就算提交或完成（对应大量专家演示）
        //    // 计算新版本状态
        //    // Syzt 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        //    if (maxSyzt == 4) {
        //        // 最快的人 4:已完成
        //        return ZtProcStatus.YWC80; // 80:已完成
        //    }
        //    if (maxSyzt == 3) {
        //        // 最快的人 3:已审阅
        //        return ZtProcStatus.DXG60; // 60:待修改、已审阅
        //    }
        //    if (maxSyzt == 2) {
        //        // 最快的人 2:审阅中
        //        return ZtProcStatus.SYZ40; // 40:审阅中
        //    }
        //    if (maxSyzt == 1) {
        //        // 所有人都 1:未审阅
        //        return ZtProcStatus.YTJ30; // 30:已提交、待审阅
        //    }
        //} else {
        //    // 【普通报告】 全部提交或完成才算提交或完成
        //    // 计算新版本状态
        //    // Syzt 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        //    if (maxSyzt == 1) {
        //        // 所有人都 1:未审阅
        //        return ZtProcStatus.YTJ30; // 30:已提交、待审阅
        //    }
        //    if (minSyzt == 4) {
        //        // 最慢的人 4:已完成
        //        return ZtProcStatus.YWC80; // 80:已完成
        //    }
        //    if (minSyzt == 3) {
        //        // 最慢的人 3:已审阅
        //        return ZtProcStatus.DXG60; // 60:待修改、已审阅
        //    }
        //    if (minSyzt <= 2) {
        //        // 前面有maxSyzt>1
        //        // 审阅开始，但最慢的人 2:审阅中 或 1:未审阅
        //        return ZtProcStatus.SYZ40; // 40:审阅中
        //    }
        //}

        // 计算新版本状态
        // Syzt 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        if (maxSyzt == 1) {
            // 所有人都 1:未审阅
            return ZtProcStatus.YTJ30; // 30:已提交、待审阅
        }
        if (minSyzt == 4) {
            // 最慢的人 4:已完成
            return ZtProcStatus.YWC80; // 80:已完成
        }
        if (minSyzt == 3) {
            // 最慢的人 3:已审阅
            return ZtProcStatus.DXG60; // 60:待修改、已审阅
        }
        if (minSyzt <= 2) {
            // 前面有maxSyzt>1
            // 审阅开始，但最慢的人 2:审阅中 或 1:未审阅
            return ZtProcStatus.SYZ40; // 40:审阅中
        }

        // 返回结果
        return null;
    }

    /**
     * 【内部处理】版本创建
     */
    private ZtProcInfoVO procCreateBb(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo) {

        Long bbidNew;
        // 【版本数据】
        int bblx = 2; // 版本类型 1:主线版本, 2:正式版本, 3:暂存版本
        int dgbb = 0;//默认非定稿版本
        Integer ssjs = null; // 所属角色 1:主要关注点负责人, 2:专家
        String bbmc = ""; // 版本名称
        LoginUser loginUser = AuthUtil.getUser();
        switch (procType) {
            case DZTJ20:// 20:点长提交
                bblx = 2;
                ssjs = 1;
                bbmc = ctx.bgmk.getMkmc();
                if (StringUtils.isNotBlank(ctx.bgmk.getCmm())) {
                    bbmc = ctx.bgmk.getCmm();
                }
                bbmc = bbmc + "-" + ctx.jdgl.getJdmc();
                if (ctx.bbgl.getBjdzsbc() != null && ctx.bbgl.getBjdzsbc() > 1) {
                    bbmc = bbmc + "-" + ctx.bbgl.getBjdzsbc();
                }
                break;

            case SYTJ40:// 40:审阅提交
                bblx = 2;
                ssjs = 2;
                bbmc = ctx.bgmk.getMkmc();
                if (StringUtils.isNotBlank(ctx.bgmk.getCmm())) {
                    bbmc = ctx.bgmk.getCmm();
                }
                bbmc = bbmc + "-" + ctx.jdgl.getJdmc();
                bbmc = bbmc + "-" + ctx.userName + "已审阅";
                if (ctx.bbgl.getBjdzsbc() != null && ctx.bbgl.getBjdzsbc() > 1) {
                    bbmc = bbmc + "-" + ctx.bbgl.getBjdzsbc();
                }
                break;

            case ZXZC91:// 91:撰写暂存
                bblx = 3;
                ssjs = null;
                bbmc = procInfo.getBbmc();
                break;
            case DZDG70:
                dgbb = 1;//为1则是定稿版本
                ssjs = 1;
                bbmc = procInfo.getBbmc();
            default:
                break;
        }


        // 获取旧版本数据
        Bbgl bbgl = bbglService.getById(bbid);
        // 设置数据
        bbgl.setId(null);
        bbgl.setZhid(null); // 为何会insert两次
        bbgl.setSsjs(ssjs);
        bbgl.setBblx(bblx);
        bbgl.setBbmc(bbmc);
        bbgl.setDgbb(dgbb);
        bbgl.setBbsm(procInfo.getBbsm());
        bbgl.setCjrq(null);

        bbgl.setBbsj(new Date());
        bbgl.setTjrid(loginUser.getUserId());
        bbgl.setTjr(loginUser.getUserName());

        // 清理OnlyOffice的key、路径
        bbgl.setWjkey(UUID.randomUUID().toString());
        if (procType == ZtProcType.DZDG70) {//点长定稿还是原来文件路径
        }else{
            bbgl.setWjlj(null);
        }
        // 保存数据
        bbglService.save(bbgl);

        // 设置返回版本
        bbidNew = bbgl.getId();
        procInfo.setBbidNew(bbidNew);

        // 【审阅专家】
        LambdaQueryWrapper<Syzj> syzjWrapper = new LambdaQueryWrapper<>();
        syzjWrapper
                //.eq(Syzj::getScbj, 0)
                .eq(Syzj::getBbid, bbid);
        List<Syzj> syzjList = syzjService.list(syzjWrapper);
        if (syzjList != null && !syzjList.isEmpty()) {
            for (Syzj item : syzjList) {
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
            }
            syzjService.saveBatch(syzjList);
        }

        // 专家意见.关联信息ID 数据转换定义
        Map<Integer, Map<Long, Long>> zjyjGlxxDic = new HashMap<>();
        Long glxxidOld = null;

        // 【数据指标关联】
        LambdaQueryWrapper<Sjzbgl> sjzbglWrapper = new LambdaQueryWrapper<>();
        sjzbglWrapper
                //.eq(Sjzbgl::getScbj, 0)
                .eq(Sjzbgl::getBbid, bbid);
        List<Sjzbgl> sjzbglList = sjzbglService.list(sjzbglWrapper);
        if (sjzbglList != null && !sjzbglList.isEmpty()) {
            for (Sjzbgl item : sjzbglList) {
                glxxidOld = item.getId();
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
                sjzbglService.save(item);
                addZjyjGlxx(zjyjGlxxDic, 1, glxxidOld, item.getId()); // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
            }
        }

        // 【数据表关联】
        LambdaQueryWrapper<Sjbgl> sjbglWrapper = new LambdaQueryWrapper<>();
        sjbglWrapper
                //.eq(Sjbgl::getScbj, 0)
                .eq(Sjbgl::getBbid, bbid);
        List<Sjbgl> sjbglList = sjbglService.list(sjbglWrapper);
        if (sjbglList != null && !sjbglList.isEmpty()) {
            for (Sjbgl item : sjbglList) {
                glxxidOld = item.getId();
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
                sjbglService.save(item);
                addZjyjGlxx(zjyjGlxxDic, 2, glxxidOld, item.getId()); // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
            }
        }

        // 【佐证材料关联】
        LambdaQueryWrapper<Zzclgl> zzclglWrapper = new LambdaQueryWrapper<>();
        zzclglWrapper
                //.eq(Zzclgl::getScbj, 0)
                .eq(Zzclgl::getBbid, bbid);
        List<Zzclgl> zzclglList = zzclglService.list(zzclglWrapper);
        if (zzclglList != null && !zzclglList.isEmpty()) {
            for (Zzclgl item : zzclglList) {
                glxxidOld = item.getId();
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
                zzclglService.save(item);
                addZjyjGlxx(zjyjGlxxDic, 3, glxxidOld, item.getId()); // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
            }
        }

        // 【备查材料关联】
        LambdaQueryWrapper<Zcclgl> zcclglWrapper = new LambdaQueryWrapper<>();
        zcclglWrapper
                //.eq(Zcclgl::getScbj, 0)
                .eq(Zcclgl::getBbid, bbid);
        List<Zcclgl> zcclglList = zcclglService.list(zcclglWrapper);
        if (zcclglList != null && !zcclglList.isEmpty()) {
            for (Zcclgl item : zcclglList) {
                glxxidOld = item.getId();
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
                zcclglService.save(item);
                addZjyjGlxx(zjyjGlxxDic, 4, glxxidOld, item.getId()); // 1:数据指标, 2:数据表, 3:佐证材料, 4:备查材料
            }
        }

        // 【专家意见】
        LambdaQueryWrapper<Zjyj> zjyjWrapper = new LambdaQueryWrapper<>();
        zjyjWrapper
                //.eq(Zjyj::getScbj, 0)
                .eq(Zjyj::getBbid, bbid);
        List<Zjyj> zjyjList = zjyjService.list(zjyjWrapper);
        if (zjyjList != null && !zjyjList.isEmpty()) {
            for (Zjyj item : zjyjList) {
                fixZjyjGlxx(zjyjGlxxDic, item);
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
            }
            zjyjService.saveBatch(zjyjList);
        }

        // 【智能检测】
        LambdaQueryWrapper<Gjcznjc> gjcznjcWrapper = new LambdaQueryWrapper<>();
        gjcznjcWrapper
                //.eq(Gjcznjc::getScbj, 0)
                .eq(Gjcznjc::getBbid, bbid);
        List<Gjcznjc> gjcznjcList = gjcznjcService.list(gjcznjcWrapper);
        if (gjcznjcList != null && !gjcznjcList.isEmpty()) {
            for (Gjcznjc item : gjcznjcList) {
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
            }
            gjcznjcService.saveBatch(gjcznjcList);
        }

        // 【版本批注】
        LambdaQueryWrapper<Bbpz> bbpzWrapper = new LambdaQueryWrapper<>();
        bbpzWrapper
                //.eq(Zjyj::getScbj, 0)
                .eq(Bbpz::getBbid, bbid);
        List<Bbpz> bbpzList = bbpzService.list(bbpzWrapper);
        if (bbpzList != null && !bbpzList.isEmpty()) {
            for (Bbpz item : bbpzList) {
                item.setZhid(null);
                item.setId(null);
                item.setBbid(bbidNew);
            }
            bbpzService.saveBatch(bbpzList);
        }

        return procInfo;
    }

    /**
     * 【内部处理】专家分工初始化
     */
    private void procResetSyzj(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo) {

        // 新数据插入
        List<Syzj> syzjList = procInfo.getSyzjList();
        if (syzjList == null || syzjList.isEmpty()) {
            throw new ServiceException("未指定审阅专家");
        }
        for (Syzj item : syzjList) {
            item.setZhid(null);
            item.setId(null);
            item.setBgid(ctx.bgid);
            item.setBgmkid(ctx.bgmkid);
            item.setBbid(bbid);
            item.setSyzt(1); // 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        }
        //2024/10/24 废弃 原型1.17 点长提交时，需要手动选择审阅专家
        //// 专家数据准备
        //List<Syzj> syzjList = new ArrayList<>();
        //for (Rwfg rwfg : ctx.rwfgList) {
        //    if (rwfg.getFglx() == null || !rwfg.getFglx().equals(3)) {
        //        continue;
        //    }
        //    Syzj item = new Syzj();
        //    item.setZhid(null);
        //    item.setId(null);
        //    item.setBgid(ctx.bgid);
        //    item.setBgmkid(ctx.bgmkid);
        //    item.setBbid(bbid);
        //    item.setZjid(rwfg.getRyid());
        //    item.setZjxm(rwfg.getRyxm());
        //    item.setZjdwid(rwfg.getDwid());
        //    item.setZjdwmc(rwfg.getDwmc());
        //
        //    item.setSyzt(1); // 1:未审阅, 2:审阅中, 3:已审阅, 4:已完成
        //
        //    syzjList.add(item);
        //}
        //if (syzjList == null || syzjList.isEmpty()) {
        //    throw new ServiceException("该关注点未关联专家，无法进行提交");
        //}

        // 旧数据删除
        LambdaQueryWrapper<Syzj> syzjWrapper = new LambdaQueryWrapper<>();
        syzjWrapper
                //.eq(Syzj::getScbj, 0)
                .eq(Syzj::getBbid, bbid);
        syzjService.remove(syzjWrapper);

        // 新数据插入
        syzjService.saveBatch(syzjList);
    }

    /**
     * 【内部处理】专家意见审阅数据删除
     */
    private void procClearZjyj(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo) {
        // 旧数据删除
        LambdaQueryWrapper<Zjyj> zjyjWrapper = new LambdaQueryWrapper<>();
        zjyjWrapper
                //.eq(Zjyj::getScbj, 0)
                .eq(Zjyj::getBbid, bbid);
        zjyjService.remove(zjyjWrapper);
    }

    /**
     * 【内部处理】报告模块操作记录追加、任务分工处理状态更新
     */
    private void procSaveCzjl(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid) {
        // 状态计算
        Integer rwfgClzt;
        Long rwfgRyid;
        Integer czjlCzxw;
        Integer czjlCzjs;

        switch (procType) {
            case DZTJ20: // 20:点长提交
                // 所有人处理状态恢复为撰写中
                rwfgRyid = null;
                rwfgClzt = 1; // 1:撰写中
                // 点长 提交
                czjlCzxw = 1; // 1提交, 2:重新编辑
                czjlCzjs = 1; // 1:点长, 2:撰写人
                break;

            case ZXZC92: // 92:撰写提交
                // 个人处理状态设置为已提交
                rwfgRyid = ctx.userId;
                rwfgClzt = 2; // 2:已提交
                // 撰写 提交
                czjlCzxw = 1; // 1提交, 2:重新编辑
                czjlCzjs = 2; // 1:点长, 2:撰写人
                break;

            case ZXZC93: // 93:撰写编辑
                // 个人处理状态设置为撰写中
                rwfgRyid = ctx.userId;
                rwfgClzt = 1; // 1:撰写中
                // 点长 编辑
                czjlCzxw = 2; // 1提交, 2:重新编辑
                czjlCzjs = 2; // 1:点长, 2:撰写人
                break;

            default:
                // 什么都不做
                return;
        }

        // 数据库更新 - 任务分工
        LambdaUpdateWrapper<Rwfg> rwfgUpdWrapper = new LambdaUpdateWrapper<>();
        if (rwfgRyid == null) {
            rwfgUpdWrapper
                    .eq(Rwfg::getBgid, bgid)
                    .eq(Rwfg::getBgmkid, ctx.bgmkid)
                    .in(Rwfg::getFglx, 1, 2)
                    .set(Rwfg::getClzt, rwfgClzt);
        } else {
            rwfgUpdWrapper
                    .eq(Rwfg::getBgid, bgid)
                    .eq(Rwfg::getBgmkid, ctx.bgmkid)
                    .eq(Rwfg::getRyid, rwfgRyid)
                    .in(Rwfg::getFglx, 1, 2)
                    .set(Rwfg::getClzt, rwfgClzt);
        }
        rwfgMapper.update(null, rwfgUpdWrapper);

        // 数据库更新 - 报告模块操作记录
        Bgmkczjl bgmkczjl = new Bgmkczjl();
        bgmkczjl.setBgid(ctx.bgid);
        bgmkczjl.setBgmkid(ctx.bgmkid);
        bgmkczjl.setCzxw(czjlCzxw);
        bgmkczjl.setCzjs(czjlCzjs);
        bgmkczjl.setRyid(ctx.userId);
        bgmkczjl.setRyxm(ctx.userName);
        bgmkczjlService.save(bgmkczjl);
    }

    /**
     * 【内部处理】更新报告状态
     */
    private void procUpdateBgzt(ZtglStatusContext ctx, int bgzt) {
        // 查看本类型（综合评价/政治理论）的报告是否已经有展示的报告
        LambdaQueryWrapper<Bggl> bgglQueryWrapper = new LambdaQueryWrapper<>();
        bgglQueryWrapper.eq(Bggl::getMklx, ctx.bggl.getMklx())
                .eq(Bggl::getBgzt, 2)
                .eq(Bggl::getScbj, 0);
        Integer bg2Count = bgglMapper.selectCount(bgglQueryWrapper);
        if (bg2Count == null) {
            bg2Count = 0;
        }

        // 查看本报告是否有定稿模块
        LambdaQueryWrapper<Bgmk> bgmkQueryWrapper = new LambdaQueryWrapper<>();
        bgmkQueryWrapper.eq(Bgmk::getBgid, ctx.bgid)
                .eq(Bgmk::getBgmkzt, 90)
                .eq(Bgmk::getScbj, 0);
        Integer mk90Count = bgmkMapper.selectCount(bgmkQueryWrapper);
        if (mk90Count == null) {
            mk90Count = 0;
        }

        if (bgzt == 2) {
            // 定稿请求，尝试发布
            if (bg2Count > 0 || mk90Count < 1) {
                // 若 已有发布报告 或者 本报告没有定稿模块，啥也不做
                return;
            }
        } else {
            // 取消定稿请求，尝试取消发布
            if (mk90Count > 0) {
                // 本报告还有定稿模块，啥也不做
                return;
            }
        }

        // 设置报告状态
        LambdaUpdateWrapper<Bggl> bgglUpdateWrapper = new LambdaUpdateWrapper<>();
        bgglUpdateWrapper
                .eq(Bggl::getId, ctx.bgid)
                .set(Bggl::getBgzt, bgzt); // 状态
        bgglMapper.update(null, bgglUpdateWrapper);
    }


    /**
     * 【内部处理】发送提醒消息 - 版本操作
     */
    private void procMessageBb(ZtglStatusContext ctx, ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo) {
        // 变量定义
        String format;
        String formatGly;
        String content;
        List<Long> userIdList = null;

        // 触发条件（操作类型）判断
        switch (procType) {
            case DZTJ20: // 点长提交
                // 消息标题
                if (ctx.newBjdzsbc < 2) {
                    // 4.
                    // 每轮进度下，点长点击提交自评报告时
                    // 点长提交时选择的专家
                    // XXX自评报告已提交，请您审阅。
                    format = "{0}自评报告已提交，请您审阅。";
                    // 1.1.1内涵内容自评报告点长已提交审阅
                    formatGly = "{0}自评报告点长已提交审阅";
                } else {
                    // 6.
                    // 专家完成审阅后，撰写人按照专家意见修改自评报告，点长点击提交（从第二次点击提交）
                    // 点长提交时选择的专家
                    // 专家，您好，1.1.1内涵内容已完成修改，请您再次审阅
                    format = "专家，您好，{0}已完成修改，请您再次审阅";
                    // 1.1.1内涵内容已完成修改，已提交至专家审阅
                    formatGly = "{0}已完成修改，已提交至专家审阅";
                }

                // 消息目标
                content = "zj";

                // 消息人员
                if (ctx.syzjList != null) {
                    userIdList = ctx.syzjList.stream()
                            .map(Syzj::getZjid)
                            .collect(Collectors.toList());
                }

                // 消息发送
                procMessageSend(ctx, content, format, userIdList);
                procMessageSend(ctx, "gly", formatGly, null);

                break;

            case SYTJ40: // 审阅提交
                // 消息标题
                // 5.
                // 每轮进度下专家完成了对自评报告的审阅，点击提交按钮
                // 对应模块的点长和撰写人收到消息
                // XXX专家已完成了对XXXX自评报告的审阅，请您按照专家审阅意见及时修改自评报告。
                format = "{1}专家已完成了对{0}自评报告的审阅，请您按照专家审阅意见及时修改自评报告。";
                // XX专家已完成了对XXXX自评报告的审阅
                formatGly = "{1}专家已完成了对{0}自评报告的审阅";

                // 消息目标
                content = "zxrOrDz";

                // 消息人员
                if (ctx.rwfgList != null) {
                    userIdList = ctx.rwfgList.stream()
                            .filter(rwfg -> rwfg.getFglx() != null && (rwfg.getFglx().equals(1) || rwfg.getFglx().equals(2))) // 1:点长, 2:撰写人, 3:专家
                            .map(Rwfg::getRyid)
                            .collect(Collectors.toList());
                }

                // 消息发送
                procMessageSend(ctx, content, format, userIdList);
                procMessageSend(ctx, "gly", formatGly, null);

                break;

            case SYWC50: // 审阅完成
                // 所有专家全完成判断
                if (ctx.newProcStatus == null || ctx.newProcStatus != ZtProcStatus.YWC80) {
                    return;
                }

                // 消息标题
                // 7
                // 专家审阅完成后，点击完成
                // 对应模块的点长和撰写人收到消息
                // XX专家已完成了对XXXX自评报告的审阅，请所负责的自评报告已无需再次提交审阅。
                format = "{1}专家已完成了对{0}自评报告的审阅，您所负责的自评报告已无需再次提交审阅。";
                // 1.1.1内涵内容自评报告已完成，已无需再次提交审阅。
                formatGly = "{0}自评报告已完成，已无需再次提交审阅";

                // 消息目标
                content = "zxrOrDz";

                // 消息人员
                if (ctx.rwfgList != null) {
                    userIdList = ctx.rwfgList.stream()
                            .filter(rwfg -> rwfg.getFglx() != null && (rwfg.getFglx().equals(1) || rwfg.getFglx().equals(2))) // 1:点长, 2:撰写人, 3:专家
                            .map(Rwfg::getRyid)
                            .collect(Collectors.toList());
                }

                // 消息发送
                procMessageSend(ctx, content, format, userIdList);
                procMessageSend(ctx, "gly", formatGly, null);

                break;

            case DZDG70: // 点长定稿
                // 消息标题
                // 8.
                // 点长点击定稿
                // 该模块下所有的撰写人收到消息
                // 教务处王老师已定稿1.1.1内涵内容自评报告，该报告的撰写任务已完成。
                format = "{2}{1}已定稿{0}自评报告，该报告的撰写任务已完成。";
                // 教务处王老师已定稿1.1.1内涵内容自评报告
                formatGly = "{2}{1}已定稿{0}自评报告";

                // 消息目标
                content = "zxrOrDz";

                // 消息人员
                if (ctx.rwfgList != null) {
                    userIdList = ctx.rwfgList.stream()
                            .filter(rwfg -> rwfg.getFglx() != null && rwfg.getFglx().equals(2)) // 1:点长, 2:撰写人, 3:专家
                            .map(Rwfg::getRyid)
                            .collect(Collectors.toList());
                }


                // 消息发送
                procMessageSend(ctx, content, format, userIdList);
                procMessageSend(ctx, "gly", formatGly, null);

                break;
        }
    }

    /**
     * 操作消息发送
     *
     * @param ctx
     * @param content
     * @param format     {0}名称, {1}操作者姓名, {2}操作者部门
     * @param userIdList
     */
    private void procMessageSend(ZtglStatusContext ctx, String content, String format, List<Long> userIdList) {
        // 目标
        String typeStr;
        if ("zxrOrDz".equals(content)) {
            typeStr = "撰写人或点长";
        } else if ("zj".equals(content)) {
            typeStr = "专家";
        } else if ("gly".equals(content)) {
            typeStr = "管理员";
        } else {
            throw new ServiceException("不识别的消息发送类型");
        }

        // 报告名称
        String mkmc = ctx.bgmk.getMkmc();
        String cmm = ctx.bgmk.getCmm();
        if (StringUtils.isNotBlank(cmm)) {
            mkmc = cmm;
        }

        // 消息内容
        String title = MessageFormat.format(format, mkmc, ctx.userName, ctx.deptName);

        // 消息发送
        if ("gly".equals(content)) {
            xxfbService.sendMessageJp(title);
        } else {
            if (userIdList == null || userIdList.isEmpty()) {
                return;
            }
            List<XxfbUserVO> userList = new ArrayList<>();
            Map<Long, Long> userIdMap = new HashMap<>();
            for (Long userId : userIdList) {
                if (userId == null || userIdMap.containsKey(userId)) {
                    continue;
                }
                userIdMap.put(userId, userId);
                XxfbUserVO user = new XxfbUserVO();
                user.setUserId(userId.toString());
                userList.add(user);
            }
            if (userList.isEmpty()) {
                return;
            }
            xxfbService.sendMessageYw(
                    title,
                    content,
                    userList,
                    typeStr,
                    ctx.bbid);
        }
    }

    /////////////////////////////////////////////////////////////////
    // Util
    /////////////////////////////////////////////////////////////////
    // 时间改为次日凌晨0点
    private Date nextDay(Date day) {
        if (day == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(day);

        // 将时间设置为次日凌晨零点
        calendar.add(Calendar.DATE, 1); // 增加一天
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0

        return calendar.getTime();
    }

    // 时间改为该日凌晨0点
    private Date trancateDay(Date day) {
        if (day == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(day);

        // 将时间设置为该日凌晨零点
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0

        return calendar.getTime();
    }

    // 记忆 专家意见 关联信息ID的旧新值
    private void addZjyjGlxx(Map<Integer, Map<Long, Long>> zjyjGlxxDic, Integer glcllx, Long idOld, Long idNew) {
        if (glcllx == null || glcllx < 1 || idOld == null || idOld < 0 || idNew == null || idNew < 0) {
            return;
        }
        Map<Long, Long> valueMap = zjyjGlxxDic.get(glcllx);
        if (valueMap == null) {
            valueMap = new HashMap<>();
            zjyjGlxxDic.put(glcllx, valueMap);
        }
        valueMap.put(idOld, idNew);
    }

    // 获取 专家意见 关联信息ID的新值
    private void fixZjyjGlxx(Map<Integer, Map<Long, Long>> zjyjGlxxDic, Zjyj zjyj) {
        Integer glcllx = zjyj.getGlcllx();
        Long glxxid = zjyj.getGlxxid();
        if (glcllx == null || glcllx < 1 || glxxid == null || glxxid < 0) {
            return;
        }
        Map<Long, Long> valueMap = zjyjGlxxDic.get(glcllx);
        if (valueMap == null) {
            return;
        }
        Long idNew = valueMap.get(glxxid);
        if (idNew != null) {
            zjyj.setGlxxid(idNew);
        }
    }
    /////////////////////////////////////////////////////////////////
    // 测试
    /////////////////////////////////////////////////////////////////

}
