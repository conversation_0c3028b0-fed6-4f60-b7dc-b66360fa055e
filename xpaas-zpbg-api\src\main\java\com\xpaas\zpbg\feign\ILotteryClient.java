package com.xpaas.zpbg.feign;

import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Demo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(
        contextId = "ILotteryClientFallback",
        value = "xpaas-lottery"
)
public interface ILotteryClient {
    String API_PREFIX = "/clientLottery";
    String CQ_CQJG = API_PREFIX + "/cq_cqjg";


    //根据ids查询结果查询
    @GetMapping(CQ_CQJG)
    R<Demo>getCqjg(@RequestParam("ids")List<Long> ids);



}
