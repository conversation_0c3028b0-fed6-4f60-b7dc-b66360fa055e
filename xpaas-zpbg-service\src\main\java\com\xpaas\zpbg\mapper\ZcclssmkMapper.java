package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zcclssmk;
import com.xpaas.zpbg.vo.ZcclssmkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料所属模块 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Repository
public interface ZcclssmkMapper extends BaseMapper<Zcclssmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zcclssmk
	 * @return
	 */
	List<ZcclssmkVO> selectZcclssmkPage(IPage page, ZcclssmkVO zcclssmk);

	/**
	 * 备查材料所属列表
	 * @param zzclId
	 * @return
	 */
	List<ZcclssmkVO> selectZcclssmkList(Long zzclId);

}
