package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.dto.ZjyjExcelDTO;
import com.xpaas.zpbg.entity.Zjyj;
import com.xpaas.zpbg.vo.ZjyjVO;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-专家意见 服务类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface IZjyjService extends BaseService<Zjyj> {

    /**
     * 自定义分页
     *
     * @param page
     * @param zjyj
     * @return
     */
    IPage<ZjyjVO> selectZjyjPage(IPage<ZjyjVO> page, ZjyjVO zjyj);

    /**
     * 自定义分页
     *
     * @param page
     * @param zjyj
     * @return
     */
    IPage<ZjyjVO> selectZxrPage(IPage<ZjyjVO> page, ZjyjVO zjyj);


    /**
     * 自定义分页
     *
     * @param page
     * @param zjyj
     * @return
     */
    IPage<ZjyjVO> selectOtherzjPage(IPage<ZjyjVO> page, ZjyjVO zjyj);

    /**
     * 导出意见
     *
     * @param map
     * @return
     */
    List<ZjyjExcelDTO> getzjyjExcel(Map<String, Object> map);

    /**
     * 自定义分页
     *
     * @param bbid
     * @param yjkeyList
     * @return
     */
    List<ZjyjVO> getzjyjList(String bbid,List<String> yjkeyList);

    int getZjyjCount(ZjyjVO zjyjvo);

    int getyjChecking (String bgid, String bbid,String bgmkid);

    List<Zjyj> getLsyj(ZjyjVO zjyjvo);
}
