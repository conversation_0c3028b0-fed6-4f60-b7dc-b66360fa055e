package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.vo.BgmkztjlVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-报告模块状态记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public interface BgmkztjlMapper extends BaseMapper<Bgmkztjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmkztjl
	 * @return
	 */
	List<BgmkztjlVO> selectBgmkztjlPage(IPage page, BgmkztjlVO bgmkztjl);

}
