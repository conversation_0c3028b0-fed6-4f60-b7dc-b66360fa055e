<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.PcglMapper">

    <!-- 版本管理数据查询 -->
    <select id="selectPcglPage" resultType="com.xpaas.zpbg.vo.PcglVO">
        select a.*,(SELECT GROUP_CONCAT(BGMC) FROM T_DT_JXPJ_ZPBG_BGGL WHERE SCBJ=0 AND PCID=a.ID) GLBGMC from T_DT_JXPJ_ZPBG_PCGL a
        where a.scbj = 0
        <if test="pcgl.pcmc!=null and pcgl.pcmc!=''">
            and a.pcmc LIKE concat( '%', #{pcgl.pcmc}, '%' )
        </if>
        ORDER BY ZSZT DESC,a.ID DESC
    </select>
    <!-- 版本管理数据查询 -->
    <select id="selectPcglPageCjrqDesc" resultType="com.xpaas.zpbg.vo.PcglVO">
        select a.*,(SELECT GROUP_CONCAT(BGMC) FROM T_DT_JXPJ_ZPBG_BGGL WHERE SCBJ=0 AND PCID=a.ID) GLBGMC from T_DT_JXPJ_ZPBG_PCGL a
        where a.scbj = 0
        <if test="pcgl.pcmc!=null and pcgl.pcmc!=''">
            and a.pcmc LIKE concat( '%', #{pcgl.pcmc}, '%' )
        </if>
        ORDER BY a.ID DESC
    </select>



</mapper>
