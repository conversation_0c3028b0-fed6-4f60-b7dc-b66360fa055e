package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Gjcznjc;
import com.xpaas.zpbg.mapper.GjcznjcMapper;
import com.xpaas.zpbg.service.IGjcznjcService;
import com.xpaas.zpbg.vo.GjcznjcVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-关键词智能检测 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Slf4j
@Service
public class GjcznjcServiceImpl extends BaseServiceImpl<GjcznjcMapper, Gjcznjc> implements IGjcznjcService {

	@Override
	public List<GjcznjcVO> selectGjcznjc(GjcznjcVO gjcznjc) {
		return baseMapper.selectGjcznjc(gjcznjc);
	}

	@Override
	public boolean removeGjcznjc(Gjcznjc gjcznjc) {
		return baseMapper.removeGjcznjc(gjcznjc);
	}

}
