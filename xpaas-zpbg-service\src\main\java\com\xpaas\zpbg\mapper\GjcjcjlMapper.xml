<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.GjcjcjlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcjcjlResultMap" type="com.xpaas.zpbg.entity.Gjcjcjl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="GJCGS" property="gjcgs"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcjcjlResultMapVO" type="com.xpaas.zpbg.vo.GjcjcjlVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="GJCGS" property="gjcgs"/>

        <result column="BGMC" property="bgmc"/>
        <result column="JDMC" property="jdmc"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectGjcjcjlPage" resultMap="gjcjcjlResultMapVO">
        select
            jcjl.*,
            concat(bggl.ND, '年度', bggl.BGMC) as BGMC,
            jdgl.JDMC
        from
            T_DT_JXPJ_ZPBG_GJCJCJL jcjl
        left join
            T_DT_JXPJ_ZPBG_BGGL bggl
                on bggl.ID = jcjl.BGID
        left join
            T_DT_JXPJ_ZPBG_JDGL jdgl
                on jdgl.ID = jcjl.JDGLID
        where
            jcjl.scbj = 0

            <if test="gjcjcjl.bgid!=null">
                and jcjl.BGID = #{gjcjcjl.bgid}
            </if>
        order by
            jcjl.ID desc
    </select>

    <!--取得关键词列表-->
    <select id="getGjcList" resultType="com.xpaas.zpbg.vo.GjcglVO">
        SELECT
            gjc.id,
            gjc.GJCMC,
            gjc.GLFW,
            GROUP_CONCAT( gjcmk.MKID ) AS MKIDS
        FROM
            T_DT_JXPJ_ZPBG_GJCGL gjc
            INNER JOIN T_DT_JXPJ_ZPBG_BGGL bggl ON bggl.id = #{bgid}
            AND gjc.ZBLY = bggl.mklx
            LEFT JOIN T_DT_JXPJ_ZPBG_GJCGLMK gjcmk ON gjcmk.GJCID = gjc.ID
            AND gjc.GLFW = 2
        WHERE
            gjc.SCBJ = 0
        GROUP BY
            gjc.id
    </select>

    <!--取得文章内容-->
    <select id="getWznr" resultType="com.xpaas.zpbg.vo.GjcjcjlVO">
        SELECT
            a.BGMKID,
            bgmk.MKID,
            nr.BBID,
            nr.WJNR
        FROM
            (
                SELECT
                    zxbb.id AS BBID,
                    zxbb.BGMKID AS BGMKID
                FROM
                    T_DT_JXPJ_ZPBG_JDMKGL mkgl

                    INNER JOIN T_DT_JXPJ_ZPBG_BBGL zxbb
                        ON zxbb.BGID = #{bgid}
                        AND zxbb.JDGLID = #{jdglid}
                        AND zxbb.BBLX = 1
                        AND zxbb.SCBJ = 0
                        AND zxbb.BGMKID = mkgl.BGMKID
                WHERE
                    mkgl.JDGLID = #{jdglid}
            ) a
            INNER JOIN T_DT_JXPJ_ZPBG_BBWJNR nr ON nr.BBID = a.BBID
            INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.ID = a.BGMKID
    </select>

    <!--报告列表-->
    <select id="getBgList" resultType="com.xpaas.zpbg.vo.GjcjcjlVO">
        SELECT
            id as bgid,
            concat(ND, '年度', BGMC) as bgmc
        FROM
            T_DT_JXPJ_ZPBG_BGGL
        WHERE
            scbj = 0
        ORDER BY
            px,	id desc
    </select>

</mapper>
