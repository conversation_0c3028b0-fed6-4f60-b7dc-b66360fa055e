package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Timeline;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

;

/**
 * 自评报告-首页甘特图视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TimelineVO对象", description = "自评报告-首页-甘特图")
public class TimelineVO extends Timeline {
	private static final long serialVersionUID = 1L;

	private String unitType;

	private String format;

	private String tooltipFormat;

}
