package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tool.utils.Func;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class OnlyOfflceTreeVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "数值")
    private String value;

    @ApiModelProperty(value = "上级ID")
    private String sjid;

    @ApiModelProperty(value = "版本管理ID")
    private String bbglId;

    @ApiModelProperty(value = "树形结构-子节点")
    private List<OnlyOfflceTreeVO> children;

    @ApiModelProperty(value = "是否选择")
    private boolean isIsSelected;

    @ApiModelProperty(value = "是否有子节点")
    @JsonSerialize(using = ToStringSerializer.class)
    private long hasChildren;

    @ApiModelProperty(value = "是否是叶子")
    private boolean isIsLeaf;

    @ApiModelProperty(value = "是否禁用")
    private boolean disabled;

    @ApiModelProperty(value = "版本类型")
    private String bblx;

    @ApiModelProperty(value = "文件路径")
    private String wjlj;

    @ApiModelProperty(value = "主要关注点ID")
    private String zygzdId;

    @ApiModelProperty(value = "一级指标ID")
    private String yjzbId;

    @ApiModelProperty(value = "一级指标")
    private String yjzb;
    @ApiModelProperty(value = "二级指标ID")
    private String ejzbId;
    @ApiModelProperty(value = "二级指标")
    private String ejzb;

    @ApiModelProperty(value = "点长列表")
    private List<Map<String,String>> dzList;

    @ApiModelProperty(value = "报告模块状态")
    private String bgmkzt;
    @ApiModelProperty(value = "模块名称")
    private String mkmc;
    @ApiModelProperty(value = "全部指标名称")
    private String qbzbmc;

    public String getQbzbmc() {
        if(Func.isNotBlank(yjzb)&&Func.isNotBlank(ejzb)&&Func.isNotBlank(mkmc)){
            return yjzb+"/"+ejzb+"/"+mkmc;
        }else{
            return "";
        }
    }
}
