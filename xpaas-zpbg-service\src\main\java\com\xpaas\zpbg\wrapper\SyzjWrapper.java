package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Syzj;
import com.xpaas.zpbg.vo.SyzjVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-审阅专家包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Component
public class SyzjWrapper extends BaseEntityWrapper<Syzj, SyzjVO>  {


	@Override
	public SyzjVO entityVO(Syzj syzj) {
		SyzjVO syzjVO = Objects.requireNonNull(BeanUtil.copy(syzj, SyzjVO.class));
		//User cjr = UserCache.getUser(syzj.getCjr());
		//if (cjr != null){
		//	syzjVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(syzj.getGxr());
		//if (gxr != null){
		//	syzjVO.setGxrName(gxr.getName());
		//}
		return syzjVO;
	}

    @Override
    public SyzjVO wrapperVO(SyzjVO syzjVO) {
		//User cjr = UserCache.getUser(syzjVO.getCjr());
		//if (cjr != null){
		//	syzjVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(syzjVO.getGxr());
		//if (gxr != null){
		//	syzjVO.setGxrName(gxr.getName());
		//}
        return syzjVO;
    }

}
