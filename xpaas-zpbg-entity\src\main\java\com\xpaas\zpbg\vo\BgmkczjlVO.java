package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bgmkczjl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-报告模块操作记录视图实体类
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BgmkczjlVO对象", description = "教学评价-自评报告-报告模块操作记录")
public class BgmkczjlVO extends Bgmkczjl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
