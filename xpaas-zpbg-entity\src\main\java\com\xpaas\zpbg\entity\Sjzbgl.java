package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-数据指标关联实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_SJZBGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Sjzbgl对象", description = "教学评价-自评报告-数据指标关联")
public class Sjzbgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 关联KEY
	*/
	@ExcelProperty("关联KEY")
	@ApiModelProperty(value = "关联KEY")
	@TableField("GLKEY")
	private String glkey;

	/**
	 * 关联文字
	 */
	@ExcelProperty("关联文字")
	@ApiModelProperty(value = "关联文字")
	@TableField("GLWZ")
	private String glwz;

	/**
	* 数据指标类型
	*/
	@ExcelProperty("数据指标类型")
	@ApiModelProperty(value = "数据指标类型")
	@TableField("SJZBLX")
	private Integer sjzblx;

	/**
	* 指标名称
	*/
	@ExcelProperty("指标名称")
	@ApiModelProperty(value = "指标名称")
	@TableField("ZBMC")
	private String zbmc;

	/**
	* 参考结果
	*/
	@ExcelProperty("参考结果")
	@ApiModelProperty(value = "参考结果")
	@TableField("CKJG")
	private String ckjg;

	/**
	* 数据来源
	*/
	@ExcelProperty("数据来源")
	@ApiModelProperty(value = "数据来源")
	@TableField("SJLY")
	private String sjly;

	/**
	* 数据来源名称
	*/
	@ExcelProperty("数据来源名称")
	@ApiModelProperty(value = "数据来源名称")
	@TableField("SJLYMC")
	private String sjlymc;

	/**
	 * 数据表名称-不修改
	 */
	@ExcelProperty("数据表名称-不修改")
	@ApiModelProperty(value = "数据表名称-不修改")
	@TableField("SJBMCORG")
	private String sjbmcorg;

	/**
	* 关键词ID
	*/
	@ExcelProperty("关键词ID")
	@ApiModelProperty(value = "关键词ID")
	@TableField("GJCID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long gjcid;

	/**
	* 关键词名称
	*/
	@ExcelProperty("关键词名称")
	@ApiModelProperty(value = "关键词名称")
	@TableField("GJCMC")
	private String gjcmc;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;



}
