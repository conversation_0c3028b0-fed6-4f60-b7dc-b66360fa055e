package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Sjclssmk;
import com.xpaas.zpbg.vo.SjclssmkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-数据材料所属模块包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Component
public class SjclssmkWrapper extends BaseEntityWrapper<Sjclssmk, SjclssmkVO>  {


	@Override
	public SjclssmkVO entityVO(Sjclssmk sjclssmk) {
		SjclssmkVO sjclssmkVO = Objects.requireNonNull(BeanUtil.copy(sjclssmk, SjclssmkVO.class));
		//User cjr = UserCache.getUser(sjclssmk.getCjr());
		//if (cjr != null){
		//	sjclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjclssmk.getGxr());
		//if (gxr != null){
		//	sjclssmkVO.setGxrName(gxr.getName());
		//}
		return sjclssmkVO;
	}

    @Override
    public SjclssmkVO wrapperVO(SjclssmkVO sjclssmkVO) {
		//User cjr = UserCache.getUser(sjclssmkVO.getCjr());
		//if (cjr != null){
		//	sjclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjclssmkVO.getGxr());
		//if (gxr != null){
		//	sjclssmkVO.setGxrName(gxr.getName());
		//}
        return sjclssmkVO;
    }

}
