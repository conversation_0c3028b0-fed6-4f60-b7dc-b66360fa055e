package com.xpaas.zpbg.service;

import com.xpaas.zpbg.vo.ZtProcInfoVO;
import com.xpaas.zpbg.vo.ZtProcType;

import java.util.List;

/**
 * 教学评价-自评报告-状态管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface IZtglService {

    /**
     * 加锁报告记录
     */
    void lockBg(Long bgid);

    /**
     * 【场景处理】报告创建、报告进度维护
     */
    void whileJdChange(Long bgid);

    /**
     * 【场景处理】日期更替，活跃报告ID获取
     */
    List<Long> whileDayChangeBgidList();

    /**
     * 【场景处理】日期更替，活跃报告处理
     */
    void whileDayChange(Long bgid);


    /**
     * 【场景处理】日期更替，消息发送处理
     */
    void whileDayChangeMessage();

    /**
     * 【场景处理】撰写开始、点长提交、审阅开始、审阅提交、审阅完成、点长定稿处理
     */
    ZtProcInfoVO whileProc(ZtProcType procType, Long bgid, Long bbid, ZtProcInfoVO procInfo);

    /**
     * 根据专家意见数检查是否可定稿
     */
    int selectZjyjCnt(Long bbid);


    /**
     * 报告取消定稿
     */
    void qxdg(Long bgid);

    /**
     * 单个版本  报告取消定稿
     */
    void qxdgD(Long bgid,Long bbid);

    /////////////////////////////////////////////////////////////////
    // 测试
    /////////////////////////////////////////////////////////////////

}