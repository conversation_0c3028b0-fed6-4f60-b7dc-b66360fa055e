package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Sjzbgl;
import com.xpaas.zpbg.vo.SjzbglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-数据指标关联包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Component
public class SjzbglWrapper extends BaseEntityWrapper<Sjzbgl, SjzbglVO>  {


	@Override
	public SjzbglVO entityVO(Sjzbgl sjzbgl) {
		SjzbglVO sjzbglVO = Objects.requireNonNull(BeanUtil.copy(sjzbgl, SjzbglVO.class));
		//User cjr = UserCache.getUser(sjzbgl.getCjr());
		//if (cjr != null){
		//	sjzbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjzbgl.getGxr());
		//if (gxr != null){
		//	sjzbglVO.setGxrName(gxr.getName());
		//}
		return sjzbglVO;
	}

    @Override
    public SjzbglVO wrapperVO(SjzbglVO sjzbglVO) {
		//User cjr = UserCache.getUser(sjzbglVO.getCjr());
		//if (cjr != null){
		//	sjzbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjzbglVO.getGxr());
		//if (gxr != null){
		//	sjzbglVO.setGxrName(gxr.getName());
		//}
        return sjzbglVO;
    }

}
