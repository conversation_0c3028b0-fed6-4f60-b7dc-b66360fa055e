package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.vo.ZzclVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-佐证材料包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Component
public class ZzclWrapper extends BaseEntityWrapper<Zzcl, ZzclVO>  {


	@Override
	public ZzclVO entityVO(Zzcl zzcl) {
		ZzclVO zzclVO = Objects.requireNonNull(BeanUtil.copy(zzcl, ZzclVO.class));
		//User cjr = UserCache.getUser(zzcl.getCjr());
		//if (cjr != null){
		//	zzclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzcl.getGxr());
		//if (gxr != null){
		//	zzclVO.setGxrName(gxr.getName());
		//}
		return zzclVO;
	}

    @Override
    public ZzclVO wrapperVO(ZzclVO zzclVO) {
		//User cjr = UserCache.getUser(zzclVO.getCjr());
		//if (cjr != null){
		//	zzclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzclVO.getGxr());
		//if (gxr != null){
		//	zzclVO.setGxrName(gxr.getName());
		//}
        return zzclVO;
    }

}
