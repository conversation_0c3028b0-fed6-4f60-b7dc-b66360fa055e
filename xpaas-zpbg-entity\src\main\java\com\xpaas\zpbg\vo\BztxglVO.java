package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bztxgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-标准体系管理视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "bztxglVO对象", description = "教学评价-自评报告-标准体系管理")
public class BztxglVO extends Bztxgl {

	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人")
    private String cjrName;

    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "全局搜索参数")
    private String qjss;

    @ApiModelProperty(value = "下级数量")
    private Integer xjsl;

    @ApiModelProperty(value = "级别")
    private Integer jb;

    @ApiModelProperty(value = "指标名称")
    private String zbmc;

    @ApiModelProperty(value = "指标ID")
    private String zbid;

    @ApiModelProperty(value = "树形结构-子节点")
    private List<BztxglVO> children;

    @ApiModelProperty(value = "是否有子节点")
    private boolean hasChildren;


}
