package com.xpaas.zpbg.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Data
public class HeaderValueDTO {

    // 分工类型
    private Integer fglx;
    // 单位ID
    @JsonSerialize(using = ToStringSerializer.class)
    private long dwid;
   //撰写人单位
    private String dwmcZxr;
    //撰写人姓名
    private String ryxmZxr;
    //点长单位
    private String dwmcDz;
    //点长姓名
    private String ryxmDz;
    //专家姓名
    private String zjxm;
 //姓名
 private String ryxm;
 //开始时间(DB)
 private Date kssj;
 //撰写结束时间(DB)
 private Date zxjssj;
 //审阅结束时间(DB)
 private Date syjssj;
 //开始时间
    //单位
    private String dwmc;
    private String startTime;
    //撰写结束时间
    private String zxEndTime;
    //审阅结束时间
    private String syEndTime;
    // 报告名称
    private String bgmc;
    // 模块名称
    private String mkmc;
    // 进度名称
    private String jdmc;
    // 报告ID
    @JsonSerialize(using = ToStringSerializer.class)
    private String bgid;
    // 模块ID
    @JsonSerialize(using = ToStringSerializer.class)
    private String bgmkid;
    // 进度管理ID
    @JsonSerialize(using = ToStringSerializer.class)
    private String jdglid;

    // 版本类型
    private String bblx;

    // 专家意见
    private boolean zjyjIsNull;
    //撰写人姓名List
    private List<Map<String,String>> ryxmZxrLsit;

    //处理状态
    private String clzt;

    //撰写人处理状态
    private String zxrclzt;
}
