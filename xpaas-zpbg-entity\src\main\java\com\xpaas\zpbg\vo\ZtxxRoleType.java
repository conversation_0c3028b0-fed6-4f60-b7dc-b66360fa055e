package com.xpaas.zpbg.vo;

public enum ZtxxRoleType {
    zxr("zxr"), // 撰写人
    dz("dz"), // 点长
    zxrOrDz("zxrOrDz"), // 撰写人或点长
    zj("zj"), // 专家
    gly("gly"); // 管理员

    private final String value;

    ZtxxRoleType(String value) {
        this.value = value;
    }

    public static ZtxxRoleType fromValue(String value) {
        for (ZtxxRoleType role : ZtxxRoleType.values()) {
            if (role.toString().equals(value)) {
                return role;
            }
        }
        throw new IllegalArgumentException("非法枚举值：" + value);
    }

    @Override
    public String toString() {
        return this.value;
    }
}
