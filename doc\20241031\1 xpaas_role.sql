-- 【0】注意：本次发布需要先执行各个脚本、进行配置后，再启动服务！！！
-- 【1】如果现场环境ID有冲突，需要联系沟通
-- 【2】这两个新角色ID，需配置到nacos中，参见《nacos增加角色配置.txt》
-- 【3】这两个新角色，要参考旧角色，重新配置相关菜单权限、重新关联教评管理及校内专家人员
-- 【4】原有“专家进院—专家”角色，需要给撰写平台应用权限


INSERT INTO xpaas_role (ID,ZHID,PARENT_ID,ROLE_NAME,SORT,ROLE_ALIAS,SCBJ,UNIT_ID,CJDW,SYN_ROLE_ID,CJRQ,`TYPE`,BIND_USER_COUNT) VALUES
	 (1851074890955853825,'000000',0,'自评报告撰写平台-专家',210,'ZPBGZXPT_ZJ',0,0,NULL,NULL,NULL,0,0),
	 (1851075004206256130,'000000',0,'自评报告撰写平台-管理员',211,'ZPBGZXPT_GLY',0,0,NULL,NULL,NULL,0,0);
