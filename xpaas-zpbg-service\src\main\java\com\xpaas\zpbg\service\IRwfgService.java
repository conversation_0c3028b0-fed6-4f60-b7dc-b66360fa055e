package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.dto.RwryDTO;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.vo.RwfgVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教学评价-自评报告-任务分工 服务类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface IRwfgService extends BaseService<Rwfg> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rwfg
	 * @return
	 */
	IPage<RwfgVO> selectRwfgPage(IPage<RwfgVO> page, RwfgVO rwfg);

//	/**
//	 * 查询任务人员信息
//	 *
//	 * @param bggl
//	 * @return
//	 */
//	List<RwryDTO> selectMkry(IPage<RwfgVO> page,Bggl bggl);

	/**
	 * 查询任务人员信息
	 *
	 * @param bggl
	 * @return
	 */
	List<RwryDTO> selectMkry(Bggl bggl);

	/**
	 * 先删除再保存任务人员信息
	 *
	 * @param rwfgList
	 * @return
	 */
	boolean deleteAndSave(List<Rwfg> rwfgList);


	/**
	 *
	 * 根据条件查询任务人员信息
	 *
	 * @param rwryWrapper
	 * @return
	 */
	List<RwryDTO> selectMkryByWrapper(@Param(Constants.WRAPPER) Wrapper<RwryDTO> rwryWrapper);

	/**
	 * 演示报告导入撰写人
	 *
	 * @param rwfg
	 * @return
	 */
	boolean zxrImportSave(Rwfg rwfg);

}
