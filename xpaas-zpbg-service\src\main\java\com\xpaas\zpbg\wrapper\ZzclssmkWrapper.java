package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zzclssmk;
import com.xpaas.zpbg.vo.ZzclssmkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-佐证材料所属模块包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Component
public class ZzclssmkWrapper extends BaseEntityWrapper<Zzclssmk, ZzclssmkVO>  {


	@Override
	public ZzclssmkVO entityVO(Zzclssmk zzclssmk) {
		ZzclssmkVO zzclssmkVO = Objects.requireNonNull(BeanUtil.copy(zzclssmk, ZzclssmkVO.class));
		//User cjr = UserCache.getUser(zzclssmk.getCjr());
		//if (cjr != null){
		//	zzclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzclssmk.getGxr());
		//if (gxr != null){
		//	zzclssmkVO.setGxrName(gxr.getName());
		//}
		return zzclssmkVO;
	}

    @Override
    public ZzclssmkVO wrapperVO(ZzclssmkVO zzclssmkVO) {
		//User cjr = UserCache.getUser(zzclssmkVO.getCjr());
		//if (cjr != null){
		//	zzclssmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzclssmkVO.getGxr());
		//if (gxr != null){
		//	zzclssmkVO.setGxrName(gxr.getName());
		//}
        return zzclssmkVO;
    }

}
