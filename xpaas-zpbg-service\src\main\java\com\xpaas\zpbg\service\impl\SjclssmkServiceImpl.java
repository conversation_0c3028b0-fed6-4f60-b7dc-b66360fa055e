package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Sjclssmk;
import com.xpaas.zpbg.mapper.SjclssmkMapper;
import com.xpaas.zpbg.service.ISjclssmkService;
import com.xpaas.zpbg.vo.SjclssmkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-数据材料所属模块 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Service
public class SjclssmkServiceImpl extends BaseServiceImpl<SjclssmkMapper, Sjclssmk> implements ISjclssmkService {

	@Override
	public IPage<SjclssmkVO> selectSjclssmkPage(IPage<SjclssmkVO> page, SjclssmkVO sjclssmk) {
		return page.setRecords(baseMapper.selectSjclssmkPage(page, sjclssmk));
	}

	@Override
	public List<SjclssmkVO> selectSjclssmkList(SjclssmkVO sjclssmk) {
		return baseMapper.selectSjclssmkList(sjclssmk);
	}

	@Override
	public boolean sjclssmkSave(SjclssmkVO sjclssmk) {
		// 删除数据材料的所属模块
		baseMapper.sjclssmkDelete(sjclssmk);

		// 所属模块保存
		for(Long mkid : sjclssmk.getMkidList()){
			Sjclssmk item = new Sjclssmk();
			item.setSjclid(sjclssmk.getSjclid());
			item.setMkid(mkid);
			super.save(item);
		}
		return true;
	}


}
