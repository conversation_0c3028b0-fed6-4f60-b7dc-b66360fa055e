package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.vo.BbglVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-版本管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public interface BbglMapper extends BaseMapper<Bbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	List<BbglVO> selectBbglPage(IPage page, BbglVO bbgl);


	/**
	 * 自定义查询
	 *
	 * @param page
	 * @param listId
	 * @return
	 */
	List<BbglVO> listByParams(IPage page, @Param("listId") List<Long> listId, BbglVO bbgl);

	/**
	 * 历史版本
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	List<BbglVO> selectLsbbPage(IPage<BbglVO> page, BbglVO bbgl);

	/**
	 * 历史版本
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	List<BbglVO> selectLsbbjpPage(IPage<BbglVO> page, BbglVO bbgl);

	/**
	 * 历史意见用
	 *
	 * @param bgmkid
	 * @return
	 */
	List<BbglVO> getLsyjList(String bgmkid);

}
