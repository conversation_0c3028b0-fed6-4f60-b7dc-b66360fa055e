<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZjjdMapper">

    <select id="getTreeOne" resultType="com.xpaas.zpbg.vo.ZjjdVO">
        select
            DISTINCT
            b.YJZBID as value,
            b.YJZB as label,
            1 as jb
        from
            t_dt_jxpj_zskgl_bztxgl b
        left join
            t_dt_jxpj_sz_zygzdgl sz on sz.ID = b.YJZBID
        where
            b.SCBJ = 0

        order by
            sz.PX
    </select>

    <select id="getTreeTwo" resultType="com.xpaas.zpbg.vo.ZjjdVO">

        select
            DISTINCT
            b.EJZBID as value,
            b.EJZB as label,
            2 as jb
        from
            t_dt_jxpj_zskgl_bztxgl b
        left join
            t_dt_jxpj_sz_zygzdgl sz on sz.ID = b.EJZBID
        where
            b.SCBJ = 0

            <if test="sjid != null and sjid != ''">
                and b.YJZBID = #{sjid}
            </if>
        order by
            sz.PX

    </select>

    <select id="getTreeThree" resultType="com.xpaas.zpbg.vo.ZjjdVO">
        select
            DISTINCT
            b.ZYGZDID as value,
            b.ZYGZD as label,
            3 as jb
        from
         t_dt_jxpj_zskgl_bztxgl b
        left join
            t_dt_jxpj_sz_zygzdgl sz on sz.ID = b.ZYGZDID
        where
            b.SCBJ = 0
            <if test="mklx != null and mklx != ''">
                AND sz.ZBLY = #{mklx}
            </if>
            <if test="sjid != null and sjid != ''">
                and b.EJZBID = #{sjid}
            </if>
        order by
            sz.PX

    </select>

</mapper>
