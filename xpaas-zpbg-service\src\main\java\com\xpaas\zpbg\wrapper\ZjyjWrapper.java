package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zjyj;
import com.xpaas.zpbg.vo.ZjyjVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-专家意见包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Component
public class ZjyjWrapper extends BaseEntityWrapper<Zjyj, ZjyjVO>  {


	@Override
	public ZjyjVO entityVO(Zjyj zjyj) {
		ZjyjVO zjyjVO = Objects.requireNonNull(BeanUtil.copy(zjyj, ZjyjVO.class));
		//User cjr = UserCache.getUser(zjyj.getCjr());
		//if (cjr != null){
		//	zjyjVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zjyj.getGxr());
		//if (gxr != null){
		//	zjyjVO.setGxrName(gxr.getName());
		//}
		return zjyjVO;
	}

    @Override
    public ZjyjVO wrapperVO(ZjyjVO zjyjVO) {
		//User cjr = UserCache.getUser(zjyjVO.getCjr());
		//if (cjr != null){
		//	zjyjVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zjyjVO.getGxr());
		//if (gxr != null){
		//	zjyjVO.setGxrName(gxr.getName());
		//}
        return zjyjVO;
    }

}
