package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Gjcglmk;
import com.xpaas.zpbg.vo.GjcglmkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-关键词关联模块包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Component
public class GjcglmkWrapper extends BaseEntityWrapper<Gjcglmk, GjcglmkVO>  {


	@Override
	public GjcglmkVO entityVO(Gjcglmk gjcglmk) {
		GjcglmkVO gjcglmkVO = Objects.requireNonNull(BeanUtil.copy(gjcglmk, GjcglmkVO.class));
		//User cjr = UserCache.getUser(gjcglmk.getCjr());
		//if (cjr != null){
		//	gjcglmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcglmk.getGxr());
		//if (gxr != null){
		//	gjcglmkVO.setGxrName(gxr.getName());
		//}
		return gjcglmkVO;
	}

    @Override
    public GjcglmkVO wrapperVO(GjcglmkVO gjcglmkVO) {
		//User cjr = UserCache.getUser(gjcglmkVO.getCjr());
		//if (cjr != null){
		//	gjcglmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcglmkVO.getGxr());
		//if (gxr != null){
		//	gjcglmkVO.setGxrName(gxr.getName());
		//}
        return gjcglmkVO;
    }

}
