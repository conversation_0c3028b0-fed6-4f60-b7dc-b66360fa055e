package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Wzgl;
import com.xpaas.zpbg.mapper.WzglMapper;
import com.xpaas.zpbg.service.IWzglService;
import com.xpaas.zpbg.vo.WzglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自评报告-文章管理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@Service
public class WzglServiceImpl extends BaseServiceImpl<WzglMapper, Wzgl> implements IWzglService {

	@Autowired
	private WzglMapper wzglMapper;

	@Override
	public IPage<WzglVO> selectWzPage(IPage<WzglVO> page, WzglVO wzgl) {
		return page.setRecords(baseMapper.selectWzPage(page, wzgl));
	}

	@Override
	public List<WzglVO> getKcList() {
		return baseMapper.getKcList();
	}

	@Override
	public int getLlrs(WzglVO wzglVO) {
		return baseMapper.getLlrs(wzglVO);
	}
}
