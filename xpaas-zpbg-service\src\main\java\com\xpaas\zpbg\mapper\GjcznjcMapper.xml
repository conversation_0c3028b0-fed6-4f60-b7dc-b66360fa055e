<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.GjcznjcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcznjcResultMap" type="com.xpaas.zpbg.entity.Gjcznjc">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PC" property="pc"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcznjcResultMapVO" type="com.xpaas.zpbg.vo.GjcznjcVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GJCID" property="gjcid"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="PC" property="pc"/>
        <result column="GJCLX" property="gjclx"/>
    </resultMap>

    <!-- 智能检测-查询 -->
    <select id="selectGjcznjc" resultMap="gjcznjcResultMapVO">
        (select
            #{bgid} as bgid,
            #{bgmkid} as bgmkid,
            #{bbid} as bbid,
            gjcgl_symk.id as gjcid,
            gjcgl_symk.gjcmc
        from
            T_DT_JXPJ_ZPBG_GJCGL gjcgl_symk
            inner join t_dt_jxpj_zpbg_bggl bggl_symk
            on bggl_symk.id = #{bgid}
            and bggl_symk.mklx = gjcgl_symk.zbly
        where gjcgl_symk.scbj = 0
          and gjcgl_symk.gjclx = #{gjclx}
          and gjcgl_symk.glfw = 1)
    union all
        (select #{bgid}   as bgid,
                #{bgmkid} as bgmkid,
                #{bbid}   as bbid,
                gjcgl.id  as gjcid,
                gjcgl.gjcmc
         from T_DT_JXPJ_ZPBG_GJCGL gjcgl
                  inner join T_DT_JXPJ_ZPBG_GJCGLMK gjcglmk
                             on gjcgl.id = gjcglmk.gjcid
                                 and gjcglmk.mkid = #{mkid}
                                 and gjcglmk.scbj = 0
                  inner join t_dt_jxpj_zpbg_bggl bggl
                            on bggl.id = #{bgid}
                                 and bggl.mklx = gjcgl.zbly
         where gjcgl.scbj = 0
           and gjcgl.gjclx = #{gjclx}
           and gjcgl.glfw = 2
           and exists(select t1.id
                      from T_DT_JXPJ_ZPBG_GJCGL t1
                               inner join T_DT_JXPJ_ZPBG_GJCGLMK t2
                                          on t1.id = t2.gjcid
                                              and t2.mkid = #{mkid}
                                              and t2.scbj = 0
                               inner join T_DT_JXPJ_ZPBG_BGMK t3
                                          on t2.mkid = t3.mkid
                                              and t3.bgid = #{bgid}
                                              and t3.scbj = 0
                               inner join T_DT_JXPJ_ZPBG_BBGL t4
                                          on t3.bgid = t4.bgid
                                              and t4.id = #{bbid}
                                              and t4.scbj = 0))
        order by
            gjcmc
    </select>

    <!-- 智能检测-删除 -->
    <delete id="removeGjcznjc">
        delete from T_DT_JXPJ_ZPBG_GJCZNJC where bgid = #{bgid} and bgmkid = #{bgmkid} and bbid = #{bbid}
    </delete>

</mapper>
