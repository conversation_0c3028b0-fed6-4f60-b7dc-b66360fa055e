package com.xpaas.zpbg.service;

import com.xpaas.zpbg.dto.HeaderValueDTO;
import com.xpaas.zpbg.vo.OnlyOfflceTreeVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * onlyOffice 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IOnlyOfficeService {

	/**
	 * 通过版本管理ID获取进度管理ID
	 * @param bbglId 版本管理ID
	 * @return
	 */
	HeaderValueDTO getJdglIdValue(String bbglId);

	/**
	 * 报告头部信息
	 * @param bbglId 版本管理ID
	 * @param bgid 报告ID
	 * @param bgmkid 报告模块ID
	 * @return
	 */
	HeaderValueDTO getHeaderValue(String bbglId,String bgid,String bgmkid);

	/**
	 * 获取模块树
	 * @param dto 参数
	 * @param role 角色
	 * @return
	 */
	List<OnlyOfflceTreeVO> getMkTreeValue(HeaderValueDTO dto,String role);

	/**
	 * onlyoffice回调
	 * @param param 参数
	 * @param request 请求
	 * @return
	 */
	public Map<String,Object> callBack(Map<String,String> param, HttpServletRequest request);

	/**
	 * 历史版本模块树
	 * @param bbglId 版本管理ID
	 * @return
	 */
	List<OnlyOfflceTreeVO> getBbAsMkTree(String bbglId);

	/**
	 * 踢人
	 * @param list
	 */
	void dropUsers(List<Map<String,Object>> list);

	/**
	 * 获取踢人提示信息
	 * @param params
	 * @return
	 */
	String getDropMsg(Map<String, Object> params);

	/**
	 * 获取批注内容
	 * @param bbid 版本ID
	 * @param list 批注ID
	 * @return
	 */
	String getCommentText(String bbid, List<String> list);

	/**
	 * 更新新版本
	 * @param params
	 * @return
	 */
	boolean updateNewBb(Map<String, String> params);

	/**
	 * 缓存提交版本KEY
	 * @param key
	 */
	void addTjCache(String key);

	/**
	 * 添加批注顺序
	 * @param params
	 * @return
	 */
	String addCommentOrder(Map<String, Object> params);

	/**
	 * 删除批注
	 * @param params 批注、版本ID
	 * @return
	 */
	String delComment(Map<String, String> params);

	/**
	 * 获取批注信息（专家进院）
	 * @param bgmkId 报告模块ID
	 * @param list 批注ID
	 * @return
	 */
    String getCommentTextZm(String bgmkId, List<String> list);
}

