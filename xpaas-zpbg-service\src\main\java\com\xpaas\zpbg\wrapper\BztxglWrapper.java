package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bztxgl;
import com.xpaas.zpbg.vo.BztxglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-知识库管理-标准体系管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Component
public class BztxglWrapper extends BaseEntityWrapper<Bztxgl, BztxglVO>  {


	@Override
	public BztxglVO entityVO(Bztxgl bztxgl) {
		BztxglVO bztxglVO = Objects.requireNonNull(BeanUtil.copy(bztxgl, BztxglVO.class));
		//User cjr = UserCache.getUser(pjtxgl.getCjr());
		//if (cjr != null){
		//	pjtxglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(pjtxgl.getGxr());
		//if (gxr != null){
		//	pjtxglVO.setGxrName(gxr.getName());
		//}
		return bztxglVO;
	}

    @Override
    public BztxglVO wrapperVO(BztxglVO pjtxglVO) {
		//User cjr = UserCache.getUser(pjtxglVO.getCjr());
		//if (cjr != null){
		//	pjtxglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(pjtxglVO.getGxr());
		//if (gxr != null){
		//	pjtxglVO.setGxrName(gxr.getName());
		//}
        return pjtxglVO;
    }

}
