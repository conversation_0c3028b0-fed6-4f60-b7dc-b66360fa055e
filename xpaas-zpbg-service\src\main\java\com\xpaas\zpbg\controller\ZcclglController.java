package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.service.IZcclService;
import com.xpaas.zpbg.service.IZcclglService;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZcclglVO;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;
import com.xpaas.zpbg.wrapper.ZcclglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 教学评价-自评报告-备查材料关联 控制器
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zcclgl")
@Api(value = "教学评价-自评报告-备查材料关联", tags = "教学评价-自评报告-备查材料关联接口")
public class ZcclglController extends BaseController {

    private ZcclglWrapper zcclglWrapper;
    private IZcclglService zcclglService;
    private IZcclService zcclService;
    private IZzclService zzclService;
    @Autowired
    private ResourceLoader resourceLoader;
    @Autowired
    private IOssClient ossClient;

    private static final String BUCKET_NAME = "minio11";



    /**
     * 查询的所有数据
     */
    @GetMapping("/allList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "所有", notes = "传入zcclgl")
    @ApiLog("备查材料关联-查询的所有数据")
    public R allList(Zcclgl zcclgl, Query query) {
        zcclgl.setScbj(0);
        List<ZcclglVO> list = zcclglService.getZclglList(zcclgl);
        return R.data(list);
    }
    /**
     * 更新顺序
     */
    @GetMapping("/updataPx")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "所有", notes = "传入zcclgl")
    @ApiLog("备查材料关联-更新排序顺序")
    public R updataPx(String id,String newpx){
        boolean flg = zcclglService.updataPx(id,newpx);
        return R.status(flg);
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入zcclgl")
    @ApiLog("备查材料关联-详情")
    public R<ZcclglVO> detail(Zcclgl zcclgl) {
        Zcclgl detail = zcclglService.getOne(Condition.getQueryWrapper(zcclgl));
        return R.data(zcclglWrapper.entityVO(detail));
    }

    /**
     * 分页 教学评价-自评报告-备查材料关联 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入zcclgl")
    @ApiLog("备查材料关联-分页")
    public R<IPage<ZcclglVO>> list(Zcclgl zcclgl, Query query) {
        IPage<Zcclgl> pages = zcclglService.page(Condition.getPage(query), Condition.getQueryWrapper(zcclgl));
        return R.data(zcclglWrapper.pageVO(pages));
    }

    /**
     * 自定义分页 教学评价-自评报告-备查材料关联
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入zcclgl")
    @ApiLog("备查材料关联-自定义分页")
    public R<IPage<ZcclglVO>> page(ZcclglVO zcclgl, Query query) {
        IPage<ZcclglVO> pages = zcclglService.selectZcclglPage(Condition.getPage(query), zcclgl);
        return R.data(zcclglWrapper.wrapperPageVO(pages));
    }


    /**
     * 新增 教学评价-自评报告-备查材料关联
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入zcclgl")
    @ApiLog("备查材料关联-新增")
    public R save(@Valid @RequestBody List<ZcclglVO> zcclglVOs) {
        ZcclglVO  zcclgl = new ZcclglVO();
        Query query = new Query();
        int px = 1;
        query.setSize(-1);
        query.setCurrent(1);
        int  maxPx = zcclglService.getMaxPx();
        px = maxPx+1;
        for (ZcclglVO zcclglVO : zcclglVOs) {
            zcclglVO.setScbj(0);
            List<Zcclgl> list = zcclglService.list(Condition.getQueryWrapper(zcclglVO));
            if (list.size() > 0) {
                return R.fail("该材料已被引用");
            }
            if(zcclglVO.getZcclid()==null){
                ZcclVO zccl = new ZcclVO();
                zccl.setBcsm(zcclglVO.getBcsm());
                zccl.setCfwz(zcclglVO.getCfwz());
                zccl.setClmc(zcclglVO.getClmc());
                zccl.setWjlj(zcclglVO.getWjlj());
                List<ZcclVO> zcllList = zcclService.getZcclId(zccl);
                if(zcllList.size()>0){
                    zcclglVO.setZcclid(zcllList.get(0).getId());
                }
            }
            zcclglVO.setPx(px);
            boolean b = zcclglService.save(zcclglVO);
            px++;
        }
        return R.status(true);
    }

    /**
     * 新增多条记录 教学评价-自评报告-不同的内容引用了相同的备查材料
     */
    @PostMapping("/sameGl")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "不同的内容引用了相同的备查材料", notes = "传入zcclgl")
    @ApiLog("数据表关联-不同的内容引用了相同的备查材料")
    public R sameGl(@Valid @RequestBody ZcclglVO zcclglVO) {
        String res = checkGl(zcclglVO.getSelectList());
        if(!"".equals(res)){
            return R.data("0");
        }
        String msg = zcclglService.sameGl(zcclglVO);
        return R.data(msg);
    }

    public String checkGl(List<Zcclgl> zcclglVOs) {
        Query query = new Query();
        query.setSize(-1);
        query.setCurrent(1);
        for (Zcclgl zcclglVO : zcclglVOs) {
            zcclglVO.setPx(null);
            // 没有关联KEY时，不用校验
            if (StringUtil.isEmpty(zcclglVO.getGlkey())) {
                return "";
            }
            zcclglVO.setScbj(0);
            List<Zcclgl> list = zcclglService.list(Condition.getQueryWrapper(zcclglVO));
            if (list.size() > 0) {
                return "此关联已添加！";
            }
        }
        return "";
    }

    /**
     * 修改 教学评价-自评报告-备查材料关联
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入zcclgl")
    @ApiLog("备查材料关联-修改")
    public R update(@Valid @RequestBody ZcclglVO zcclglVO) {
        boolean b = zcclglService.updateById(zcclglVO);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-备查材料关联 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入zcclgl")
    @ApiLog("备查材料关联-新增或修改")
    public R submit(@Valid @RequestBody Zcclgl zcclgl) {
        return R.status(zcclglService.saveOrUpdate(zcclgl));
    }


    /**
     * 删除 教学评价-自评报告-备查材料关联
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("备查材料关联-逻辑删除")
    public R remove(@Valid @RequestBody List<ZzclglVO> ids) {
        List<Long> longList = new ArrayList<>();
        if(ids.size()>0){
            for (ZzclglVO item :ids){
                longList.add(item.getId());
            }
        }
        boolean b = zcclglService.deleteLogic(longList);
        return R.status(b);
    }


    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("备查材料关联-高级查询")
    public R<IPage<ZcclglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Zcclgl> queryWrapper = Condition.getQueryWrapper(map, Zcclgl.class);
        IPage<Zcclgl> pages = zcclglService.page(Condition.getPage(query), queryWrapper);
        return R.data(zcclglWrapper.pageVO(pages));
    }


    /**
     * 导入Excel
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    @ApiLog("备查材料关联-导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Zcclgl> list = ExcelUtil.read(file, Zcclgl.class);
        zcclglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("备查材料关联-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Zcclgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Zcclgl> list = zcclglService.list(queryWrapper);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        ExcelUtil.export(response, "Zcclgl导入模板", "Zcclgl导入模板", columnFiledNames, list, Zcclgl.class);
    }

    /**
     * 导出Excel
     */
    @GetMapping("/exportExcel")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("备查材料关联-导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "报告ID") String bgid,
                            @ApiParam(value = "报告模块ID") String bgmkid,
                            @ApiParam(value = "版本ID") String bbid,
                            @ApiParam(value = "材料名称") String clmc) {

        ZcclglVO zcclglVO = new ZcclglVO();
        zcclglVO.setBgid(Long.parseLong(bgid));
        zcclglVO.setBgmkid(Long.parseLong(bgmkid));
        zcclglVO.setBbid(Long.parseLong(bbid));
        zcclglVO.setClmc(clmc);
        zcclglVO.setScbj(0);

        Query query = new Query();
        query.setCurrent(1);
        query.setSize(-1);

        IPage<Zcclgl> pages = zcclglService.page(Condition.getPage(query), Condition.getQueryWrapper(zcclglVO));

        export(response, pages.getRecords());
    }

    @GetMapping("/exportZip")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "材料库导出ZIP", notes = "材料库导出ZIP")
    @ApiLog("材料库导出ZIP")
    public void exportZip(HttpServletResponse response,
                          @ApiParam(value = "模块类型") Integer mklx,
                          @ApiParam(value = "年度") String nd,
                          @ApiParam(value = "材料名称") String clmc,
                          @ApiParam(value = "引用状态") Integer yyzt,
                          @ApiParam(value = "任务类型") Integer rwlx,
                          @ApiParam(value = "材料类型") String cllx,
                          @ApiParam(value = "所属模块") Long ssmkid,
                          @ApiParam(value = "多个所属模块") String ssmkids,
                          @ApiParam(value = "分页") Query query,
                          @ApiParam(value = "报告id") Long bgId) throws ServiceException {
        String method = "材料库导出ZIP接口";
        log.info("开始执行 {}", method);
        long startTime = System.currentTimeMillis();

        int successCount = 0;
        List<String> failedFiles = new ArrayList<>();
        List<Map<String, Object>> allFiles = new ArrayList<>();

        List<ZcclVO> zccllist = new ArrayList<>();
        List<ZzclVO> zzcllist = new ArrayList<>();

        //备查材料
        if ("2".equals(cllx)) {
            ZcclVO zccl = new ZcclVO();
            zccl.setMklx(mklx);
            zccl.setNd(nd);
            if (Func.isNotEmpty(clmc)) {
                zccl.setClmc(clmc);
            }
            if (Func.isNotEmpty(yyzt)) {
                zccl.setYyzt(yyzt);
            }
            zccl.setRwlx(rwlx);
            if (Func.isNotEmpty(bgId)) {
                zccl.setBgId(bgId);
            }
            if (Func.isNotEmpty(ssmkid)) {
                zccl.setSsmkid(ssmkid);
            }
            if (Func.isNotEmpty(ssmkids)) {
                zccl.setSsmkids(ssmkids);
            }
            if (Func.isEmpty(query)) {
                query = new Query();
                query.setSize(200);
                query.setCurrent(1);
            }
            IPage<ZcclVO> pages = zcclService.selectZcclPage(Condition.getPage(query), zccl);
            if (Func.isNotEmpty(pages.getRecords())) {
                zccllist = pages.getRecords();
            }
        }

        //佐证材料
        if ("1".equals(cllx)) {
            ZzclVO zzcl = new ZzclVO();
            zzcl.setMklx(mklx);
            zzcl.setNd(nd);
            if (Func.isNotEmpty(clmc)) {
                zzcl.setClmc(clmc);
            }
            if (Func.isNotEmpty(yyzt)) {
                zzcl.setYyzt(yyzt);
            }
            zzcl.setRwlx(rwlx);
            if (Func.isNotEmpty(bgId)) {
                zzcl.setBgId(String.valueOf(bgId));
            }
            if (Func.isNotEmpty(ssmkid)) {
                zzcl.setSsmkid(ssmkid);
            }
            if (Func.isNotEmpty(ssmkids)) {
                zzcl.setSsmkids(ssmkids);
            }
            if (Func.isEmpty(query)) {
                query = new Query();
                query.setSize(200);
                query.setCurrent(1);
            }
            IPage<ZzclVO> pages = zzclService.selectZzclPage(Condition.getPage(query), zzcl);
            if (Func.isNotEmpty(pages.getRecords())) {
                zzcllist = pages.getRecords();
            }
        }

        // 合并数据源
        zccllist.stream()
                .filter(item -> Func.isNotEmpty(item.getWjlj()))
                .forEach(item -> {
                    Map<String, Object> map = new HashMap<>(Func.toMap(item));
                    allFiles.add(map);
                });

        zzcllist.stream()
                .filter(item -> Func.isNotEmpty(item.getWjlj()))
                .forEach(item -> {
                    Map<String, Object> map = new HashMap<>(Func.toMap(item));
                    allFiles.add(map);
                });
        Set<String> processedNames = new HashSet<>();
        try (ServletOutputStream servletOut = response.getOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(servletOut, StandardCharsets.UTF_8)) {

            // 响应头设置
            response.setContentType("application/zip");
            response.setBufferSize(64 * 1024); // 增加缓冲区
            response.setHeader("Connection", "Keep-Alive");
            response.setHeader("Keep-Alive", "timeout=60000");
            String fileName = "材料_" + System.currentTimeMillis() + ".zip";
            response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");

            // 处理重名问题，生成唯一文件名
            for (Map<String, Object> file : allFiles) {
                String wjlj = Optional.ofNullable((String) file.get("wjlj")).orElse("");
                String mc = Optional.ofNullable((String) file.get("clmc")).orElse("");
                String cmm = Optional.ofNullable((String) file.get("cmm")).orElse("");
                String uniqueName = generateUniqueName(mc, wjlj, processedNames, cmm);
                file.put("uniqueName", uniqueName);
            }

            // 开始逐个下载并写入 ZIP
            for (Map<String, Object> file : allFiles) {
                String wjlj = (String) file.get("wjlj");
                String yclmc = (String) file.get("clmc");
                String cmm = (String) file.get("cmm");
                String uniqueName = (String) file.get("uniqueName");

                try (InputStream is = getFileStreamAsStream(wjlj)) {
                    if (is == null) {
                        failedFiles.add("材料名称:" + yclmc + " | 重命名:" + cmm + " | 文件路径:" + wjlj);
                        continue;
                    }

                    zipOut.putNextEntry(new ZipEntry(uniqueName));
                    byte[] buffer = new byte[8192];
                    int len;
                    while ((len = is.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, len); // 边下边写
                    }
                    zipOut.closeEntry();
                    successCount++;

                } catch (IOException e) {
                    log.error("{}: ZIP 写入失败 {}", method, uniqueName, e);
                    failedFiles.add("材料名称:" + yclmc + " | 重命名:" + cmm + " | 文件路径:" + wjlj);
                }
            }

            // 写入失败记录
            if (!failedFiles.isEmpty()) {
                String failedContent = "共 " + failedFiles.size() + " 个文件需要手动下载:\n" +
                        String.join("\n", failedFiles);
                zipOut.putNextEntry(new ZipEntry("需要手动下载的材料明细.txt"));
                zipOut.write(failedContent.getBytes(StandardCharsets.UTF_8));
                zipOut.closeEntry();
            }

            zipOut.flush();

        } catch (SocketException e) {
            log.error("{}:客户端中断下载", method);
        } catch (IOException e) {
            log.error("{}:ZIP 流写入失败", method, e);
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("{} 成功下载 {} 个文件，失败 {} 个，总耗时: {} ms ({} 秒)",
                method, successCount, failedFiles.size(), duration, duration / 1000.0);
    }

    private InputStream getFileStreamAsStream(String wjlj) {
        try {
            R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj, BUCKET_NAME);
            if (inputR.isSuccess()) {
                return new ByteArrayInputStream(inputR.getData());
            }
            log.error("文件下载失败: {}, 原因: {}", wjlj, inputR.getMsg());
            return null; // 返回 null 表示该文件下载失败
        } catch (Exception e) {
            log.error("OSS连接异常", e);
            return null;
        }
    }

    private String generateUniqueName(String clmc, String wjlj, Set<String> names,String cmm) {
        // 文件名生成逻辑（保持原有唯一性校验）
        String baseName = Func.isNotEmpty(clmc) ? clmc :
                wjlj.substring(wjlj.lastIndexOf('/') + 1);
        if (Func.isNotEmpty(cmm)) {
            baseName = baseName + "_" + cmm;
        }
        String extension = wjlj.contains(".") ?
                wjlj.substring(wjlj.lastIndexOf('.')) : "";

        int counter = 1;
        String uniqueName = baseName + extension;
        while (names.contains(uniqueName)) {
            uniqueName = baseName + "(" + counter++ + ")" + extension;
        }
        names.add(uniqueName);
        return uniqueName;
    }



    private void export(HttpServletResponse response, List<Zcclgl> dataList) {
        try {
            String path = "classpath:doc/zcclgl.xlsx";
            org.springframework.core.io.Resource resource = resourceLoader.getResource(path);
            InputStream inStream= resource.getInputStream();
            XSSFWorkbook wb = new XSSFWorkbook(inStream);
            response.reset();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(Charsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=zcclgl.xlsx");

            writeData(wb, dataList);
            wb.write(response.getOutputStream());
            wb.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void writeData(XSSFWorkbook wb, List<Zcclgl> list) {
        XSSFSheet sheet = wb.getSheetAt(0);
        int rowIndex = 1;
        Row row = null;

        if(list != null) {
            for(int i = 0; i < list.size(); i++) {
                Zcclgl vo = list.get(i);

                int colIndex = 0;
                // 插入行
                row = sheet.createRow(rowIndex);

                // 序号
                setCellValue(wb, row, (i + 1), colIndex);
                colIndex++;

                // 备查材料名称
                setCellValue(wb, row, vo.getClmc(), colIndex);
                colIndex++;

                // 存放位置
                setCellValue(wb, row, vo.getCfwz(), colIndex);
                colIndex++;

                // 备注
                setCellValue(wb, row, vo.getBcsm(), colIndex);
                colIndex++;
                rowIndex++;
            }
        }
    }

    private void setCellValue(XSSFWorkbook wb, Row row, Object str, int col) {
        Font titleFont = wb.createFont();
        titleFont.setFontName("微软雅黑");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setColor(IndexedColors.BLACK.index);
        XSSFCellStyle titleStyle = wb.createCellStyle();
        titleStyle.setWrapText(true);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFont(titleFont);

        byte[] rgb = new byte[]{(byte) 0, (byte) 0, (byte) 0};
        setBorder(titleStyle, BorderStyle.THIN, new XSSFColor(rgb ,null));
        Cell cell = row.createCell(col);
        if(str == null) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(String.valueOf(str));
        }

        cell.setCellStyle(titleStyle);
    }

    private void setBorder(XSSFCellStyle style, BorderStyle border, XSSFColor color) {
        style.setBorderTop(border);
        style.setBorderLeft(border);
        style.setBorderRight(border);
        style.setBorderBottom(border);
        style.setBorderColor(XSSFCellBorder.BorderSide.TOP, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, color);
    }

    /**
     * 校验关键词是否关联
     */
    @GetMapping("/checkGlkey")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "zcclglvo")
    @ApiLog("备查材料关联-校验关键词是否关联")
    public R checkGlkey(ZcclglVO zcclglvo) {
        if(zcclglvo.getGlkeyStr()!=null){
            zcclglvo.setGlkeyList(Arrays.asList(zcclglvo.getGlkeyStr().split(",")));
        }
        int flag = zcclglService.checkGlkey(zcclglvo);
        return R.data(flag);
    }
}
