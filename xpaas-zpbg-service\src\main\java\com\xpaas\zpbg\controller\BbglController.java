package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.service.IBbglService;
import com.xpaas.zpbg.service.IBgmkService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.BbglVO;
import com.xpaas.zpbg.wrapper.BbglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-版本管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bbgl")
@Api(value = "教学评价-自评报告-版本管理", tags = "教学评价-自评报告-版本管理接口")
public class BbglController extends BaseController {

    private BbglWrapper bbglWrapper;
    private IBbglService bbglService;
    private IBgmkService bgmkService;

    // # 自评报告撰写平台-专家-角色ID
    @Value("${xpaas.roles.zpbgzxptZjRoleId}")
    private String[] zpbgzxptZjRoleId;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入bbgl")
    @ApiLog("版本管理-详情")
    public R<BbglVO> detail(Bbgl bbgl) {
        Bbgl detail = bbglService.getOne(Condition.getQueryWrapper(bbgl));
        return R.data(bbglWrapper.entityVO(detail));
    }

    /**
     * 分页 教学评价-自评报告-版本管理 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入bbgl")
    @ApiLog("版本管理-分页")
    public R<IPage<BbglVO>> list(Bbgl bbgl, Query query) {
        IPage<Bbgl> pages = bbglService.page(Condition.getPage(query), Condition.getQueryWrapper(bbgl));
        return R.data(bbglWrapper.pageVO(pages));
    }

    /**
     * 自定义分页 教学评价-自评报告-版本管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bbgl")
    @ApiLog("版本管理-自定义分页")
    public R<IPage<BbglVO>> page(BbglVO bbgl, Query query) {
        IPage<BbglVO> pages = bbglService.selectBbglPage(Condition.getPage(query), bbgl);
        return R.data(bbglWrapper.wrapperPageVO(pages));
    }


    /**
     * 新增 教学评价-自评报告-版本管理
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入bbgl")
    @ApiLog("版本管理-新增")
    public R save(@Valid @RequestBody BbglVO bbglVO) {
        boolean b = bbglService.save(bbglVO);

        return R.status(b);
    }

    /**
     * 修改 教学评价-自评报告-版本管理
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入bbgl")
    @ApiLog("版本管理-修改")
    public R update(@Valid @RequestBody BbglVO bbglVO) {
        boolean b = bbglService.updateById(bbglVO);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-版本管理 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入bbgl")
    @ApiLog("版本管理-新增或修改")
    public R submit(@Valid @RequestBody Bbgl bbgl) {
        return R.status(bbglService.saveOrUpdate(bbgl));
    }


    /**
     * 删除 教学评价-自评报告-版本管理
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("版本管理-逻辑删除")
    public R remove(@RequestBody BaseDeleteVO deleteVO) {
        boolean b = bbglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
        return R.status(b);
    }


    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("版本管理-高级查询")
    public R<IPage<BbglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Bbgl> queryWrapper = Condition.getQueryWrapper(map, Bbgl.class);
        IPage<Bbgl> pages = bbglService.page(Condition.getPage(query), queryWrapper);
        return R.data(bbglWrapper.pageVO(pages));
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("版本管理-导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<Bbgl> queryWrapper = Condition.getQueryWrapper(map, Bbgl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<Bbgl> list = bbglService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Bbgl.class);
    }


    /**
     * 导入Excel
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    @ApiLog("版本管理-导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Bbgl> list = ExcelUtil.read(file, Bbgl.class);
        //TODO 此处需要根据具体业务添加代码
        bbglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("版本管理-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Bbgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Bbgl> list = bbglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        //TODO 此处需要根据具体业务添加代码
        //columnFiledNames.add("id");
        //columnFiledNames.add("cjrq");
        ExcelUtil.export(response, "Bbgl导入模板", "Bbgl导入模板", columnFiledNames, list, Bbgl.class);
    }

    /**
     * 自定义查询 教学评价-自评报告-版本管理
     */
    @GetMapping("/listByParams")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "自定义查询", notes = "传入bbgl")
    @ApiLog("版本管理-自定义查询")
    public R<IPage<BbglVO>> listByParams(BbglVO bbgl, Query query, Long mkid) {
        QueryWrapper<Bgmk> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MKID", mkid).or().eq("YJZBID", mkid).or().eq("EJZBID", mkid);
        List<Bgmk> bgmkList = bgmkService.list(queryWrapper);

        IPage<BbglVO> pages = bbglService.listByParamsPage(Condition.getPage(query), bgmkList, bbgl);
        return R.data(bbglWrapper.wrapperPageVO(pages));
    }

    /**
     * 历史版本 教学评价-自评报告-版本管理
     */
    @GetMapping("/lsbb")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bbgl")
    @ApiLog("版本管理-历史版本")
    public R<IPage<BbglVO>> lsbb(BbglVO bbgl, Query query) {
        IPage<BbglVO> pages = bbglService.selectLsbbPage(Condition.getPage(query), bbgl);
        return R.data(bbglWrapper.wrapperPageVO(pages));
    }


    /**
     * 教评中心历史版本 教学评价-自评报告-版本管理
     */
    @GetMapping("/lsbbjp")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bbgl")
    @ApiLog("版本管理-历史版本")
    public R<IPage<BbglVO>> lsbbjp(BbglVO bbgl, Query query) {
        IPage<BbglVO> pages = bbglService.selectLsbbjpPage(Condition.getPage(query), bbgl);
        return R.data(bbglWrapper.wrapperPageVO(pages));
    }


    /**
     * 专家角色id 教学评价-自评报告-版本管理
     */
    @GetMapping("/zjjsid")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bbgl")
    @ApiLog("版本管理-获取专家角色id")
    public R<String[]> zjjsid() {
        return R.data(zpbgzxptZjRoleId);
    }

}
