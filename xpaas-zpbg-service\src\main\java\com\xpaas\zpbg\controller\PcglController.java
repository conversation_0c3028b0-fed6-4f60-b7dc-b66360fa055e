package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Pcgl;
import com.xpaas.zpbg.entity.Zccl;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.service.*;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.PcglVO;
import com.xpaas.zpbg.wrapper.PcglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-批次管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pcgl")
@Api(value = "教学评价-自评报告-批次管理", tags = "教学评价-自评报告-批次管理接口")
public class PcglController extends BaseController {

    private PcglWrapper pcglWrapper;
    private IPcglService pcglService;
    private IBgglService bgglService;
    private IZcclService zcclService;
    private IZzclService zzclService;
    private IBgfzService bgfzService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入pcgl")
    @ApiLog("批次管理-详情")
    public R<PcglVO> detail(Pcgl pcgl) {
        Pcgl detail = pcglService.getOne(Condition.getQueryWrapper(pcgl));
        return R.data(pcglWrapper.entityVO(detail));
    }

    /**
     * 自定义分页 教学评价-自评报告-批次管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入pcgl")
    @ApiLog("批次管理-自定义分页")
    public R<IPage<PcglVO>> page(Pcgl pcgl, Query query) {
        IPage<PcglVO> pages = pcglService.selectPcglPage(Condition.getPage(query), pcgl);
        return R.data(pages);
    }
    /**
     * 自定义分页 教学评价-自评报告-批次管理
     */
    @GetMapping("/pageCjrqDesc")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入pcgl")
    @ApiLog("批次管理-自定义分页")
    public R<IPage<PcglVO>> pageCjrqDesc(Pcgl pcgl, Query query) {
        IPage<PcglVO> pages = pcglService.selectPcglPageCjrqDesc(Condition.getPage(query), pcgl);
        return R.data(pages);
    }


    /**
     * 新增 教学评价-自评报告-批次管理
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入pcglVO")
    @ApiLog("批次管理-新增")
    public R save(@Valid @RequestBody PcglVO pcglVO) {
        int count = pcglService.count(new LambdaQueryWrapper<Pcgl>().eq(Pcgl::getPcmc, pcglVO.getPcmc()));
        if(count>0){
            return R.fail("批次名称已存在");
        }
        boolean b = pcglService.save(pcglVO);
        return R.status(b);
    }

    /**
     * 修改 教学评价-自评报告-批次管理
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入pcglVO")
    @ApiLog("批次管理-修改")
    public R update(@Valid @RequestBody PcglVO pcglVO) {
        int count = pcglService.count(new LambdaQueryWrapper<Pcgl>().eq(Pcgl::getPcmc, pcglVO.getPcmc()).ne(Pcgl::getId,pcglVO.getId()));
        if(count>0){
            return R.fail("批次名称已存在");
        }
        boolean b = pcglService.updateById(pcglVO);
        return R.status(b);
    }

    /**
     * 复制批次
     */
    @PostMapping("/fzpc")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "复制批次", notes = "传入bggl")
    @ApiLog("批次管理-复制批次")
    public R fzpc(@RequestBody Pcgl pcgl) {
        Long oldId = pcgl.getId();
        pcgl.setId(null);
        pcgl.setZhid(null);
        pcgl.setPcmc(pcgl.getPcmc()+"-复制");
        boolean b = pcglService.save(pcgl);
        if(b){
            Long newId = pcgl.getId();
            //复制批次下的报告
            bgfzService.bgfzByPcid(oldId,newId);
            //复制批次下的佐证材料
            zzclService.updateZzclByPcid(oldId,newId);
            //复制批次下的备查材料
            zcclService.updateZcclByPcid(oldId,newId);
        }
        return R.status(b);
    }


    /**
     * 删除 教学评价-自评报告-批次管理
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("批次管理-逻辑删除")
    public R remove(@RequestBody BaseDeleteVO deleteVO) {
        List<Long> longs = Func.toLongList(deleteVO.getIds());
        //删除之前需要判断这个批次下是否有报告和材料
        QueryWrapper<Bggl> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Bggl::getScbj,0).in(Bggl::getPcid,longs);
        List<Bggl> list = bgglService.list(queryWrapper);
        if(Func.isNotEmpty(list) && list.size()>0){//判断批次下是否有报告
            return R.fail("批次下有报告需要先删除批次下所有报告才能删除批次！");
        }
        Boolean flag = false;//判断批次下是否有材料
        for(Long id:longs){
            QueryWrapper<Zccl> queZccl = new QueryWrapper<>();
            queZccl.lambda().eq(Zccl::getScbj,0).apply(" FIND_IN_SET({0},PC) ",id);
            List<Zccl> listZccl = zcclService.list(queZccl);
            ArrayList<Long> zcclDelIds = new ArrayList<>();
            for(Zccl zccl:listZccl){
                if(id.toString().equals(zccl.getPc())){
                    zcclDelIds.add(id);
                }else{
                    String pc = zccl.getPc().replace(id.toString(), "").replace(",,", ",");
                    if(pc.startsWith(",")){
                        pc=pc.substring(1);
                    }
                    zccl.setPc(pc);
                    zcclService.updateById(zccl);
                }
            }
            if(Func.isNotEmpty(zcclDelIds)){
                zcclService.deleteLogic(zcclDelIds);
            }
            QueryWrapper<Zzcl> queZzcl = new QueryWrapper<>();
            queZzcl.lambda().eq(Zzcl::getScbj,0).apply(" FIND_IN_SET({0},PC) ",id);
            List<Zzcl> listZzcl = zzclService.list(queZzcl);
            ArrayList<Long> zzclDelIds = new ArrayList<>();
            for(Zzcl zzcl:listZzcl){
                if(id.toString().equals(zzcl.getPc())){
                    zzclDelIds.add(id);
                }else{
                    String pc = zzcl.getPc().replace(id.toString(), "").replace(",,", ",");
                    if(pc.startsWith(",")){
                        pc=pc.substring(1);
                    }
                    zzcl.setPc(pc);
                    zzclService.updateById(zzcl);
                }
            }
            if(Func.isNotEmpty(zzclDelIds)){
                zzclService.deleteLogic(zzclDelIds);
            }
        }
        boolean b = pcglService.deleteLogic(longs);
        return R.status(b);
    }

}
