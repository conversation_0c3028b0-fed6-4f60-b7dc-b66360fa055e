package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.ZjmkSp;
import com.xpaas.zpbg.vo.ZjmkSpVO;
import org.springframework.stereotype.Repository;

/**
 * 教学评价-专家慕课-视频 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Repository
public interface ZjmkSpMapper extends BaseMapper<ZjmkSp> {

	/**
	 * 获取主要关注点
	 *
	 * @return
	 */
	@SqlParser(filter = true)
	ZjmkSpVO getZjmkSp(ZjmkSpVO zjmkSp);

}
