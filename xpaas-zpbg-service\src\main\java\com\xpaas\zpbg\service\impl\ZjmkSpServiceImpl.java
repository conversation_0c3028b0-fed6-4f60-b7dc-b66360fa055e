package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.ZjmkSp;
import com.xpaas.zpbg.mapper.ZjmkSpMapper;
import com.xpaas.zpbg.service.IZjmkSpService;
import com.xpaas.zpbg.vo.ZjmkSpVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-专家慕课-视频 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Slf4j
@Service
public class ZjmkSpServiceImpl extends BaseServiceImpl<ZjmkSpMapper, ZjmkSp> implements IZjmkSpService {

	@Override
	public ZjmkSpVO getZjmkSp(ZjmkSpVO zjmkSp) {
		return baseMapper.getZjmkSp(zjmkSp);
	}
}
