package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-标准体系管理实体类
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Data
@TableName("T_DT_JXPJ_ZSKGL_BZTXGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bztxgl对象", description = "教学评价-自评报告-标准体系管理")
public class Bztxgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 主要关注点
	*/
	@ExcelProperty("主要关注点")
	@ApiModelProperty(value = "主要关注点")
	@TableField("ZYGZD")
	private String zygzd;
	/**
	* 主要关注点
	*/
	@ExcelProperty("主要关注点ID")
	@ApiModelProperty(value = "主要关注点ID")
	@TableField("ZYGZDID")
	private String zygzdid;

	/**
	* 一级指标
	*/
	@ExcelProperty("一级指标")
	@ApiModelProperty(value = "一级指标")
	@TableField("YJZB")
	private String yjzb;
	/**
	* 一级指标
	*/
	@ExcelProperty("一级指标ID")
	@ApiModelProperty(value = "一级指标ID")
	@TableField("YJZBID")
	private String yjzbid;

	/**
	* 二级指标
	*/
	@ExcelProperty("二级指标")
	@ApiModelProperty(value = "二级指标")
	@TableField("EJZB")
	private String ejzb;
	/**
	* 二级指标
	*/
	@ExcelProperty("二级指标ID")
	@ApiModelProperty(value = "二级指标ID")
	@TableField("EJZBID")
	private String ejzbid;

	/**
	* 关键词
	*/
	@ExcelProperty("关键词")
	@ApiModelProperty(value = "关键词")
	@TableField("GJC")
	private String gjc;
	/**
	* 关键词
	*/
	@ExcelProperty("关键词")
	@ApiModelProperty(value = "关键词名称")
	@TableField("GJCMC")
	private String gjcmc;

	/**
	* 指标来源
	*/
	@ExcelProperty("指标来源")
	@ApiModelProperty(value = "指标来源")
	@TableField("ZBLY")
	private String zbly;

	/**
	* 导向要求
	*/
	@ExcelProperty("导向要求")
	@ApiModelProperty(value = "导向要求")
	@TableField("DXYQ")
	private String dxyq;

	/**
	* 限定性要求
	*/
	@ExcelProperty("限定性要求")
	@ApiModelProperty(value = "限定性要求")
	@TableField("XDXYQ")
	private String xdxyq;

	/**
	* 政治理论教学评价考察要点
	*/
	@ExcelProperty("政治理论教学评价考察要点")
	@ApiModelProperty(value = "政治理论教学评价考察要点")
	@TableField("ZZLLJXPJKCYQ")
	private String zzlljxpjkcyq;

	/**
	 * 考察要目
	 */
	@ExcelProperty("考察要目")
	@ApiModelProperty(value = "考察要目")
	@TableField("BZ")
	private String bz;

	/**
	* 学院标准
	*/
	@ExcelProperty("学院标准")
	@ApiModelProperty(value = "学院标准")
	@TableField("YXBZ")
	private String yxbz;

	/**
	* 核心算法
	*/
	@ExcelProperty("核心算法")
	@ApiModelProperty(value = "核心算法")
	@TableField("HXSF")
	private String hxsf;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private String px;

}
