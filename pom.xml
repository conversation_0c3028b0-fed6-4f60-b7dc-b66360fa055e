<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xpaas</groupId>
    <artifactId>xpaas-zpbg</artifactId>
    <version>jxpj-1.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>xpaas-zpbg-api</module>
        <module>xpaas-zpbg-common</module>
        <module>xpaas-zpbg-entity</module>
        <module>xpaas-zpbg-service</module>
    </modules>
    <properties>
        <xpaas.project.version>jxpj-1.0.0</xpaas.project.version>
<!--        &lt;!&ndash;xpaas核心代码&ndash;&gt;-->
<!--        <xpaas.core.version>core-1.6.3</xpaas.core.version>-->
<!--        &lt;!&ndash;xpaas-admin的代码&ndash;&gt;-->
<!--        <xpaas.admin.version>admin-1.6.3</xpaas.admin.version>-->
        <!--xpaas核心代码-->
        <xpaas.core.version>core-1.6.8-SNAPSHOT</xpaas.core.version>
        <!--xpaas-admin的代码-->
        <xpaas.admin.version>admin-jxpj-SNAPSHOT</xpaas.admin.version>
        <java.version>1.8</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring.boot.version>2.1.12.RELEASE</spring.boot.version>
        <spring.cloud.version>Greenwich.SR5</spring.cloud.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xpaas.platform</groupId>
                <artifactId>xpaas-bom</artifactId>
                <version>${xpaas.core.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.xpaas</groupId>
                <artifactId>xpaas-zpbg-common</artifactId>
                <version>${xpaas.project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>com.xpaas</groupId>
                <artifactId>xpaas-starter-trace</artifactId>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://*************:8081/repository/maven-releases/</url>
            <snapshots>
                <!--禁用快照库-->
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!--快照库-->
        <repository>
            <id>xpaas-snapshots</id>
            <name>xpaas-snapshots</name>
            <url>http://*************:8081/repository/maven-snapshots/</url>
            <releases>
                <!--禁用发布库-->
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <!--实时更新-->
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-repos</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <!--禁用快照库-->
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://*************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>xpaas-snapshots</id>
            <name>xpaas-snapshots</name>
            <url>http://*************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
