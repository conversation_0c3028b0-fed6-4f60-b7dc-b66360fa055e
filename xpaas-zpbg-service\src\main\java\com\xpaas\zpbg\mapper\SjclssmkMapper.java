package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Sjclssmk;
import com.xpaas.zpbg.vo.SjclssmkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-数据材料所属模块 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Repository
public interface SjclssmkMapper extends BaseMapper<Sjclssmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjclssmk
	 * @return
	 */
	List<SjclssmkVO> selectSjclssmkPage(IPage page, SjclssmkVO sjclssmk);

	/**
	 * 所属模块查询
	 *
	 * @param sjclssmk
	 * @return
	 */
	List<SjclssmkVO> selectSjclssmkList(SjclssmkVO sjclssmk);

	/**
	 * 所属模块删除
	 *
	 * @param sjclssmk
	 * @return
	 */
	boolean sjclssmkDelete(SjclssmkVO sjclssmk);

}
