package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Sjclssmk;
import com.xpaas.zpbg.vo.SjclssmkVO;

import java.util.List;

/**
 * 教学评价-自评报告-数据材料所属模块 服务类
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
public interface ISjclssmkService extends BaseService<Sjclssmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjclssmk
	 * @return
	 */
	IPage<SjclssmkVO> selectSjclssmkPage(IPage<SjclssmkVO> page, SjclssmkVO sjclssmk);

	/**
	 * 所属模块查询
	 *
	 * @param sjclssmk
	 * @return
	 */
	List<SjclssmkVO> selectSjclssmkList(SjclssmkVO sjclssmk);

	/**
	 * 所属模块保存
	 *
	 * @param sjclssmk
	 * @return
	 */
	boolean sjclssmkSave(SjclssmkVO sjclssmk);

}
