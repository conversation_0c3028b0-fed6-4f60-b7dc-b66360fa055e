package com.xpaas.zlkgl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Repository
public interface WxzlWjjMapper extends BaseMapper<WxzlWjj> {

    /**
     * 自定义分页
     * 分页查询教学评价-资料库平台-外校资料文件夹树表表数据
     *
     * @param page
     * @param wxzlWjj
     * @return
     * <AUTHOR>
     * @since 2025-07-25
     */
    List<WxzlWjjVO> selectWxzlWjjPage(IPage page, WxzlWjjVO wxzlWjj);

    /**
     * 根据ID查询文件夹
     */
    @Select("SELECT * FROM t_td_jxpj_zlk_wxzl_wjj WHERE ID = #{id}")
    WxzlWjj selectByIdCustom(String id);

    /**
     * 查询指定父文件夹下的所有子文件夹
     */
    @Select("SELECT * FROM t_td_jxpj_zlk_wxzl_wjj WHERE fj_wjj_id = #{parentId}")
    List<WxzlWjj> selectByParentId(String parentId);

}
