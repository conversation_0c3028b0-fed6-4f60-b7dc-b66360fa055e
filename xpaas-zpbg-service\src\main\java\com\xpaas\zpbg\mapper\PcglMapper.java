package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Pcgl;
import com.xpaas.zpbg.vo.PcglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-批次管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public interface PcglMapper extends BaseMapper<Pcgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param pcgl
	 * @return
	 */
	List<PcglVO> selectPcglPage(IPage page, Pcgl pcgl);
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param pcgl
	 * @return
	 */
	List<PcglVO> selectPcglPageCjrqDesc(IPage page, Pcgl pcgl);



}
