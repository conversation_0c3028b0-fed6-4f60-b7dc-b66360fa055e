package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bgmk;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-报告模块视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BgmkVO对象", description = "教学评价-自评报告-报告模块")
public class BgmkVO extends Bgmk {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;



    @ApiModelProperty(value = "模块下的定稿版本信息")
    private List<Bbgl> bbgls;


}
