<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.HomeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="homeResultMap" type="com.xpaas.zpbg.entity.Home">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>

    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="homeResultMapVO" type="com.xpaas.zpbg.vo.HomeVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>

        <result column="bgmc" property="bgmc"/>
        <result column="mklx" property="mklx"/>
        <result column="PX" property="px"/>
        <result column="jdId" property="jdId"/>
        <result column="jdmc" property="jdmc"/>
        <result column="kssj" property="kssj"/>
        <result column="zxjssj" property="zxjssj"/>
        <result column="syjssj" property="syjssj"/>
        <result column="nowTime" property="nowTime"/>

        <result column="mkmc" property="mkmc"/>
        <result column="qtrStr" property="qtrStr"/>
        <result column="zxrStr" property="zxrStr"/>
        <result column="bgmkzt" property="bgmkzt"/>
        <result column="bgId" property="bgId"/>
        <result column="bbId" property="bbId"/>
        <result column="bjdzsbc" property="bjdzsbc"/>
        <result column="dzFlag" property="dzFlag"/>

        <result column="zb" property="zb"/>

        <result column="syzt" property="syzt"/>
        <result column="bbgllsbbId" property="bbgllsbbId"/>
        <result column="jdztFlag" property="jdztFlag"/>

    </resultMap>

    <select id="getBgJpList" resultType="com.xpaas.zpbg.vo.HomeVO">
        SELECT
            bggl.ID as id,
            CONCAT(bggl.nd, '年度' ,bggl.BGMC) as bgmc,
            bggl.nd,
            bggl.BGMC mc,
            bggl.RWLX as rwlx,
            bggl.BGLX as bglx,
            bggl.MKLX as mklx,
            MAX( jdgl.PX ),
            jdgl.id as jdId,
            jdgl.JDMC as jdmc,
            date_format(jdgl.KSSJ, '%Y年%m月%d日') as kssj,
            date_format(jdgl.ZXJSSJ, '%Y年%m月%d日') as zxjssj,
            date_format(jdgl.SYJSSJ, '%Y年%m月%d日') as syjssj,
            date_format(now(), '%Y-%m-%d %H:%i:%S') as nowTime,
            CASE WHEN DATEDIFF(date_format(jdgl.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                AND DATEDIFF(date_format(jdgl.SYJSSJ, '%Y%m%d'), curdate()) >= 0 THEN
                1
            ELSE
                2
            END jdztFlag,
            bbgl.ID as bbId
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                (SELECT
                    *
                FROM
                    T_DT_JXPJ_ZPBG_JDGL jdglInner
                WHERE
                jdglInner.SCBJ = 0
                AND DATEDIFF(date_format(jdglInner.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
               ) jdgl
            ON jdgl.BGID = bggl.id
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
           INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bggl.id
            AND bbgl.BGMKID = bgmk.ID
            AND bbgl.JDGLID = jdgl.ID
            AND bbgl.BBLX = 1
            AND bbgl.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.SFGD=0
            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
        GROUP BY bggl.id
        HAVING MAX( jdgl.PX ) is not null
        ORDER BY bggl.PX, bggl.ID desc
    </select>

    <select id="getBgDzAndZxrList" resultType="com.xpaas.zpbg.vo.HomeVO">
        SELECT
            bggl.ID as id,
            CONCAT(bggl.nd, '年度' ,bggl.BGMC) as bgmc,
            bggl.nd,
            bggl.BGMC mc,
            bggl.RWLX as rwlx,
            bggl.BGLX as bglx,
            bggl.MKLX as mklx,
            MAX( jdgl.PX ),
            jdgl.id as jdId,
            jdgl.JDMC as jdmc,
            date_format(jdgl.KSSJ, '%Y年%m月%d日') as kssj,
            date_format(jdgl.ZXJSSJ, '%Y年%m月%d日') as zxjssj,
            date_format(jdgl.SYJSSJ, '%Y年%m月%d日') as syjssj,
            date_format(now(), '%Y-%m-%d %H:%i:%S') as nowTime,
            CASE WHEN DATEDIFF(date_format(jdgl.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                AND DATEDIFF(date_format(jdgl.SYJSSJ, '%Y%m%d'), curdate()) >= 0 THEN
                1
            ELSE
                2
            END jdztFlag,
            bbgl.ID as bbId
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                (SELECT
                    *
                FROM
                    T_DT_JXPJ_ZPBG_JDGL jdglInner
                WHERE
                jdglInner.SCBJ = 0
                AND DATEDIFF(date_format(jdglInner.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
               ) jdgl
            ON jdgl.BGID = bggl.id
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_RWFG rwfg
            ON rwfg.BGID = bggl.id
            AND rwfg.BGMKID = bgmk.ID
            AND rwfg.SCBJ = 0
            INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bggl.id
            AND bbgl.BGMKID = bgmk.ID
            AND bbgl.JDGLID = jdgl.ID
            AND bbgl.BBLX = 1
            AND bbgl.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.SFGD=0
            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
            AND rwfg.FGLX in (1, 2)
            AND rwfg.RYID = #{home.userId}
        GROUP BY bggl.id
        HAVING MAX( jdgl.PX ) is not null
        ORDER BY bggl.PX, bggl.ID desc
    </select>

    <!-- 获取报告列表 工作台使用 专家、点长、撰写人 -->
    <select id="getBgZjAndDzAndZxrList" resultType="com.xpaas.zpbg.vo.HomeVO">
        SELECT
            bggl.ID as id,
            CONCAT(bggl.nd, '年度' ,bggl.BGMC) as bgmc,
            bggl.nd,
            bggl.BGMC mc,
            bggl.RWLX as rwlx,
            bggl.BGLX as bglx,
            bggl.MKLX as mklx,
            MAX( jdgl.PX ),
            jdgl.id as jdId,
            jdgl.JDMC as jdmc,
            date_format(jdgl.KSSJ, '%Y年%m月%d日') as kssj,
            date_format(jdgl.ZXJSSJ, '%Y年%m月%d日') as zxjssj,
            date_format(jdgl.SYJSSJ, '%Y年%m月%d日') as syjssj,
            date_format(now(), '%Y-%m-%d %H:%i:%S') as nowTime,
            CASE WHEN DATEDIFF(date_format(jdgl.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                AND DATEDIFF(date_format(jdgl.SYJSSJ, '%Y%m%d'), curdate()) >= 0 THEN
                     1
                 ELSE
                     2
                END jdztFlag,
            bbgl.ID as bbId
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
                INNER JOIN
            (SELECT
                 *
             FROM
                 T_DT_JXPJ_ZPBG_JDGL jdglInner
             WHERE
                 jdglInner.SCBJ = 0
               AND DATEDIFF(date_format(jdglInner.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
            ) jdgl
            ON jdgl.BGID = bggl.id
                INNER JOIN
            T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
                AND bgmk.SCBJ = 0
                INNER JOIN
            T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
                AND jdmkgl.BGMKID = bgmk.ID
                AND jdmkgl.SCBJ = 0
                LEFT JOIN
            T_DT_JXPJ_ZPBG_RWFG rwfg
            ON rwfg.BGID = bggl.id
                AND rwfg.BGMKID = bgmk.ID
                AND rwfg.SCBJ = 0
                INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bggl.id
                AND bbgl.BGMKID = bgmk.ID
                AND bbgl.JDGLID = jdgl.ID
                AND bbgl.BBLX = 1
                AND bbgl.SCBJ = 0
                LEFT JOIN
            T_DT_JXPJ_ZPBG_SYZJ syzj
            ON syzj.BGID = bggl.id
                AND syzj.BGMKID = bgmk.ID
                AND syzj.BBID = bbgl.ID
                AND syzj.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
          AND bggl.SFGD=0
          AND (bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
          AND rwfg.FGLX in (1, 2)
          AND rwfg.RYID = #{home.userId}) or (syzj.ZJID = #{home.userId}
          AND (((bgmk.BGMKZT = 10 or bgmk.BGMKZT = 60) and (syzj.syzt = 3 or syzj.syzt = 4))
           or bgmk.BGMKZT = 30 or bgmk.BGMKZT = 40 or bgmk.BGMKZT = 80 or bgmk.BGMKZT = 90))
        GROUP BY bggl.id
        HAVING MAX( jdgl.PX ) is not null
        ORDER BY bggl.PX, bggl.ID desc
    </select>

    <select id="getBgZjList" resultType="com.xpaas.zpbg.vo.HomeVO">
        SELECT
            bggl.ID as id,
            CONCAT(bggl.nd, '年度' ,bggl.BGMC) as bgmc,
            bggl.nd,
            bggl.BGMC mc,
            bggl.RWLX as rwlx,
            bggl.BGLX as bglx,
            bggl.MKLX as mklx,
            bbgl.BJDZSBC as bjdzsbc,
            MAX( jdgl.PX ),
            jdgl.id as jdId,
            jdgl.JDMC as jdmc,
            date_format(jdgl.KSSJ, '%Y年%m月%d日') as kssj,
            date_format(jdgl.ZXJSSJ, '%Y年%m月%d日') as zxjssj,
            date_format(jdgl.SYJSSJ, '%Y年%m月%d日') as syjssj,
            date_format(now(), '%Y-%m-%d %H:%i:%S') as nowTime,
            CASE WHEN DATEDIFF(date_format(jdgl.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                AND DATEDIFF(date_format(jdgl.SYJSSJ, '%Y%m%d'), curdate()) >= 0 THEN
                1
            ELSE
                2
            END jdztFlag
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                (SELECT
                    *
                FROM
                    T_DT_JXPJ_ZPBG_JDGL jdglInner
                WHERE
                    jdglInner.SCBJ = 0
                    AND DATEDIFF(date_format(jdglInner.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                ) jdgl
            ON jdgl.BGID = bggl.id
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_BBGL bbgl
            ON bbgl.BGID = bggl.id
            AND bbgl.BGMKID = bgmk.ID
            AND bbgl.JDGLID = jdgl.ID
            AND bbgl.BBLX = 1
            AND bbgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_SYZJ syzj
            ON syzj.BGID = bggl.id
            AND syzj.BGMKID = bgmk.ID
            AND syzj.BBID = bbgl.ID
            AND syzj.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.SFGD=0
            AND syzj.ZJID = #{home.userId}
            AND (((bgmk.BGMKZT = 10 or bgmk.BGMKZT = 60) and (syzj.syzt = 3 or syzj.syzt = 4))
             or bgmk.BGMKZT = 30 or bgmk.BGMKZT = 40 or bgmk.BGMKZT = 80 or bgmk.BGMKZT = 90)

        GROUP BY bggl.id
        HAVING MAX( jdgl.PX ) is not null
        ORDER BY bggl.PX, bggl.ID desc
    </select>

    <select id="getNowTime" resultMap="homeResultMapVO">
        SELECT
            date_format(now(), '%Y-%m-%d %H:%i:%S') as nowTime
        FROM
            dual
    </select>

    <select id="getJdList" resultMap="homeResultMapVO">
        SELECT
            jdgl.id as jdId,
            jdgl.JDMC as jdmc,
            date_format(jdgl.KSSJ, '%Y%-%m-%d') as kssj,
            date_format(jdgl.ZXJSSJ, '%Y-%m-%d') as zxjssj,
            date_format(jdgl.SYJSSJ, '%Y-%m-%d') as syjssj
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDGL jdgl
            ON jdgl.BGID = bggl.id
            AND jdgl.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
        <if test="home.bgId != null and home.bgId != ''">
            and bggl.id = #{home.bgId}
        </if>
        ORDER BY jdgl.PX, jdgl.KSSJ, jdgl.id
    </select>

    <select id="getDzAndZxrRole" resultType="map">

        SELECT
            IFNULL(SUM(CASE WHEN rwfg.FGLX = 1 THEN
                1
            ELSE
                0
            END), 0) AS dz
            ,IFNULL(SUM(CASE WHEN rwfg.FGLX = 2 THEN
                1
            ELSE
                0
            END), 0) AS zxr
        FROM T_DT_JXPJ_ZPBG_RWFG rwfg
        INNER join T_DT_JXPJ_ZPBG_BGGL bggl
   	    ON rwfg.BGID = bggl.id
        AND bggl.SCBJ = 0
        INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk
        ON bgmk.id = rwfg.BGMKID
        AND bgmk.scbj = 0
        WHERE
             rwfg.RYID = #{userId}
           AND rwfg.SCBJ = 0
        <if test="bgId!=null and bgId!=''">
            and bggl.ID=#{bgId}
        </if>
        <if test="jdId!=null and jdId!=''">
            and bggl.JDGLID=#{jdId}
        </if>
    </select>

    <select id="getZygzdRole" resultType="int">
        SELECT
            count(1)
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDGL jdgl
            ON jdgl.BGID = bggl.id
            AND jdgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_RWFG rwfg
            ON rwfg.BGID = bggl.id
            AND rwfg.BGMKID = bgmk.ID
            AND rwfg.FGLX = 1
            AND rwfg.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.ID = #{bgId}

            <if test="jdId != null and jdId != ''">
                and jdgl.id = #{jdId}
            </if>

            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
            AND rwfg.RYID = #{userId}
        LIMIT 1

    </select>

    <select id="getZygzdRoleByBgmkId" resultType="map">
        SELECT
            count(1)
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDGL jdgl
        ON jdgl.BGID = bggl.id
        AND jdgl.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_BGMK bgmk
        ON bgmk.BGID = bggl.id
        AND bgmk.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
        ON jdmkgl.JDGLID = jdgl.id
        AND jdmkgl.BGMKID = bgmk.ID
        AND jdmkgl.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_RWFG rwfg
        ON rwfg.BGID = bggl.id
        AND rwfg.BGMKID = bgmk.ID
        AND rwfg.FGLX = 1
        AND rwfg.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.ID = #{bgId}

            <if test="jdId != null and jdId != ''">
                and jdgl.id = #{jdId}
            </if>

            <if test="bgmkId != null and bgmkId != ''">
                and bgmk.id = #{bgmkId}
            </if>
            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
            AND rwfg.RYID = #{userId}

        LIMIT 1

    </select>

    <select id="getBgmkDzAndZxrList" resultMap="homeResultMapVO">
        SELECT
            bgmk.ID AS id
            ,CASE
                WHEN bgmk.CMM IS NOT NULL AND bgmk.CMM != '' THEN
                    bgmk.CMM
                ELSE
                    bgmk.MKMC
                END
             AS mkmc
            ,GROUP_CONCAT(distinct CASE WHEN rwfgAll.FGLX = 1 THEN CONCAT(rwfgAll.DWMC, '，',rwfgAll.RYXM) END ORDER BY rwfgAll.id asc SEPARATOR '；') AS qtrStr
            ,GROUP_CONCAT(distinct CASE WHEN rwfgAll.FGLX = 2 THEN CONCAT(rwfgAll.DWMC, '，',rwfgAll.RYXM) END ORDER BY rwfgAll.id asc SEPARATOR '；') AS zxrStr
            ,CASE
                WHEN bgmk.BGMKZT = 0
                AND (( bgmkztjl.id IS NOT NULL AND date_format( jdgl.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR COUNT(DISTINCT bgmkztjl.id ) = 0 ) THEN
                    bgmk.BGMKZT
                WHEN bgmk.BGMKZT = 10
                AND ( date_format( jdgl.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR bbgl.bjdzsbc > 1 ) THEN
                    bgmk.BGMKZT
                WHEN bgmk.BGMKZT = 20
                OR ( COUNT(DISTINCT bgmkztjl.id ) = 1 AND date_format( jdgl.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND bbgl.bjdzsbc &lt; 2 ) THEN
                    20
                ELSE bgmk.BGMKZT
                END AS bgmkzt
            ,bggl.ID as bgId
            ,bbgl.ID as bbId

            ,CASE
            WHEN GROUP_CONCAT( distinct
                CASE
                WHEN rwfgAll.FGLX = 1 THEN
                CONCAT(  rwfgAll.ryid )
                END
                ORDER BY
                rwfgAll.id ASC SEPARATOR '；'
                )

                LIKE CONCAT('%', #{home.userId}, '%') THEN
                    1 ELSE 0
            END dzFlag
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDGL jdgl
            ON jdgl.BGID = bggl.id
            AND jdgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_RWFG rwfg
            ON rwfg.BGID = bggl.id
            AND rwfg.BGMKID = bgmk.ID
            AND rwfg.SCBJ = 0
            AND rwfg.FGLX in (1, 2)
            AND rwfg.RYID = #{home.userId}

            INNER JOIN
                T_DT_JXPJ_ZPBG_RWFG rwfgAll
            ON rwfgAll.BGID = bggl.id
            AND rwfgAll.BGMKID = bgmk.ID
            AND rwfgAll.SCBJ = 0

            INNER JOIN
                T_DT_JXPJ_ZPBG_BBGL bbgl
            ON bbgl.BGID = bggl.id
            AND bbgl.BGMKID = bgmk.ID
            AND bbgl.JDGLID = jdgl.ID
            AND bbgl.BBLX = 1
            AND bbgl.SCBJ = 0
            LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl
            ON bgmkztjl.BGID = bggl.ID
            AND bgmkztjl.BGMKID = bgmk.ID
            AND bgmkztjl.SCBJ = 0

        WHERE
            bggl.SCBJ = 0
            AND bggl.ID = #{home.bgId}
            AND jdgl.ID = #{home.jdId}

            <if test="home.bgmkzt != null and home.bgmkzt != 0">
                AND bgmk.BGMKZT = #{bgmkzt}
            </if>

            <if test="home.bgmkztType != null and home.bgmkztType != 0">
                 <if test="home.bgmkztType == 1">
                    AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60)
                 </if>
                <if test="home.bgmkztType == 2">
                    AND bgmk.BGMKZT in (80, 90)
                </if>
            </if>

        GROUP BY bgmk.ID
        ORDER BY bgmk.PX, bgmk.BGMKZT, bgmk.ID

    </select>

    <select id="getBgmkZjList" resultMap="homeResultMapVO">
        SELECT
            bgmk.ID AS id
            ,CASE
                WHEN bgmk.CMM IS NOT NULL AND bgmk.CMM != '' THEN
                    bgmk.CMM
                ELSE
                    bgmk.MKMC
            END
            AS mkmc
            ,GROUP_CONCAT(distinct CASE WHEN rwfg.FGLX = 1 THEN CONCAT(rwfg.DWMC, '，',rwfg.RYXM) END ORDER BY rwfg.id asc SEPARATOR '；') AS qtrStr
            ,GROUP_CONCAT(distinct CASE WHEN rwfg.FGLX = 2 THEN CONCAT(rwfg.DWMC, '，',rwfg.RYXM) END ORDER BY rwfg.id asc SEPARATOR '；') AS zxrStr
            ,bgmk.BGMKZT AS bgmkzt
            ,bggl.ID as bgId
            ,bbgl.ID as bbId
            ,bbgl.BJDZSBC as bjdzsbc
            ,syzj.SYZT as syzt
            ,CASE WHEN bgmk.BGMKZT = 10 THEN
                (SELECT
                    bbgllsbb.id
                FROM
                    T_DT_JXPJ_ZPBG_BBGL bbgllsbb
                INNER JOIN
                    T_DT_JXPJ_ZPBG_SYZJ syzjlsbb
                ON syzjlsbb.BGID = bbgllsbb.BGID
                AND syzjlsbb.BGMKID = bbgllsbb.BGMKID
                AND syzjlsbb.BBID = bbgllsbb.ID
                AND syzjlsbb.ZJID = #{home.userId}
                AND syzjlsbb.SYZT in (3, 4)
                AND syzjlsbb.SCBJ = 0
                WHERE
                    bbgllsbb.BGID = bggl.id
                    AND bbgllsbb.BGMKID = bgmk.ID
                    AND bbgllsbb.JDGLID = jdgl.ID
                    AND bbgllsbb.BBLX = 2
                    AND bbgllsbb.SSJS = 2
                    AND bbgllsbb.SCBJ = 0
                ORDER BY
                    bbgllsbb.CJRQ DESC
                LIMIT 1
                )
                ELSE
                    null
                END AS bbgllsbbId

        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDGL jdgl
        ON jdgl.BGID = bggl.id
        AND jdgl.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_BGMK bgmk
        ON bgmk.BGID = bggl.id
        AND bgmk.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
        ON jdmkgl.JDGLID = jdgl.id
        AND jdmkgl.BGMKID = bgmk.ID
        AND jdmkgl.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_BBGL bbgl
        ON bbgl.BGID = bggl.id
        AND bbgl.BGMKID = bgmk.ID
        AND bbgl.JDGLID = jdgl.ID
        AND bbgl.BBLX = 1
        AND bbgl.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_RWFG rwfg
        ON rwfg.BGID = bggl.id
        AND rwfg.BGMKID = bgmk.ID
        AND rwfg.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_SYZJ syzj
        ON syzj.BGID = bggl.id
        AND syzj.BGMKID = bgmk.ID
        AND syzj.BBID = bbgl.ID
        AND syzj.SCBJ = 0
        WHERE
            bggl.SCBJ = 0

            AND bggl.ID = #{home.bgId}
            AND jdgl.ID = #{home.jdId}

            AND syzj.ZJID = #{home.userId}

            <if test="home.bgmkzt != null and home.bgmkzt != 0">
                AND bgmk.BGMKZT = #{bgmkzt}
            </if>

            <if test="home.bgmkztType != null and home.bgmkztType != 0">
                <if test="home.bgmkztType == 21">
                    AND (bgmk.BGMKZT = 30 or (bgmk.BGMKZT = 40 and (syzj.syzt = 1 or syzj.syzt = 2)))
                </if>
                <if test="home.bgmkztType == 22">
                    AND ((bgmk.BGMKZT = 10 or bgmk.BGMKZT = 40 or bgmk.BGMKZT = 60) and syzj.syzt = 3)
                </if>
                <if test="home.bgmkztType == 23">
                    AND (((bgmk.BGMKZT = 10 or bgmk.BGMKZT = 40 or bgmk.BGMKZT = 60) and syzj.syzt = 4) or bgmk.BGMKZT = 80 or bgmk.BGMKZT = 90)
                </if>

                <if test="home.bgmkztType == 24">
                    AND (((bgmk.BGMKZT = 10 or bgmk.BGMKZT = 40 or bgmk.BGMKZT = 60) and (syzj.syzt = 3 or syzj.syzt = 4)) or bgmk.BGMKZT = 80 or bgmk.BGMKZT = 90)
                </if>
            </if>

        GROUP BY bgmk.ID
        ORDER BY bgmk.PX, bgmk.BGMKZT, bgmk.ID

    </select>

    <select id="getGdwjdtjList" resultType="map">

        SELECT
            dwid,
            dwmc,
            bgmkztjlCnt,
            bjdzsbc,
            bgmkzt,
            bgmkztmc,
            IFNULL( COUNT( DISTINCT bgmkId ), 0 ) AS cnt,
            IFNULL( totalCnt, 0 ) AS totalCnt,
            IFNULL( ROUND( ROUND( COUNT( DISTINCT bgmkId ) / totalCnt, 2 ) * 100 ), 0 ) AS zb
        FROM
            (
            SELECT
                dwid,
                dwmc,
                bgmkztjlCnt,
                a.bjdzsbc,
                CASE

                WHEN bgmkzt = 0
                AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
                bgmkzt
                WHEN (bgmkzt = 10
                AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) OR a.BGMKZT = 60 THEN
                    10
                WHEN bgmkzt = 20
                OR (
                (bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND
                date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                    20
                WHEN bgmkzt = 30 OR bgmkzt = 40 THEN
                    30
                ELSE
                    bgmkzt
                END AS bgmkzt,
                CASE

                WHEN bgmkzt = 0
                AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
                '未开始'
                WHEN (bgmkzt = 10
                AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) OR a.BGMKZT = 60 THEN
                '撰写中'
                WHEN bgmkzt = 20
                OR (
                (bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND
                date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                '撰写延期'
                WHEN bgmkzt = 30 OR bgmkzt = 40 THEN
                    '专家审阅'
--                 WHEN bgmkzt = 30 THEN
--                 '已提交'
--                 WHEN bgmkzt = 40 THEN
--                 '审阅中'
--                 WHEN bgmkzt = 60 THEN
--                 '已审阅'
                WHEN bgmkzt = 80 THEN
                '已完成'
                WHEN bgmkzt = 90 THEN
                '已提交' ELSE ''
                END AS bgmkztmc,
                bgmkId,
                IFNULL( totalCnt, 0 ) AS totalCnt
            FROM
                (
                    SELECT
                        rwfg.DWID,
                        rwfg.DWMC AS dwmc,
                        bgmk.id AS bgmkId,
                        bgmk.BGMKZT,
                        b.totalCnt,
                        jdgl.zxjssj,
                        bbgl.bjdzsbc,
                        bbgl.id AS bbId,
                        bgmkztjl.id AS bgmkztjlId,
                        COUNT( DISTINCT bgmkztjl.id ) AS bgmkztjlCnt
                    FROM
                        T_DT_JXPJ_ZPBG_BGGL bggl
                    INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.id
                    AND jdgl.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id
                    AND bgmk.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id
                    AND jdmkgl.BGMKID = bgmk.ID
                    AND jdmkgl.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_RWFG rwfg ON rwfg.BGID = bggl.id
                    AND rwfg.BGMKID = bgmk.ID
                    AND rwfg.FGLX = 2
                    AND rwfg.SCBJ = 0
                    INNER JOIN (
                        SELECT
                            bgglInner.ID AS bgId,
                            jdglInner.ID AS jdId,
                            rwfgInner.DWID AS dwid,
                            COUNT( DISTINCT bgmkInner.ID ) AS totalCnt
                        FROM
                            T_DT_JXPJ_ZPBG_BGGL bgglInner
                        INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdglInner ON jdglInner.BGID = bgglInner.id
                        AND jdglInner.SCBJ = 0
                        INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmkInner ON bgmkInner.BGID = bgglInner.id
                        AND bgmkInner.SCBJ = 0
                        INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkglInner ON jdmkglInner.JDGLID = jdglInner.id
                        AND jdmkglInner.BGMKID = bgmkInner.ID
                        AND jdmkglInner.SCBJ = 0
                        INNER JOIN T_DT_JXPJ_ZPBG_RWFG rwfgInner ON rwfgInner.BGID = bgglInner.id
                        AND rwfgInner.BGMKID = bgmkInner.ID
                        AND rwfgInner.FGLX = 2
                        AND rwfgInner.SCBJ = 0
                        WHERE
                            bgglInner.SCBJ = 0
                            AND bgglInner.id = #{home.bgId}
                            AND jdglInner.id = #{home.jdId}
                            AND bgmkInner.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
                        GROUP BY
                            rwfgInner.DWID,
                            bgglInner.id,
                            jdglInner.id
                        ) b ON b.bgId = bggl.id
                    AND b.jdId = jdgl.id
                    AND b.dwid = rwfg.dwid
                    LEFT JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
                    AND bbgl.bblx = 1
                    AND bbgl.scbj = 0
                    LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl ON bgmkztjl.BGID = bggl.ID
                    AND bgmkztjl.BGMKID = bgmk.ID
                    AND bgmkztjl.SCBJ = 0
                    WHERE
                        bggl.SCBJ = 0
                        AND bggl.id = #{home.bgId}
                        AND jdgl.id = #{home.jdId}
                        AND bgmk.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
                    GROUP BY
                        rwfg.DWID,
                        bgmk.BGMKZT,
                        bgmk.id
                ) a
            ) b
        GROUP BY
            dwid,
            bgmkzt
        ORDER BY
            dwid,
            bgmkzt

    </select>

    <select id="getZjsyqkList" resultType="map">

        SELECT
            bgmk.bgmkzt
            ,CASE WHEN bgmk.bgmkzt = 30 THEN
                '待审阅'
                WHEN bgmk.bgmkzt = 40 THEN
                '审阅中'
                WHEN bgmk.bgmkzt = 60 THEN
                '已审阅'
                WHEN bgmk.bgmkzt = 80 THEN
                '已完成'
            ELSE
                ''
            END AS bgmkztmc

            ,COUNT(DISTINCT bgmk.ID) AS cnt

        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
             INNER JOIN
                T_DT_JXPJ_ZPBG_JDGL jdgl
            ON jdgl.BGID = bggl.id
            AND jdgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_BGMK bgmk
            ON bgmk.BGID = bggl.id
            AND bgmk.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl
            ON jdmkgl.JDGLID = jdgl.id
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            INNER JOIN
                T_DT_JXPJ_ZPBG_BBGL bbgl
            ON bbgl.BGID = bggl.id
            AND bbgl.BGMKID = bgmk.ID
            AND bbgl.JDGLID = jdgl.ID
            AND bbgl.BBLX = 1
            AND bbgl.SCBJ = 0

        WHERE
            bggl.SCBJ = 0
            AND bggl.id = #{home.bgId}
            AND jdgl.id = #{home.jdId}
            AND bgmk.BGMKZT in (30, 40, 60, 80)

        GROUP BY bgmk.bgmkzt
        ORDER BY bgmk.bgmkzt

    </select>

    <select id="getGmksypcList" resultType="map">

        SELECT
            bgmk.ID AS id,
            CASE
                WHEN bgmk.CMM IS NOT NULL
                AND bgmk.CMM != '' THEN
                    bgmk.CMM ELSE bgmk.MKMC
                    END AS mkmc,
                IFNULL( SUM( CASE WHEN bgmkztjl.CZLX = '30' THEN 1 ELSE 0 END ), 0 ) AS bjdzsbc
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.ID
            AND jdgl.SCBJ = 0
            INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.ID
            AND bgmk.SCBJ = 0
            INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.ID
            AND jdmkgl.BGMKID = bgmk.ID
            AND jdmkgl.SCBJ = 0
            LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl
            ON bgmkztjl.BGID = bggl.ID
            AND bgmkztjl.BGMKID = bgmk.ID
            AND bgmkztjl.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.id = #{home.bgId}
            AND jdgl.id = #{home.jdId}
            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
        GROUP BY
            bgmk.ID
        ORDER BY
            bgmk.PX,
            bgmk.ID

    </select>

    <select id="getGyjzbqktjList" resultType="java.util.LinkedHashMap">
        SELECT * FROM (
            SELECT
                yjzbid,
                yjzb,
                bgmkztjlCnt,
                bjdzsbc,
                bgmkzt,
                bgmkztmc,
                MAX(px) AS px,
                IFNULL( COUNT( DISTINCT bgmkId ), 0 ) AS cnt,
                IFNULL( totalCnt, 0 ) AS totalCnt,
                IFNULL( ROUND( ROUND( COUNT( DISTINCT bgmkId ) / totalCnt, 2 ) * 100 ), 0 ) AS zb
            FROM
                (
                SELECT
                    yjzbid,
                    yjzb,
                    bgmkztjlCnt,
                    a.bjdzsbc,
                    CASE

                    WHEN bgmkzt = 0
                    AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
                    bgmkzt
                    WHEN (bgmkzt = 10
                    AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) OR a.BGMKZT = 60 THEN
                        10
                    WHEN bgmkzt = 20
                    OR ((bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                        20
                    WHEN bgmkzt = 30 OR bgmkzt = 40 THEN
                        30
                    ELSE
                        bgmkzt
                    END AS bgmkzt,
                    CASE

                    WHEN bgmkzt = 0
                    AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
                    '未开始'
                    WHEN (bgmkzt = 10
                    AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) OR a.BGMKZT = 60 THEN
                    '撰写中'
                    WHEN bgmkzt = 20
                    OR ((bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                    '撰写延期'

                    WHEN bgmkzt = 30 OR bgmkzt = 40 THEN
                    '专家审阅'
--                     WHEN bgmkzt = 30 THEN
--                     '已提交'
--                     WHEN bgmkzt = 40 THEN
--                     '审阅中'
--                     WHEN bgmkzt = 60 THEN
--                     '已审阅'
                    WHEN bgmkzt = 80 THEN
                    '已完成'
                    WHEN bgmkzt = 90 THEN
                    '已提交' ELSE ''
                    END AS bgmkztmc,
                    bgmkId,
                    px,
                    IFNULL( totalCnt, 0 ) AS totalCnt
                FROM
                (
                    SELECT
                        bgmk.YJZBID AS yjzbid,
                        bgmk.YJZB AS yjzb,
                        bgmk.id AS bgmkId,
                        bgmk.BGMKZT,
                        bgmk.PX AS px,
                        COUNT( DISTINCT bgmk.ID ) AS cnt,
                        b.totalCnt,
                        jdgl.zxjssj,
                        bbgl.bjdzsbc,
                        bbgl.id AS bbId,
                        bgmkztjl.id AS bgmkztjlId,
                        COUNT( DISTINCT bgmkztjl.id ) AS bgmkztjlCnt
                    FROM
                        T_DT_JXPJ_ZPBG_BGGL bggl
                    INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.id
                    AND jdgl.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id
                    AND bgmk.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id
                    AND jdmkgl.BGMKID = bgmk.ID
                    AND jdmkgl.SCBJ = 0
                    INNER JOIN (
                    SELECT
                        bgglInner.ID AS bgId,
                        jdglInner.ID AS jdId,
                        bgmkInner.YJZBID AS yjzbid,
                        COUNT( DISTINCT bgmkInner.ID ) AS totalCnt
                    FROM
                        T_DT_JXPJ_ZPBG_BGGL bgglInner
                    INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdglInner ON jdglInner.BGID = bgglInner.id
                    AND jdglInner.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmkInner ON bgmkInner.BGID = bgglInner.id
                    AND bgmkInner.SCBJ = 0
                    INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkglInner ON jdmkglInner.JDGLID = jdglInner.id
                    AND jdmkglInner.BGMKID = bgmkInner.ID
                    AND jdmkglInner.SCBJ = 0
                    WHERE
                        bgglInner.SCBJ = 0
                        AND bgglInner.id = #{home.bgId}
                        AND jdglInner.id = #{home.jdId}
                        AND bgmkInner.YJZBID IS NOT NULL
                        AND bgmkInner.YJZBID != ''
                        AND bgmkInner.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
                    GROUP BY
                        bgmkInner.YJZBID,
                        bgglInner.id,
                        jdglInner.id
                    ) b ON b.bgId = bggl.id
                    AND b.jdId = jdgl.id
                    AND b.YJZBID = bgmk.YJZBID
                    LEFT JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
                    AND bbgl.bblx = 1
                    AND bbgl.scbj = 0
                    LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl ON bgmkztjl.BGID = bggl.ID
                    AND bgmkztjl.BGMKID = bgmk.ID
                    AND bgmkztjl.SCBJ = 0
                    WHERE
                        bggl.SCBJ = 0
                        AND bggl.id = #{home.bgId}
                        AND jdgl.id = #{home.jdId}
                        AND bgmk.YJZBID IS NOT NULL
                        AND bgmk.YJZBID != ''
                        AND bgmk.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
                    GROUP BY
                        bgmk.YJZBID,
                        bgmk.BGMKZT,
                        bgmk.id
                ) a
            ) b
            GROUP BY
                yjzbid,
                bgmkzt
            ) c
        ORDER BY
            px,
            yjzbid,
            bgmkzt

    </select>

    <select id="getZlxxtjInfo" resultType="map">
        SELECT
            COUNT( DISTINCT a.ID ) AS totalCnt,
            IFNULL(
            SUM(
            CASE
                WHEN a.BGMKZT = 0
                AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
                1 ELSE 0
                END
                ),
                0
                ) AS wksCnt,
            IFNULL(
            SUM( CASE WHEN a.BGMKZT = 30 OR a.BGMKZT = 40 THEN 1 ELSE 0 END ),
            0
            ) AS ytjCnt,
            IFNULL( SUM( CASE WHEN a.BGMKZT = 40 THEN 1 ELSE 0 END ), 0 ) AS syCnt,
            IFNULL(
            SUM(
            CASE
                WHEN (a.BGMKZT = 10
                AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 )) OR a.BGMKZT = 60 THEN
                1 ELSE 0
                END
                ),
                0
                ) AS zxzCnt,
            IFNULL(
            SUM(
            CASE
                WHEN a.BGMKZT = 20
                OR ( (a.BGMKZT = 0 OR a.BGMKZT = 10) AND bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                1 ELSE 0
                END
                ),
                0
                ) AS yyqCnt,
            IFNULL( SUM( CASE WHEN a.BGMKZT = 80 THEN 1 ELSE 0 END ), 0 ) AS ywcCnt,
            IFNULL( SUM( CASE WHEN a.BGMKZT = 90 THEN 1 ELSE 0 END ), 0 ) AS ydbCnt

        FROM
            (
                SELECT
                    bgmk.*,
                    jdgl.zxjssj,
                    bbgl.bjdzsbc,
                    bbgl.id AS bbId,
                    bgmkztjl.id AS bgmkztjlId,
                    COUNT(DISTINCT bgmkztjl.id) AS bgmkztjlCnt
                FROM
                    T_DT_JXPJ_ZPBG_BGGL bggl
                INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.id
                AND jdgl.SCBJ = 0
                INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id
                AND bgmk.SCBJ = 0
                INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id
                AND jdmkgl.BGMKID = bgmk.ID
                AND jdmkgl.SCBJ = 0
                LEFT JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
                AND bbgl.bblx = 1
                AND bbgl.scbj = 0
                LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl ON bgmkztjl.BGID = bggl.ID
                AND bgmkztjl.BGMKID = bgmk.ID
                AND bgmkztjl.SCBJ = 0
                WHERE
                    bggl.SCBJ = 0
                    AND bggl.id = #{home.bgId}
                    AND jdgl.id = #{home.jdId}
                    AND bgmk.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90)
                GROUP BY
                    bgmk.id
            ) a

    </select>

    <select id="getSymkList" resultType="map">

        SELECT

            CAST(bgmkId AS char) AS bgmkId,
            mkmc,
            bgmkPx,
            CASE

            WHEN bgmkzt = 0
            AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
            bgmkzt
            WHEN (bgmkzt = 10
            AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) THEN
                10
            WHEN bgmkzt = 20
            OR (
            (bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND
            date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
                20
            ELSE
                bgmkzt
            END AS bgmkzt,
            CASE

            WHEN bgmkzt = 0
            AND (( bgmkztjlId IS NOT NULL AND date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' )) OR bgmkztjlCnt = 0 ) THEN
            '未开始'
            WHEN (bgmkzt = 10
            AND ( date_format( a.zxjssj, '%Y%-%m-%d' ) >= date_format( curdate(), '%Y%-%m-%d' ) OR a.bjdzsbc > 1 ) ) THEN
            '撰写中'
            WHEN bgmkzt = 20
            OR (
            (bgmkzt = 0 OR bgmkzt = 10) AND bgmkztjlId IS NOT NULL AND
            date_format( a.zxjssj, '%Y%-%m-%d' ) &lt; date_format( curdate(), '%Y%-%m-%d' ) AND a.bjdzsbc &lt; 2 ) THEN
            '撰写延期'
            WHEN bgmkzt = 30 THEN
            '待审阅'
            WHEN bgmkzt = 40 THEN
            '审阅中'
            WHEN bgmkzt = 60 THEN
            '已审阅'
            WHEN bgmkzt = 80 THEN
            '已完成'
            WHEN bgmkzt = 90 THEN
            '已提交' ELSE ''
            END AS bgmkztmc,

            qtrStr,
            zxrStr,
            zjStr,

            CAST(yjzbid AS char) AS yjzbid,
            CASE WHEN yjzbid IS NULL OR yjzbid = '' THEN
                NULL
            ELSE
                yjzb
            END yjzb

        FROM
            (
                SELECT
                    bgmk.YJZBID AS yjzbid,
                    bgmk.YJZB AS yjzb,
                    bgmk.id AS bgmkId,
                    bgmk.PX AS bgmkPx,
                    CASE
                    WHEN bgmk.CMM IS NOT NULL
                    AND bgmk.CMM != '' THEN
                        bgmk.CMM ELSE bgmk.MKMC
                        END AS mkmc,
                    bgmk.BGMKZT,

                    GROUP_CONCAT(distinct CASE WHEN rwfgAll.FGLX = 1 THEN CONCAT(rwfgAll.DWMC, '，',rwfgAll.RYXM) END ORDER BY rwfgAll.id asc SEPARATOR '；') AS qtrStr,
                    GROUP_CONCAT(distinct CASE WHEN rwfgAll.FGLX = 2 THEN CONCAT(rwfgAll.DWMC, '，',rwfgAll.RYXM) END ORDER BY rwfgAll.id asc SEPARATOR '；') AS zxrStr,
                    GROUP_CONCAT(distinct CONCAT(syzj.ZJDWMC, '，',syzj.ZJXM) ORDER BY syzj.id asc SEPARATOR '；') AS zjStr,

                    jdgl.zxjssj,
                    bbgl.bjdzsbc,
                    bbgl.id AS bbId,
                    bgmkztjl.id AS bgmkztjlId,
                    COUNT( DISTINCT bgmkztjl.id ) AS bgmkztjlCnt
                FROM
                    T_DT_JXPJ_ZPBG_BGGL bggl
                INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.id
                AND jdgl.SCBJ = 0
                INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id
                AND bgmk.SCBJ = 0
                INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id
                AND jdmkgl.BGMKID = bgmk.ID
                AND jdmkgl.SCBJ = 0

                LEFT JOIN
                    T_DT_JXPJ_ZPBG_RWFG rwfgAll
                ON rwfgAll.BGID = bggl.id
                AND rwfgAll.BGMKID = bgmk.ID
                AND rwfgAll.SCBJ = 0

                LEFT JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
                AND bbgl.bblx = 1
                AND bbgl.scbj = 0

                LEFT JOIN
                    T_DT_JXPJ_ZPBG_SYZJ syzj
                ON syzj.BGID = bggl.id
                AND syzj.BGMKID = bgmk.ID
                AND syzj.BBID = bbgl.ID
                AND syzj.SCBJ = 0

                LEFT JOIN T_DT_JXPJ_ZPBG_BGMKZTJL bgmkztjl ON bgmkztjl.BGID = bggl.ID
                AND bgmkztjl.BGMKID = bgmk.ID
                AND bgmkztjl.SCBJ = 0
                WHERE
                    bggl.SCBJ = 0
                    AND bggl.id = #{home.bgId}
                    AND jdgl.id = #{home.jdId}
                    AND bgmk.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
                GROUP BY
                    bgmk.id
            ) a

        ORDER BY
            bgmkPx,
            bgmkId
    </select>

    <select id="getSymkRwfgList" resultType="map">

        SELECT
            CAST(rwfg.DWID AS char) AS dwid,
            rwfg.DWMC AS dwmc,
            CAST(bgmk.id AS char) AS bgmkId,
            CAST(jdgl.id AS char) jdId,
            CAST(bggl.id AS char) AS bgId
        FROM
            T_DT_JXPJ_ZPBG_BGGL bggl
        INNER JOIN T_DT_JXPJ_ZPBG_JDGL jdgl ON jdgl.BGID = bggl.id
        AND jdgl.SCBJ = 0
        INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id
        AND bgmk.SCBJ = 0
        INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id
        AND jdmkgl.BGMKID = bgmk.ID
        AND jdmkgl.SCBJ = 0
        INNER JOIN T_DT_JXPJ_ZPBG_RWFG rwfg ON rwfg.BGID = bggl.id
        AND rwfg.BGMKID = bgmk.ID
        AND rwfg.FGLX = 1
        AND rwfg.SCBJ = 0
        WHERE
            bggl.SCBJ = 0
            AND bggl.id = #{home.bgId}
            AND jdgl.id = #{home.jdId}
            AND bgmk.BGMKZT IN ( 0, 10, 20, 30, 40, 60, 80, 90 )
            AND rwfg.DWID IS NOT NULL
            AND rwfg.DWID != ''
            AND rwfg.DWMC IS NOT NULL
            AND rwfg.DWMC != ''
        GROUP BY
            rwfg.DWID,
            bgmk.id
        ORDER BY
            bgmk.PX,
            bgmk.ID

    </select>

    <update id="updatBgmkById">
        update T_DT_JXPJ_ZPBG_BGMK set BGMKZT = #{bgmkzt} where id = #{bgmkId}
    </update>

    <update id="updatBbglById">
        update T_DT_JXPJ_ZPBG_BBGL
        set BGMKZT = #{bgmkzt}
        where
            ID = #{bbId}
            AND BGMKID = #{bgmkId}
            AND scbj = 0
    </update>

    <update id="updatSyzjById">
        update T_DT_JXPJ_ZPBG_SYZJ
        set SYZT = #{syzt}
        where
            BGID = #{bgId}
            AND BGMKID = #{bgmkId}
            AND BBID = #{bbId}
            AND ZJID = #{userId}
            AND scbj = 0
    </update>

    <!-- 工作台报告数量 -->
    <select id="gztbgsl" resultType="Map">
        select ifnull(sum(t1.wzx),0) wzx,ifnull(sum(t1.dxg),0) dxg from (
            SELECT bggl.ID,ifnull(sum(if(bgmk.BGMKZT=0,1,0)),0) wzx,ifnull(sum(if(bgmk.BGMKZT=60,1,0)),0) dxg
            FROM T_DT_JXPJ_ZPBG_BGGL bggl
            INNER JOIN (SELECT *
                    FROM T_DT_JXPJ_ZPBG_JDGL jdglInner
                    WHERE jdglInner.SCBJ = 0 AND DATEDIFF(date_format(jdglInner.KSSJ, '%Y%m%d'), curdate()) &lt;= 0
                ) jdgl ON jdgl.BGID = bggl.id
            INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk ON bgmk.BGID = bggl.id AND bgmk.SCBJ = 0
            INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdmkgl ON jdmkgl.JDGLID = jdgl.id AND jdmkgl.BGMKID = bgmk.ID AND jdmkgl.SCBJ = 0
            <if test="home.userId!=null and home.userId!=''">
                INNER JOIN T_DT_JXPJ_ZPBG_RWFG rwfg ON rwfg.BGID = bggl.id AND rwfg.BGMKID = bgmk.ID AND rwfg.SCBJ = 0
            </if>
            INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bggl.id AND bbgl.BGMKID = bgmk.ID AND bbgl.JDGLID = jdgl.ID AND bbgl.BBLX = 1 AND bbgl.SCBJ = 0
            WHERE
            bggl.SCBJ = 0
            AND bggl.SFGD=0
            AND bgmk.BGMKZT in (0, 10, 20, 30, 40, 60, 80, 90)
            <if test="home.userId!=null and home.userId!=''">
                AND rwfg.FGLX in (1, 2)
                AND rwfg.RYID = #{home.userId}
            </if>
        <if test="home.bgId!=null and home.bgId!=''">
            AND bggl.ID = #{home.bgId}
        </if>
            GROUP BY bggl.id
            HAVING MAX( jdgl.PX ) is not null
            ORDER BY bggl.PX, bggl.ID desc) t1
    </select>

</mapper>
