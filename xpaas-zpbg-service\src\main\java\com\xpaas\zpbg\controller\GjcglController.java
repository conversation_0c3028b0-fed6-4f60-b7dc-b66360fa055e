package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Gjcgl;
import com.xpaas.zpbg.service.IGjcglService;
import com.xpaas.zpbg.service.IGjcglmkService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.GjcglVO;
import com.xpaas.zpbg.vo.GjcglmkVO;
import com.xpaas.zpbg.wrapper.GjcglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
/**
 * 教学评价-自评报告-关键词管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/gjcgl")
@Api(value = "教学评价-自评报告-关键词管理", tags = "教学评价-自评报告-关键词管理接口")
public class GjcglController extends BaseController {

	private GjcglWrapper gjcglWrapper;
	private IGjcglService gjcglService;
	private IGjcglmkService gjcglmkService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入gjcgl")
	@ApiLog("关键词管理-详情")
	public R<GjcglVO> detail(Gjcgl gjcgl) {
		Gjcgl detail = gjcglService.getOne(Condition.getQueryWrapper(gjcgl));
		return R.data(gjcglWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-关键词管理 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入gjcgl")
	@ApiLog("关键词管理-分页")
	public R<IPage<GjcglVO>> list(Gjcgl gjcgl, Query query) {
		IPage<Gjcgl> pages = gjcglService.page(Condition.getPage(query), Condition.getQueryWrapper(gjcgl));
		return R.data(gjcglWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-关键词管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入gjcgl")
	@ApiLog("关键词管理-自定义分页")
    public R<IPage<GjcglVO>> page(GjcglVO gjcgl, Query query) {
        IPage<GjcglVO> pages = gjcglService.selectGjcglPage(Condition.getPage(query), gjcgl);
        return R.data(gjcglWrapper.wrapperPageVO(pages));
    }



	/**
	 * 新增 教学评价-自评报告-关键词管理
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入gjcgl")
	@ApiLog("关键词管理-新增")
	public R save(@Valid @RequestBody GjcglVO gjcglVO) {
		boolean b = gjcglService.save(gjcglVO);

		// 关键词关联模块保存
		GjcglmkVO gjcglmkVO = new GjcglmkVO();
		gjcglmkVO.setMkidList(gjcglVO.getMkidList());
		gjcglmkVO.setGjcid(gjcglVO.getId());
		gjcglmkService.gjcglmkSave(gjcglmkVO, gjcglVO.getGlfw(), "add");

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-关键词管理
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入gjcgl")
	@ApiLog("关键词管理-修改")
	public R update(@Valid @RequestBody GjcglVO gjcglVO) {
		boolean b = gjcglService.updateById(gjcglVO);

		// 关键词关联模块保存
		GjcglmkVO gjcglmkVO = new GjcglmkVO();
		gjcglmkVO.setMkidList(gjcglVO.getMkidList());
		gjcglmkVO.setGjcid(gjcglVO.getId());
		gjcglmkService.gjcglmkSave(gjcglmkVO, gjcglVO.getGlfw(), "update");

		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-关键词管理 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入gjcgl")
	@ApiLog("关键词管理-新增或修改")
	public R submit(@Valid @RequestBody Gjcgl gjcgl) {
		return R.status(gjcglService.saveOrUpdate(gjcgl));
	}

	
	/**
	 * 删除 教学评价-自评报告-关键词管理
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("关键词管理-逻辑删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = gjcglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("关键词管理-高级查询")
	public R<IPage<GjcglVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Gjcgl> queryWrapper = Condition.getQueryWrapper(map, Gjcgl.class);
		IPage<Gjcgl> pages = gjcglService.page(Condition.getPage(query), queryWrapper);
		return R.data(gjcglWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("关键词管理-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Gjcgl> queryWrapper = Condition.getQueryWrapper(map, Gjcgl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Gjcgl> list = gjcglService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Gjcgl.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("关键词管理-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Gjcgl> list = ExcelUtil.read(file, Gjcgl.class);
		//TODO 此处需要根据具体业务添加代码
		gjcglService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("关键词管理-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Gjcgl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Gjcgl> list = gjcglService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Gjcgl导入模板", "Gjcgl导入模板",columnFiledNames, list, Gjcgl.class);
	}

	/**
	 * 模块列表查询 教学评价-自评报告-关键词管理
	 */
	@GetMapping("/selectMkglList")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "模块列表查询", notes = "传入gjcgl")
	@ApiLog("关键词管理-模块列表查询")
	public List<GjcglVO>selectMkglList(GjcglVO gjcgl) {
		return gjcglService.selectMkglList(gjcgl);
	}

	/**
	 * 关键词关联模块查询
	 */
	@GetMapping("/selectGjcglmkList")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "关键词关联模块查询", notes = "传入gjcglmk")
	@ApiLog("关键词管理-关键词关联模块查询")
	public List<GjcglmkVO>selectGjcglmkList(GjcglmkVO gjcglmk) {
		return gjcglmkService.selectGjcglmkList(gjcglmk);
	}

	/**
	 * 报告列表查询
	 */
	@GetMapping("/selectBgglList")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "报告列表查询", notes = "传入gjcgl")
	@ApiLog("关键词管理-报告列表查询")
	public List<GjcglVO>selectBgglList(GjcglVO gjcgl) {
		return gjcglService.selectBgglList(gjcgl);
	}

	/**
	 * 进度查询
	 */
	@GetMapping("/selectJdgl")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "进度查询", notes = "传入gjcgl")
	@ApiLog("关键词管理-进度查询")
	public Map<String, List<GjcglVO>>selectJdgl(GjcglVO gjcgl) {
		Map<String, List<GjcglVO>> dataMap = new HashMap<>();
		dataMap.put("jdglList",gjcglService.selectJdglList(gjcgl));
		dataMap.put("zxjd",gjcglService.selectZxjd(gjcgl));
		return dataMap;
	}

}
