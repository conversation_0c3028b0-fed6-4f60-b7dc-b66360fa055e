package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.ZjmkSp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-专家慕课-视频视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZjmkSpVO对象", description = "教学评价-专家慕课-视频")
public class ZjmkSpVO extends ZjmkSp {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "主要关注点")
    private String zygzdid;

    @ApiModelProperty(value = "主要关注点名称")
    private String zygzdmc;

    @ApiModelProperty(value = "全局搜索")
    private String qjss;
}
