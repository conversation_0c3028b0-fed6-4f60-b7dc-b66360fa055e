package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Gjcznjc;
import com.xpaas.zpbg.service.IGjcznjcService;
import com.xpaas.zpbg.vo.GjcznjcVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
/**
 * 教学评价-自评报告-关键词智能检测 控制器
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/znjc")
@Api(value = "教学评价-自评报告-关键词智能检测", tags = "教学评价-自评报告-关键词智能检测接口")
public class GjcznjcController extends BaseController {

	private IGjcznjcService gjcznjcService;

    /**
     * 查询 教学评价-自评报告-关键词智能检测
     */
    @GetMapping("/getznjc")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查询", notes = "传入gjcznjc")
	@ApiLog("智能检测-查询")
    public R<List<GjcznjcVO>> getznjc(GjcznjcVO gjcznjc) {
        List<GjcznjcVO> gjcznjcList = gjcznjcService.selectGjcznjc(gjcznjc);
        return R.data(gjcznjcList);
    }

	/**
	 * 修改 教学评价-自评报告-关键词智能检测
	 */
	@PostMapping("/updatelist")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改", notes = "传入gjcznjc")
	@ApiLog("智能检测-多条修改")
	public R updatelist(@Valid @RequestBody List<Gjcznjc> gjcznjcList) {
		Gjcznjc newGjcznjc = new Gjcznjc();
		newGjcznjc.setBgid(gjcznjcList.get(0).getBgid());
		newGjcznjc.setBgmkid(gjcznjcList.get(0).getBgmkid());
		newGjcznjc.setBbid(gjcznjcList.get(0).getBbid());
		gjcznjcService.removeGjcznjc(newGjcznjc);
		boolean add = gjcznjcService.saveBatch(gjcznjcList);
		return R.status(add);
	}
}
