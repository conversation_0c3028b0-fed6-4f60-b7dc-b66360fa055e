package com.xpaas.zpbg.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Xxfb;
import com.xpaas.zpbg.vo.XxfbUserVO;

import java.util.List;

/**
 * 自评报告-消息 服务类
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IXxfbService extends BaseService<Xxfb> {

//	/**
//	 * 专家审阅 点击完成
//	 *
//	 * @return
//	 */
//	void sywcMsg(Long bgId, Long bgmkId, Long jdmkglId, String mkmc);
//
//
//	/**
//	 * 点长 点击定稿
//	 *
//	 * @return
//	 */
//	void dgMsg(Long bgId, Long bgmkId, Long jdmkglId, String mkmc);
//
//	/**
//	 * 报告期限预警
//	 *
//	 * @return
//	 */
//	void bgWarn();
//
//	/**
//	 * 报告开始预警
//	 *
//	 * @return
//	 */
//	void bgStartWarn();
//
//	/**
//	 * 报告延期预警
//	 *
//	 * @return
//	 */
//	void bgDelayWarn();

    /**
     * 业务上用来发消息
     *
     * @return
     */
    void sendMessageYw(String title, String content, List<XxfbUserVO> userList, String typeStr, Long ywid);

    /**
     * 业务上用来给教评管理员发消息
     *
     * @return
     */
    void sendMessageJp(String titleJp);
}
