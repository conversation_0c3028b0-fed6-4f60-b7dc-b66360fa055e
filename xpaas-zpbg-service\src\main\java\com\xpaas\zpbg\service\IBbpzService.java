package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bbpz;
import com.xpaas.zpbg.vo.BbpzGlCopyVO;
import com.xpaas.zpbg.vo.BbpzVO;

import java.util.List;

/**
 * 教学评价-自评报告-版本批注 服务类
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface IBbpzService extends BaseService<Bbpz> {

    /**
     * 自定义分页
     *
     * @param page
     * @param bbpz 教学评价-自评报告-版本批注 实体
     * @return
     * <AUTHOR>
     * @since 2024-09-02
     */
    IPage<BbpzVO> selectBbpzPage(IPage<BbpzVO> page, BbpzVO bbpz);

    /**
     * 复制批注关联引用
     *
     * @param params
     * @return
     */
    boolean bbpzGlCopy(List<BbpzGlCopyVO> params);
}
