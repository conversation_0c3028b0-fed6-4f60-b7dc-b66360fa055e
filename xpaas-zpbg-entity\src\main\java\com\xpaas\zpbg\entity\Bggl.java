package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-报告管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BGGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bggl对象", description = "教学评价-自评报告-报告管理")
public class Bggl extends TenantEntity {

		private static final long serialVersionUID = 1L;

	/**
	* 报告名称
	*/
	@ExcelProperty("报告名称")
	@ApiModelProperty(value = "报告名称")
	@TableField("BGMC")
	private String bgmc;

	/**
	* 年度
	*/
	@ExcelProperty("年度")
	@ApiModelProperty(value = "年度")
	@TableField("ND")
	private Integer nd;

	/**
	* 模块类型
	*/
	@ExcelProperty("模块类型")
	@ApiModelProperty(value = "模块类型")
	@TableField("MKLX")
	private Integer mklx;
	/**
	* 报告类型
	*/
	@ExcelProperty("报告类型")
	@ApiModelProperty(value = "报告类型")
	@TableField("BGLX")
	private Integer bglx;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 报告状态
	*/
	@ExcelProperty("报告状态")
	@ApiModelProperty(value = "报告状态")
	@TableField("BGZT")
	private Integer bgzt;

	/**
	 * 进度管理ID
	 */
	@ExcelProperty("进度管理ID")
	@ApiModelProperty(value = "进度管理ID")
	@TableField("JDGLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long jdglid;

	/**
	* 备注
	*/
	@ExcelProperty("备注")
	@ApiModelProperty(value = "备注")
	@TableField("BZ")
	private String bz;

	/**
	* 报告功能
	*/
	@ExcelProperty("报告功能")
	@ApiModelProperty(value = "报告功能")
	@TableField("BGGN")
	private String bggn;

	/**
	 * 创建日期
	 */
	@ExcelProperty("创建日期")
	@ApiModelProperty(value = "创建日期")
	@TableField("CJRQ")
	@JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
	private Date cjrq;

	/**
	 * 演示报告
	 */
	@ExcelProperty("演示报告")
	@ApiModelProperty(value = "演示报告")
	@TableField("YSBG")
	private Integer ysbg;

	/**
	 * 任务类型:字典编码rwlx
	 */
	@ExcelProperty("任务类型:字典编码rwlx")
	@ApiModelProperty(value = "任务类型:字典编码rwlx")
	@TableField("RWLX")
	private Integer rwlx;

	/**
	 * 是否归档:1-是;0-否;
	 */
	@ExcelProperty("是否归档:1-是;0-否;")
	@ApiModelProperty(value = "是否归档:1-是;0-否;")
	@TableField("SFGD")
	private Integer sfgd;

	/**
	 * 批次管理ID(批次功能废弃)
	 */
	@ExcelProperty("批次管理ID")
	@ApiModelProperty(value = "批次管理ID")
	@TableField("PCID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long pcid;


	/**
	 * 批次管理名称(批次功能废弃)
	 */
	@ApiModelProperty(value = "批次管理名称")
	@TableField(exist = false)
	private String pcmc;


}
