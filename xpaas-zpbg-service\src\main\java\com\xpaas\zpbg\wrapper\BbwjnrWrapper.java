package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bbwjnr;
import com.xpaas.zpbg.vo.BbwjnrVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-版本文件内容包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class BbwjnrWrapper extends BaseEntityWrapper<Bbwjnr, BbwjnrVO>  {


	@Override
	public BbwjnrVO entityVO(Bbwjnr bbwjnr) {
		BbwjnrVO bbwjnrVO = Objects.requireNonNull(BeanUtil.copy(bbwjnr, BbwjnrVO.class));
		//User cjr = UserCache.getUser(bbwjnr.getCjr());
		//if (cjr != null){
		//	bbwjnrVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbwjnr.getGxr());
		//if (gxr != null){
		//	bbwjnrVO.setGxrName(gxr.getName());
		//}
		return bbwjnrVO;
	}

    @Override
    public BbwjnrVO wrapperVO(BbwjnrVO bbwjnrVO) {
		//User cjr = UserCache.getUser(bbwjnrVO.getCjr());
		//if (cjr != null){
		//	bbwjnrVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbwjnrVO.getGxr());
		//if (gxr != null){
		//	bbwjnrVO.setGxrName(gxr.getName());
		//}
        return bbwjnrVO;
    }

}
