<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.DocumentMapper">

	<!--取得模块文件列表-->
	<select id="getMkList" resultType="com.xpaas.zpbg.entity.Demt">
		SELECT
			MBWJLJ AS wjlj,
			SFWFM
		FROM
			T_DT_JXPJ_ZPBG_MKGL
		WHERE
			SCBJ = 0
			AND MKLX = #{mklx}
			AND MBWJLJ is not null
			AND MBWJLJ != ''

			<if test="mbidList != null and mbidList.size() > 0">
				<foreach collection="mbidList" item="item" open=" AND ID IN (" separator="," close=")">
					#{item}
				</foreach>
			</if>
		ORDER BY
			SFWFM desc, PX, ID desc
	</select>

	<!--取得报告模块-->
    <select id="getBgmkList" resultType="com.xpaas.zpbg.entity.Demt">
		SELECT
			bbgl.id,
			bbgl.wjlj,
			bgmk.yjzb,
			bgmk.ejzb,
			(case when bgmk.cmm is null or bgmk.cmm = '' then bgmk.mkmc else bgmk.cmm end) AS mkmc,
			bgmk.sfwfm,
			GROUP_CONCAT(distinct rwfg.DWMC) AS qtdw
		FROM
			T_DT_JXPJ_ZPBG_BBGL bbgl
		INNER JOIN
			T_DT_JXPJ_ZPBG_BGMK bgmk
				ON bgmk.ID = bbgl.BGMKID
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDGL jd
                on jd.BGID = bbgl.BGID
                and jd.SCBJ = 0
        INNER JOIN
            T_DT_JXPJ_ZPBG_JDMKGL jdgl
                on jdgl.JDGLID = jd.ID
                and jdgl.BGMKID = bbgl.BGMKID
                and jdgl.SCBJ = 0
		LEFT JOIN
			T_DT_JXPJ_ZPBG_RWFG rwfg
				ON rwfg.BGID = bgmk.BGID
				AND rwfg.BGMKID = bgmk.ID
				AND rwfg.FGLX = 2
				AND rwfg.SCBJ = 0
		WHERE
			bbgl.SCBJ = 0
			AND bbgl.BGID = #{bgid}
			AND bbgl.BBLX = 1
		GROUP BY
			bbgl.id
		ORDER BY
			bgmk.sfwfm desc,
			bgmk.PX,
			bgmk.MKID desc
    </select>

	<select id="getZzclList" resultType="com.xpaas.zpbg.entity.Zzclgl">
		SELECT
			bbgl.id AS bbid,
			( CASE WHEN gl.CMM IS NOT NULL AND gl.CMM != '' THEN gl.CMM ELSE gl.CLMC END ) AS clmc,
			gl.GLKEY,
			gl.CFWZ,
			gl.WJH
		FROM
			T_DT_JXPJ_ZPBG_BBGL bbgl
		INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk
			ON bgmk.ID = bbgl.BGMKID
		INNER JOIN T_DT_JXPJ_ZPBG_JDGL jd
			ON jd.BGID = bbgl.BGID
			AND jd.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdgl
			ON jdgl.JDGLID = jd.ID
			AND jdgl.BGMKID = bbgl.BGMKID
			AND jdgl.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz
			ON bbpz.BBID = bbgl.ID
			AND bbpz.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_ZZCLGL gl
			ON gl.BBID = bbpz.BBID
			AND gl.GLKEY = bbpz.COMMENT_ID
			AND gl.SCBJ = 0
		WHERE
			bbgl.SCBJ = 0
			AND bbgl.BGID = #{bgid}
			AND bbgl.BBLX = 1
			<if test="bbid != null">
				AND bbgl.ID = #{bbid}
			</if>
		ORDER BY
			bbgl.id,
			bbpz.px,
			gl.px
	</select>

	<select id="getZcclList" resultType="com.xpaas.zpbg.entity.Zcclgl">
		SELECT
			bbgl.id AS bbid,
			gl.CLMC AS clmc,
			gl.GLKEY,
			gl.CFWZ,
			gl.BCSM
		FROM
			T_DT_JXPJ_ZPBG_BBGL bbgl
		INNER JOIN T_DT_JXPJ_ZPBG_BGMK bgmk
			ON bgmk.ID = bbgl.BGMKID
		INNER JOIN T_DT_JXPJ_ZPBG_JDGL jd
			ON jd.BGID = bbgl.BGID
			AND jd.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL jdgl
			ON jdgl.JDGLID = jd.ID
			AND jdgl.BGMKID = bbgl.BGMKID
			AND jdgl.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz
			ON bbpz.BBID = bbgl.ID
			AND bbpz.SCBJ = 0
		INNER JOIN T_DT_JXPJ_ZPBG_ZCCLGL gl
			ON gl.BBID = bbpz.BBID
			AND gl.GLKEY = bbpz.COMMENT_ID
			AND gl.SCBJ = 0
		WHERE
			bbgl.SCBJ = 0
			AND bbgl.BGID = #{bgid}
			AND bbgl.BBLX = 1
			<if test="bbid != null">
				AND bbgl.ID = #{bbid}
			</if>
		ORDER BY
			bbgl.id,
			bbpz.px,
			gl.px
	</select>

	<select id="getFmlj" resultType="String">
		SELECT
			mkgl.MBWJLJ
		FROM
			t_dt_jxpj_zpbg_mkgl mkgl
			INNER JOIN t_dt_jxpj_zpbg_bggl bggl ON bggl.ID = #{bgid}
			AND bggl.MKLX = mkgl.MKLX
		WHERE
			mkgl.SFWFM = 1
		ORDER BY
			mkgl.ID DESC
		LIMIT 1
	</select>
</mapper>
