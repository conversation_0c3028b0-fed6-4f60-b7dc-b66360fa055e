package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bbwjnr;
import com.xpaas.zpbg.vo.BbwjnrVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-版本文件内容 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface BbwjnrMapper extends BaseMapper<Bbwjnr> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bbwjnr
	 * @return
	 */
	List<BbwjnrVO> selectBbwjnrPage(IPage page, BbwjnrVO bbwjnr);

	/**
	 * 根据版本id取得记录
	 *
	 * @param bbid
	 * @return
	 */
	Bbwjnr getByBbid(Long bbid);
}
