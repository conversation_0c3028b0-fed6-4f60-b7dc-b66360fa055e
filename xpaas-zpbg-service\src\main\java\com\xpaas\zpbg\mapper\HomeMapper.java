package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Home;
import com.xpaas.zpbg.vo.HomeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-评建跟踪-任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Repository
public interface HomeMapper extends BaseMapper<Home> {

    /**
     * 获取教评管理员报告列表
     * @param home
     * @return
     */
    List<HomeVO> getBgJpList(@Param("home") HomeVO home);

    /**
     * 获取点长或教评管理员报告列表
     * @param home
     * @return
     */
    List<HomeVO> getBgDzAndZxrList(@Param("home") HomeVO home);

    /**
     * 获取报告列表 工作台使用 专家、点长、撰写人
     */
    List<HomeVO> getBgZjAndDzAndZxrList(@Param("home") HomeVO home);

    /**
     * 获取专家报告列表
     * @param home
     * @return
     */
    List<HomeVO> getBgZjList(@Param("home") HomeVO home);

    /**
     * 获取当前时间
     * @return
     */
    HomeVO getNowTime();

    /**
     * 获取进度下拉列表
     * @param home
     * @return
     */
    List<HomeVO> getJdList(@Param("home") HomeVO home);

    /**
     * 获取是否是点长角色
     * @param bgId
     * @param jdId
     * @param userId
     * @return
     */
    int getZygzdRole(Long bgId, Long jdId, Long userId);

    /**
     * 获取是否有点长或者撰写人角色
     * @return
     */
    Map<String, Object> getDzAndZxrRole(Long userId,String bgId,String jdId);

    /**
     * 根据模块id获取是否是点长角色
     * @param bgId
     * @param jdId
     * @param bgmkId
     * @param userId
     * @return
     */
    int getZygzdRoleByBgmkId(Long bgId, Long jdId, Long bgmkId, Long userId);

    /**
     * 获取点长或教评管理员模块列表
     * @param home
     * @return
     */
    List<HomeVO> getBgmkDzAndZxrList(@Param("home") HomeVO home);

    /**
     * 教评管理员统计用的所有的模块列表
     * @param home
     * @return
     */
    @SqlParser(filter = true)
    List<Map<String, Object>> getSymkList(@Param("home") HomeVO home);

    /**
     * 教评管理员统计用的所有的模块对应的任务分工列表
     * @param home
     * @return
     */
    List<Map<String, Object>> getSymkRwfgList(@Param("home") HomeVO home);

    /**
     * 获取专家模块列表
     * @param home
     * @return
     */
    List<HomeVO> getBgmkZjList(@Param("home") HomeVO home);

    /**
     * 各单位进度统计
     * @param home
     * @return
     */
    @SqlParser(filter = true)
    List<Map<String, Object>> getGdwjdtjList(@Param("home") HomeVO home);

    /**
     * 专家审阅情况统计
     * @param home
     * @return
     */
    List<Map<String, Object>> getZjsyqkList(@Param("home") HomeVO home);

    /**
     * 各模块审阅频次统计
     * @param home
     * @return
     */
    List<Map<String, Object>> getGmksypcList(@Param("home") HomeVO home);

    /**
     * 各一级指标情况统计
     * @param home
     * @return
     */
    @SqlParser(filter = true)
    List<java.util.LinkedHashMap<String, Object>> getGyjzbqktjList(@Param("home") HomeVO home);

    /**
     * 总览信息统计
     * @param home
     * @return
     */
    Map<String, Object> getZlxxtjInfo(@Param("home") HomeVO home);

    /**
     * 更新审阅状态
     * @param bgId
     * @param bgmkId
     * @param bbId
     * @param userId
     * @param syzt
     * @return
     */
    boolean updatSyzjById(Long bgId, Long bgmkId, Long bbId, Long userId, Integer syzt);

    /**
     * 更新版本状态
     * @param bgmkId
     * @param bbId
     * @param bgmkzt
     * @return
     */
    boolean updatBbglById(Long bgmkId, Long bbId, Integer bgmkzt);

    /**
     * 更新模块状态
     * @param bgmkId
     * @param bgmkzt
     * @return
     */
    boolean updatBgmkById(Long bgmkId, Integer bgmkzt);

    /**
     * 工作台报告数量
     */
    Map<String, Object> gztbgsl(@Param("home")HomeVO homeVO);
}
