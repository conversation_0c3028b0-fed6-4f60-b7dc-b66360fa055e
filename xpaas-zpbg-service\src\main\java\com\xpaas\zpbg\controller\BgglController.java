package com.xpaas.zpbg.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.system.entity.DictBiz;
import com.xpaas.system.feign.IDictBizClient;
import com.xpaas.zpbg.client.IZjjyClient;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.service.IBbglService;
import com.xpaas.zpbg.service.IBgglService;
import com.xpaas.zpbg.service.IBgmkService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.BgglVO;
import com.xpaas.zpbg.vo.BgmkVO;
import com.xpaas.zpbg.wrapper.BgglWrapper;
import com.xpaas.zpbg.wrapper.BgmkWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教学评价-自评报告-报告管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bggl")
@Api(value = "教学评价-自评报告-报告管理", tags = "教学评价-自评报告-报告管理接口")
public class BgglController extends BaseController {

    private BgglWrapper bgglWrapper;
    private IBgglService bgglService;


    private IDictBizClient dictClient;
    private IZjjyClient zjjyClient;
    private IBgmkService bgmkService;
    private BgmkWrapper bgmkWrapper;
    private IBbglService bbglService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入bggl")
    @ApiLog("报告管理-详情")
    public R<BgglVO> detail(Bggl bggl) {
        Bggl detail = bgglService.getOne(Condition.getQueryWrapper(bggl));
        return R.data(bgglWrapper.entityVO(detail));
    }

    /**
     * 根据报告id获取报告信息报告下的模块，模块下的版本
     */
    @GetMapping("/getBgglMkglBbglByBgid")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "根据报告id获取报告信息报告下的模块，模块下的版本", notes = "传入bggl")
    @ApiLog("根据报告id获取报告信息报告下的模块，模块下的版本")
    public R<BgglVO> getBgglMkglBbglByBgid(Bggl bggl) {
        Bggl detail = bgglService.getOne(Condition.getQueryWrapper(bggl));
        BgglVO bgglVO = bgglWrapper.entityVO(detail);
        List<Bgmk> listMk = bgmkService.list(new QueryWrapper<Bgmk>().lambda().eq(Bgmk::getScbj, 0).eq(Bgmk::getBgid, bgglVO.getId()).orderByAsc(Bgmk::getPx).orderByDesc(Bgmk::getMkid));
        if(Func.isNotEmpty(listMk) && listMk.size()>0){
            List<BgmkVO> listMkVOs = bgmkWrapper.listVO(listMk);
            List<Bbgl> listBbgls = bbglService.list(new QueryWrapper<Bbgl>().lambda().eq(Bbgl::getScbj, 0)
                    .eq(Bbgl::getBgid,bgglVO.getId())
                    .eq(Bbgl::getBblx,2)
                    .eq(Bbgl::getDgbb,1));//dgbb为1的是定稿后的版本  版本类型 1:主线版本, 2:正式版本, 3:暂存版本
            if(Func.isNotEmpty(listBbgls) && listBbgls.size()>0){
                Map<Long, List<Bbgl>> mapMkidBbgl = listBbgls.stream().collect(Collectors.groupingBy(Bbgl::getBgmkid));
                for(BgmkVO bgmkVO:listMkVOs){
                    List<Bbgl> bbgls = mapMkidBbgl.get(bgmkVO.getId());
                    if(Func.isNotEmpty(bbgls) && bbgls.size()>0){
                        bgmkVO.setBbgls(bbgls);
                    }else{
                        bgmkVO.setBbgls(new ArrayList<Bbgl>());
                    }

                }
            }
            bgglVO.setBgmkVOS(listMkVOs);
        }else{
            bgglVO.setBgmkVOS(new ArrayList<BgmkVO>());
        }
        return R.data(bgglVO);
    }

    /**
     * 分页 教学评价-自评报告-报告管理 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入bggl")
    @ApiLog("报告管理-分页")
    public R<IPage<BgglVO>> list(Bggl bggl, Query query) {
        IPage<Bggl> pages = bgglService.page(Condition.getPage(query), Condition.getQueryWrapper(bggl));
        return R.data(bgglWrapper.pageVO(pages));
    }

    /**
     * 自定义分页 教学评价-自评报告-报告管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bggl")
    @ApiLog("报告管理-自定义分页")
    public R<IPage<BgglVO>> page(BgglVO bggl, Query query) {
        IPage<BgglVO> pages = bgglService.selectBgglPage(Condition.getPage(query), bggl);
        return R.data(bgglWrapper.wrapperPageVO(pages));
    }


    /**
     * 新增 教学评价-自评报告-报告管理
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入bggl")
    @ApiLog("报告管理-新增")
    public R save(@Valid @RequestBody BgglVO bgglVO) {
        boolean b = bgglService.save(bgglVO);
        return R.status(b);
    }

    /**
     * 修改 教学评价-自评报告-报告管理
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入bggl")
    @ApiLog("报告管理-修改")
    public R update(@Valid @RequestBody BgglVO bgglVO) {
        Integer count = bgglService.count(new LambdaQueryWrapper<Bggl>().eq(Bggl::getBgmc, bgglVO.getBgmc()).eq(Bggl::getRwlx, bgglVO.getRwlx()).eq(Bggl::getNd, bgglVO.getNd()).ne(Bggl::getId,bgglVO.getId()));
        if(count>0){
            return R.fail("相同年度任务类型报告已存在");
        }
        boolean b = bgglService.updateById(bgglVO);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-报告管理 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入bggl")
    @ApiLog("报告管理-新增或修改")
    public R submit(@RequestBody Bggl bggl) {
        return R.status(bgglService.saveOrUpdate(bggl));
    }


    /**
     * 删除 教学评价-自评报告-报告管理
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("报告管理-逻辑删除")
    public R remove(@RequestBody BaseDeleteVO deleteVO) {
        boolean b = bgglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
        return R.status(b);
    }


    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("报告管理-高级查询")
    public R<IPage<BgglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Bggl> queryWrapper = Condition.getQueryWrapper(map, Bggl.class);
        IPage<Bggl> pages = bgglService.page(Condition.getPage(query), queryWrapper);
        return R.data(bgglWrapper.pageVO(pages));
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("报告管理-导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<Bggl> queryWrapper = Condition.getQueryWrapper(map, Bggl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<Bggl> list = bgglService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Bggl.class);
    }


    /**
     * 导入Excel
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    @ApiLog("报告管理-导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Bggl> list = ExcelUtil.read(file, Bggl.class);
        //TODO 此处需要根据具体业务添加代码
        bgglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("报告管理-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Bggl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Bggl> list = bgglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        //TODO 此处需要根据具体业务添加代码
        //columnFiledNames.add("id");
        //columnFiledNames.add("cjrq");
        ExcelUtil.export(response, "Bggl导入模板", "Bggl导入模板", columnFiledNames, list, Bggl.class);
    }

    /**
     * 模糊查询
     */
    @GetMapping("/listByBgmc")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "模糊查询", notes = "传入字段_条件")
    @ApiLog("报告管理-模糊查询")
    public R<IPage<BgglVO>> listByBgmc(@RequestParam Map<String, Object> map, Query query) {
        String s = JSON.toJSONString(map);
        BgglVO bgglVO = JSON.parseObject(s, BgglVO.class);
        QueryWrapper<Bggl> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("BGMC", bgglVO.getBgmc());
        IPage<Bggl> pages = bgglService.page(Condition.getPage(query), queryWrapper);
        return R.data(bgglWrapper.pageVO(pages));
    }

    /**
     * 根据id查询
     */
    @GetMapping("/listById")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "根据id查询", notes = "传入字段_条件")
    @ApiLog("报告管理-根据id查询")
    public R<IPage<BgglVO>> listById(@RequestParam Map<String, Object> map, Query query) {
        String s = JSON.toJSONString(map);
        BgglVO bgglVO = JSON.parseObject(s, BgglVO.class);
        QueryWrapper<Bggl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", bgglVO.getId());
        IPage<Bggl> pages = bgglService.page(Condition.getPage(query), queryWrapper);
        return R.data(bgglWrapper.pageVO(pages));
    }


    /**
     * 教学评价-自评报告-报告进度
     */
    @GetMapping("/bgjdList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "报告进度", notes = "传入bggl")
    @ApiLog("报告管理-报告进度")
    public R bgjdList(Bggl bggl) {
        Map<String, Object> map = bgglService.selectBgjd(bggl);
        return R.data(map);
    }

    /**
     * 根据code查询字典
     */
    @GetMapping("/dictList")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "查询字典", notes = "查询字典")
    @ApiLog("报告管理-查询字典")
    public R<List<DictBiz>> dictList(@RequestParam String code) {
        List<DictBiz> dictList = dictClient.getList(code).getData();
        return R.data(dictList);
    }

    /**
     * 新增报告和报告模块 教学评价-自评报告-报告管理
     */
    @PostMapping("/saveBgAndMk")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增报告和报告模块", notes = "传入bgglVO")
    @ApiLog("报告管理-新增报告和报告模块")
    public R saveBgAndMk(@Valid @RequestBody BgglVO bgglVO) {
        boolean b = bgglService.saveBgAndMk(bgglVO);
        return R.status(b);
    }

    /**
     * 教学评价-自评报告-报告查询
     */
    @GetMapping("/searchBggl")
    @ApiOperationSupport(order = 16)
    @ApiOperation(value = "报告查询", notes = "传入bgglVO")
    @ApiLog("报告管理-报告查询")
    public R<List<BgglVO>> searchBggl(BgglVO bgglVO) {
        List<BgglVO> bgglVOS = bgglService.searchBggl(bgglVO);
        return R.data(bgglVOS);
    }

    /**
     * 报告展示 教学评价-自评报告-报告管理
     */
    @PostMapping("/sfzsChange")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入bggl")
    @ApiLog("报告管理-报告展示")
    public R sfzsChange(@Valid @RequestBody BgglVO bgglVO) {
        R returnData = R.status(true);
        boolean b = false;
        // 从未展示变成展示状态时
        if (bgglVO.getBgzt() == 2) {
            // 确认不同类型（综合评价/政治理论）的报告是否已经有展示的报告
            LambdaQueryWrapper<Bggl> wrapper = new LambdaQueryWrapper<Bggl>();
            wrapper.eq(Bggl::getMklx, bgglVO.getMklx());
            wrapper.eq(Bggl::getBgzt, 2);
            List<Bggl> res = bgglService.list(wrapper);
            if (res == null || res.size() == 0) {
                bgglService.updateById(bgglVO);
                returnData.setMsg("展示成功!");
            } else {
                returnData.setMsg("专家进院平台已展示报告!");
            }
        // 从展示变成未展示状态时，直接更新
        } else {
            returnData.setMsg("取消展示成功!");
            bgglService.updateById(bgglVO);
            //zjjyClient.deleteByBgid(bgglVO.getId());
        }
        return returnData;
    }

    /**
     * 同步报告和报告模块 教学评价-自评报告-报告管理
     */
    @GetMapping("/syncBgAndMk")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "同步报告和报告模块", notes = "传入bgglVO")
    @ApiLog("报告管理-新增报告和报告模块")
    public R syncBgAndMk(@RequestParam(required = true) Long id) {
        bgglService.syncBgAndMk(id);
        return R.success("成功");
    }
}
