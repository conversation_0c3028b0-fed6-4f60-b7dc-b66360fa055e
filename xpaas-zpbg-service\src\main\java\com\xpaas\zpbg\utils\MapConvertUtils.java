package com.xpaas.zpbg.utils;

import java.util.*;

public class MapConvertUtils {


    /**
     * 将 Map 中的 Long 类型的值转换为 String 类型
     *
     * @param map           要转换的 Map
     * @param keysToConvert 可变参数，指定要转换的 key 列表，如果为空则转换所有 Long 类型的值
     */
    public static void convertLongToString(Map<String, Object> map, String... keysToConvert) {
        if (map == null) {
            return;
        }
        // 如果 keysToConvert 为 null，则使用一个空数组
        if (keysToConvert == null) {
            keysToConvert = new String[0];
        }

        // 将可变参数转换为 Set
        Set<String> keysSet = new HashSet<>(Arrays.asList(keysToConvert));

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 检查是否需要转换
            if (value != null && value instanceof Long && (keysSet.isEmpty() || keysSet.contains(key))) {
                map.put(key, value.toString());
            }
        }
    }

    /**
     * 将 List<Map<String, Object>> 中的 Long 类型的值转换为 String 类型
     *
     * @param list          要转换的 List
     * @param keysToConvert 可变参数，指定要转换的 key 列表，如果为空则转换所有 Long 类型的值
     */
    public static void convertLongToString(List<Map<String, Object>> list, String... keysToConvert) {
        if (list == null) {
            return;
        }
        for (Map<String, Object> map : list) {
            convertLongToString(map, keysToConvert);
        }
    }

}
