package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;


/**
 * 自评报告-版本批注拷贝参数VO
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class BbpzGlCopyVO {
    private static final long serialVersionUID = 1L;

    // 版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbId;

    // 批注KEY
    private String commentId;

    // 新版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbIdNew;

    // 新批注KEY
    private String commentIdNew;

}
