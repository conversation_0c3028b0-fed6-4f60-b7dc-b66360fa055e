package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bqgl;
import com.xpaas.zpbg.vo.BqglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-标签管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class BqglWrapper extends BaseEntityWrapper<Bqgl, BqglVO>  {


	@Override
	public BqglVO entityVO(Bqgl bqgl) {
		BqglVO bqglVO = Objects.requireNonNull(BeanUtil.copy(bqgl, BqglVO.class));
		//User cjr = UserCache.getUser(bqgl.getCjr());
		//if (cjr != null){
		//	bqglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bqgl.getGxr());
		//if (gxr != null){
		//	bqglVO.setGxrName(gxr.getName());
		//}
		return bqglVO;
	}

    @Override
    public BqglVO wrapperVO(BqglVO bqglVO) {
		//User cjr = UserCache.getUser(bqglVO.getCjr());
		//if (cjr != null){
		//	bqglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bqglVO.getGxr());
		//if (gxr != null){
		//	bqglVO.setGxrName(gxr.getName());
		//}
        return bqglVO;
    }

}
