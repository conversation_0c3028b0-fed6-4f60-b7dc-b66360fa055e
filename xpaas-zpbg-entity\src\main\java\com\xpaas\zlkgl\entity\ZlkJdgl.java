package com.xpaas.zlkgl.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 教学评价-资料库平台-节点管理表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("T_TD_JXPJ_ZLK_JDGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Jdgl对象", description = "教学评价-资料库平台-节点管理表")
public class ZlkJdgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 节点名称
	*/
	@ExcelProperty("节点名称")
	@ApiModelProperty(value = "节点名称")
	private String jdMc;

	/**
	* 节点时间
	*/
	@ExcelProperty("节点时间")
	@ApiModelProperty(value = "节点时间")
	private String jdSj;

	/**
	* 是否在时间轴显示（1：是 0：否）
	*/
	@ExcelProperty("是否在时间轴显示（1：是 0：否）")
	@ApiModelProperty(value = "是否在时间轴显示（1：是 0：否）")
	private Integer sjzXs;

	/**
	* 评价类型
	*/
	@ExcelProperty("评价类型")
	@ApiModelProperty(value = "评价类型")
	private String pjlx;



}
