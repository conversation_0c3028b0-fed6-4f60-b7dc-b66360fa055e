package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Timescale;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

;

/**
 * 自评报告-首页甘特图视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TimescaleVO对象", description = "自评报告-首页-甘特图")
public class TimescaleVO extends Timescale {
	private static final long serialVersionUID = 1L;

	private Integer unitWidth;

	private List<SummarySectionVO> summarySections;


}
