package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zjjdcl;
import com.xpaas.zpbg.vo.ZjjdclVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-知识库管理-标准体系管理-专家解读材料包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Component
public class ZjjdclWrapper extends BaseEntityWrapper<Zjjdcl, ZjjdclVO>  {


	@Override
	public ZjjdclVO entityVO(Zjjdcl zjjdcl) {
		ZjjdclVO zjjdclVO = Objects.requireNonNull(BeanUtil.copy(zjjdcl, ZjjdclVO.class));
		//User cjr = UserCache.getUser(zjjdcl.getCjr());
		//if (cjr != null){
		//	zjjdclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zjjdcl.getGxr());
		//if (gxr != null){
		//	zjjdclVO.setGxrName(gxr.getName());
		//}
		return zjjdclVO;
	}

    @Override
    public ZjjdclVO wrapperVO(ZjjdclVO zjjdclVO) {
		//User cjr = UserCache.getUser(zjjdclVO.getCjr());
		//if (cjr != null){
		//	zjjdclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zjjdclVO.getGxr());
		//if (gxr != null){
		//	zjjdclVO.setGxrName(gxr.getName());
		//}
        return zjjdclVO;
    }

}
