<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.SjbglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sjbglResultMap" type="com.xpaas.zpbg.entity.Sjbgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="GLWZ" property="glwz"/>
        <result column="SJLY" property="sjly"/>
        <result column="SJB" property="sjb"/>
        <result column="SJBMC" property="sjbmc"/>
        <result column="SJBMCORG" property="sjbmcorg"/>
        <result column="SJCLID" property="sjclid"/>
        <result column="PX" property="px"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="sjbglResultMapVO" type="com.xpaas.zpbg.vo.SjbglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="GLWZ" property="glwz"/>
        <result column="SJLY" property="sjly"/>
        <result column="SJB" property="sjb"/>
        <result column="SJBMC" property="sjbmc"/>
        <result column="SJBMCORG" property="sjbmcorg"/>
        <result column="SJCLID" property="sjclid"/>
        <result column="PX" property="px"/>
        <result column="CLDZ" property="cldz"/>
        <result column="XH" property="xh"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectSjbglPage" resultMap="sjbglResultMapVO">
        select t2.* from (
        select t1.*,(select @rownum:=@rownum+1) xh from(
        select
            sjbgl.*,
            sjcl.CLDZ
            ,(select @rownum:=0) r
             ,bbpz.px as bbpzpx
        from
            T_DT_JXPJ_ZPBG_SJBGL sjbgl
        left join
            T_DT_JXPJ_ZPBG_SJCL sjcl
                on sjcl.ID = sjbgl.SJCLID
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = sjbgl.BBID
                and bbpz.COMMENT_ID = sjbgl.GLKEY
                and bbpz.SCBJ = 0
        where
            sjbgl.scbj = 0

            <if test="sjbgl.bgid != null and sjbgl.bgid != ''">
                and sjbgl.BGID = #{sjbgl.bgid}
            </if>

            <if test="sjbgl.bgmkid != null and sjbgl.bgmkid != ''">
                and sjbgl.BGMKID = #{sjbgl.bgmkid}
            </if>

            <if test="sjbgl.bbid != null and sjbgl.bbid != ''">
                and sjbgl.BBID = #{sjbgl.bbid}
            </if>

        ) t1 ) t2
        <where>
            <if test="sjbgl.glkeyList != null and sjbgl.glkeyList.size() > 0">
                <foreach collection="sjbgl.glkeyList" item="item" open=" AND t2.GLKEY IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by
        t2.bbpzpx, t2.PX, t2.ID
    </select>

    <!--判断是否已关联-->
    <select id="getGlList" resultType="com.xpaas.zpbg.vo.SjbglVO">
        SELECT
            gl.*
        FROM
            T_DT_JXPJ_ZPBG_SJBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
        WHERE
            gl.BGID = #{bgid}
            AND gl.BGMKID = #{bgmkid}
            AND gl.BBID = #{bbid}
            AND gl.GLKEY = #{glkey}
            AND gl.SCBJ = 0
        ORDER BY
            gl.PX
    </select>

    <!--不同的内容引用了相同的数据表-->
    <select id="sameGl" resultType="com.xpaas.zpbg.vo.SjbglVO">
        SELECT
            gl.*
        FROM
            T_DT_JXPJ_ZPBG_SJBGL gl
        inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = gl.BBID
                and bbpz.COMMENT_ID = gl.GLKEY
                and bbpz.SCBJ = 0
        WHERE
            gl.BGID = #{bgid}
            AND gl.BGMKID = #{bgmkid}
            AND gl.BBID = #{bbid}
            AND gl.SJB = #{sjb}
            AND gl.GLKEY != #{glkey}
            AND gl.SCBJ = 0
    </select>
</mapper>
