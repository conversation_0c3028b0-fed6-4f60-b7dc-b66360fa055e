package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bgmkczjl;
import com.xpaas.zpbg.vo.BgmkczjlVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-报告模块操作记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Component
public class BgmkczjlWrapper extends BaseEntityWrapper<Bgmkczjl, BgmkczjlVO>  {


	@Override
	public BgmkczjlVO entityVO(Bgmkczjl bgmkczjl) {
		BgmkczjlVO bgmkczjlVO = Objects.requireNonNull(BeanUtil.copy(bgmkczjl, BgmkczjlVO.class));
		//User cjr = UserCache.getUser(bgmkczjl.getCjr());
		//if (cjr != null){
		//	bgmkczjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmkczjl.getGxr());
		//if (gxr != null){
		//	bgmkczjlVO.setGxrName(gxr.getName());
		//}
		return bgmkczjlVO;
	}

    @Override
    public BgmkczjlVO wrapperVO(BgmkczjlVO bgmkczjlVO) {
		//User cjr = UserCache.getUser(bgmkczjlVO.getCjr());
		//if (cjr != null){
		//	bgmkczjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmkczjlVO.getGxr());
		//if (gxr != null){
		//	bgmkczjlVO.setGxrName(gxr.getName());
		//}
        return bgmkczjlVO;
    }

}
