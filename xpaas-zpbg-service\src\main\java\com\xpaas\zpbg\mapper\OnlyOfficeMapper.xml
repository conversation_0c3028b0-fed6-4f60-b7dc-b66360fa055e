<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.OnlyOfficeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="headerValueDTO" type="com.xpaas.zpbg.dto.HeaderValueDTO">
        <result column="FGLX" property="fglx"/>
        <result column="DWID" property="dwid"/>
        <result column="DWMC" property="dwmc"/>
        <result column="ZJXM" property="zjxm"/>
        <result column="RYXM" property="ryxm"/>
        <result column="KSSJ" property="kssj"/>
        <result column="ZXJSSJ" property="zxjssj"/>
        <result column="SYJSSJ" property="syjssj"/>
        <result column="BGMC" property="bgmc"/>
        <result column="MKMC" property="mkmc"/>
        <result column="JDMC" property="jdmc"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BBLX" property="bblx" />
        <result column="CLZT" property="clzt" />

    </resultMap>

    <!-- 更新新版本文件路径 文件key -->
    <update id="updateNewBb">
        UPDATE t_dt_jxpj_zpbg_bbgl bbgl1,
            t_dt_jxpj_zpbg_bbgl bbgl2
        SET bbgl1.wjlj = bbgl2.WJLJ
        WHERE
            bbgl1.id = #{newBbglid}
          AND bbgl2.id = #{bbglid}
    </update>

    <!-- 获取进度管理模块ID -->
    <select id="getJdglMkIdValue" parameterType="String" resultMap="headerValueDTO">
        SELECT
            JDGLID,
            BGMKID,
            BBLX
        FROM
            T_DT_JXPJ_ZPBG_BBGL bbgl
        WHERE
            bbgl.ID = #{bbglId}
          AND bbgl.SCBJ=0
    </select>

    <!-- 获取撰写信息 -->
    <select id="getHeaderValueOther" parameterType="String" resultMap="headerValueDTO">
        SELECT
           CONCAT( A.ND,'年度',  A.BGMC)  AS BGMC,
            CASE
                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END as MKMC,
            C.KSSJ,
            C.ZXJSSJ,
            C.SYJSSJ,
            U.BGID,
            U.BGMKID,
            U.BBMC AS JDMC
        FROM
            T_DT_JXPJ_ZPBG_BGGL AS A
                INNER JOIN ( SELECT BGID, BGMKID, JDGLID,BBMC FROM T_DT_JXPJ_ZPBG_BBGL WHERE ID = #{bbglId} AND SCBJ = 0 ) AS U ON U.BGID = A.ID
                INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk on bgmk.id = u.BGMKID
                INNER JOIN T_DT_JXPJ_ZPBG_JDGL AS C ON U.JDGLID = C.ID
        WHERE
            A.SCBJ = 0
          AND bgmk.SCBJ = 0
          AND C.SCBJ =0
    </select>

    <!-- 取报告名称、模块名称 -->
    <select id="getGlHeader" parameterType="String" resultMap="headerValueDTO">
        SELECT
           CONCAT( A.ND,'年度',  A.BGMC)  AS BGMC,
            CASE
                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END as MKMC,
            U.BGID,
            U.BGMKID
        FROM
            T_DT_JXPJ_ZPBG_BGGL AS A
                INNER JOIN ( SELECT BGID, BGMKID, JDGLID FROM T_DT_JXPJ_ZPBG_BBGL WHERE ID = #{bbglId} AND SCBJ = 0 ) AS U ON U.BGID = A.ID
                INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk on bgmk.id = u.BGMKID
        WHERE
            A.SCBJ = 0
          AND bgmk.SCBJ = 0

    </select>

    <!-- 获取任务分工 -->
    <select id="getUserClzt" parameterType="String" resultMap="headerValueDTO">
        SELECT * FROM T_DT_JXPJ_ZPBG_RWFG WHERE BGID=#{reportId} AND BGMKID=#{bgmkId} and RYID=#{userid} AND SCBJ=0  AND FGLX='2'
    </select>

    <!-- 去撰写人信息 -->
    <select id="getHeaderValue" parameterType="String" resultMap="headerValueDTO">
        SELECT FGLX,DWID,DWMC,RYXM,CLZT FROM T_DT_JXPJ_ZPBG_RWFG WHERE  BGID=#{reportId} AND BGMKID=#{bgmkId} AND FGLX='1' AND SCBJ=0
        UNION ALL SELECT FGLX,DWID,DWMC,RYXM,CLZT FROM T_DT_JXPJ_ZPBG_RWFG WHERE BGID=#{reportId} AND BGMKID=#{bgmkId} AND FGLX='2' AND SCBJ=0
    </select>

    <!-- 取专家姓名 -->
    <select id="getHeaderValuezj" parameterType="String" resultMap="headerValueDTO">
        SELECT ZJXM FROM T_DT_JXPJ_ZPBG_SYZJ WHERE  BGID=#{reportId} AND BGMKID=#{bgmkId} AND BBID=#{bbId}  AND SYZT in(2,3,4) AND SCBJ=0
    </select>

    <!-- 获取模块列表 -->
    <select id="getMkTree1" parameterType="String" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        (SELECT (bgmk.ID)   AS VALUE,
                CASE
                    WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                        bgmk.MKMC ELSE bgmk.cmm
                    END  AS label,
                bbgl.ID  as bbglId,
                bbgl.bblx,
                bgmk.YJZBID AS hasChildren
         FROM
              T_DT_JXPJ_ZPBG_BGMK bgmk
                  INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL D ON D.BGMKID = bgmk.ID
                  INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl on d.BGMKID = bbgl.BGMKID and bbgl.JDGLID = #{jdglId} and bbgl.bblx=1
         WHERE D.SCBJ = 0
           and bgmk.SCBJ = 0
           AND bbgl.SCBJ = 0
           AND D.JDGLID = #{jdglId}
           AND (bgmk.YJZBID IS NULL)
         ORDER BY bgmk.PX,bgmk.id)
        UNION ALL

        (SELECT DISTINCT (bgmk.YJZBID) AS VALUE,
                         bgmk.YJZB     AS label,
                         ''    as bbglId,
                         '' as bblx,
                         (bgmk.YJZBID) AS hasChildren
         FROM
                  T_DT_JXPJ_ZPBG_BGMK bgmk
                  INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL B ON B.BGMKID = bgmk.ID
         WHERE B.SCBJ = 0
           AND bgmk.SCBJ = 0
           AND B.JDGLID = #{jdglId}
           AND (bgmk.YJZBID IS NOT NULL)
         ORDER BY bgmk.PX,bgmk.id)
    </select>

    <!-- 获取二级模块列表 -->
    <select id="getMkTree2" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        SELECT DISTINCT
            ( bgmk.EJZBID ) AS VALUE,
            bgmk.EJZB AS label
        FROM
                 T_DT_JXPJ_ZPBG_BGMK bgmk
            INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL B ON B.BGMKID = bgmk.ID
        WHERE
            bgmk.SCBJ = 0
          and bgmk.scbj = 0
          AND B.SCBJ = 0
          AND B.JDGLID = #{jdglId} AND bgmk.YJZBID = #{yjzbId}
    </select>

    <!-- 获取三级模块列表 -->
    <select id="getMkTree3" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        SELECT
            bgmk.ID AS value,
            CASE
                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END  AS label,
            bbgl.bblx,
            bbgl.ID  as bbglId,
            bgmk.bgmkzt as bgmkzt
            ,bgmk.MKMC
        FROM
            T_DT_JXPJ_ZPBG_BGMK bgmk
        INNER JOIN T_DT_JXPJ_ZPBG_JDMKGL B ON B.BGMKID = bgmk.ID
        INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON b.BGMKID = bbgl.BGMKID and bbgl.bblx=1
        WHERE
            B.SCBJ = 0 AND bgmk.SCBJ = 0
          and bbgl.scbj = 0
          AND B.JDGLID =#{jdglId}
          AND bgmk.YJZBID = #{yjzbId}
          AND bgmk.EJZBID = #{ejzbId}
        order by bgmk.px,bgmk.id
    </select>

    <!-- 判断当前人员是否在任务分工中 -->
    <select id="selectCountZxr" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            T_DT_JXPJ_ZPBG_RWFG
        WHERE
                BGMKID = ( SELECT bgmkid FROM t_dt_jxpj_zpbg_bbgl WHERE id = #{bbglId} AND scbj = 0 )
          AND ryid = #{userId}
          AND scbj =0
    </select>

    <!--  判断当前专家是否在审阅列表中 -->
    <select id="selectCountZj" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            T_DT_JXPJ_ZPBG_SYZJ
        WHERE
            BBID = #{bbglId}
          AND ZJID = #{userId}
          AND scbj =0
    </select>

    <!-- 查询历史版本 -->
    <select id="selectHistoryVersion" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        SELECT bgmk.ID AS VALUE,
               CASE
                   WHEN IFNULL(bgmk.cmm, '') = '' THEN
                       bgmk.MKMC
                   ELSE bgmk.cmm
                   END     AS label,
               bbgl.ID     AS bbglId,
               bbgl.bblx,
               bgmk.YJZBID AS hasChildren
        FROM T_DT_JXPJ_ZPBG_BGMK bgmk
                 INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGMKID = bgmk.id
        WHERE bbgl.id = #{_parameter}
          AND bbgl.SCBJ = 0
          AND bgmk.SCBJ = 0
    </select>

    <!-- 获取模块 -->
    <select id="getMkTreeByBgid" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        ( SELECT
              ( bgmk.ID ) AS
                             VALUE
                  ,
              CASE

                  WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                      bgmk.MKMC ELSE bgmk.cmm
                  END AS label,
              bbgl.ID AS bbglId,
              bbgl.bblx,
              bgmk.YJZBID AS hasChildren,
              bgmk.PX,
              bgmk.id as pxid,
              bbgl.wjlj
          FROM
              T_DT_JXPJ_ZPBG_BGMK bgmk
                  INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.bgid = bgmk.bgid
                  AND bbgl.BGMKID = bgmk.id
                  AND bbgl.BBLX = 1
          WHERE
              bbgl.SCBJ = 0
            AND bgmk.SCBJ = 0
            AND bbgl.bgid = #{_parameter}
            AND ( bgmk.YJZBID IS NULL )
        ) UNION ALL
        (
            SELECT
                bgmk.YJZBID AS VALUE,
                bgmk.YJZB AS label,
                '' AS bbglId,
                '' AS bblx,
                ( bgmk.YJZBID ) AS hasChildren,
                min(bgmk.PX) as px,
                min(bgmk.id) as pxid,
                '' as wjlj
            FROM
                T_DT_JXPJ_ZPBG_BGMK bgmk
                    INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.bgid = bgmk.bgid
                    AND bbgl.BGMKID = bgmk.id
                    AND bbgl.BBLX = 1
            WHERE
                bbgl.SCBJ = 0
              AND bgmk.SCBJ = 0
              AND bbgl.bgid = #{_parameter}
              AND ( bgmk.YJZBID IS NOT NULL )
                group by bgmk.YJZBID
            )
        ORDER BY
            PX,
            pxid

    </select>

    <!-- 获取二级模块 -->
    <select id="getMkTree2ByBgid" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">
        SELECT DISTINCT
            ( bgmk.EJZBID ) AS VALUE,
            bgmk.EJZB AS label
        FROM
            T_DT_JXPJ_ZPBG_BGMK bgmk
        INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bgmk.BGID and bbgl.bblx=1 and bbgl.BGMKID = bgmk.ID
        WHERE
            bgmk.SCBJ = 0
        AND bbgl.SCBJ = 0
          AND bbgl.BGID = #{bgid} AND bgmk.YJZBID = #{yjzbId}
    </select>

    <!-- 获取三级模块 -->
    <select id="getMkTree3ByBgid" resultType="com.xpaas.zpbg.vo.OnlyOfflceTreeVO">

        SELECT
            bgmk.ID AS value,
            CASE
                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END  AS label,
            bbgl.bblx,
            bbgl.ID  as bbglId,
            bbgl.wjlj,
            bgmk.bgmkzt as bgmkzt
            ,bgmk.MKMC
        FROM
            T_DT_JXPJ_ZPBG_BGMK bgmk
            INNER JOIN T_DT_JXPJ_ZPBG_BBGL bbgl ON bbgl.BGID = bgmk.BGID and bbgl.bblx=1 and bbgl.BGMKID = bgmk.ID
        WHERE
             bgmk.SCBJ = 0
          and bbgl.scbj = 0
          AND bbgl.bgid =#{bgid}
          AND bgmk.YJZBID = #{yjzbId}
          AND bgmk.EJZBID = #{ejzbId}
        order by bgmk.px,bgmk.id

    </select>

    <!-- 获取文件key -->
    <select id="getWjkeyByBgmkid" resultType="java.lang.String">
        select wjkey from t_dt_jxpj_zpbg_bbgl where scbj = 0 and bblx = 1 and bgmkid = #{_parameter} limit 1
    </select>

    <!-- 获取撰写人负责模块 -->
    <select id="getZxrMkList" resultType="com.xpaas.zpbg.vo.OnlyOfficeTreeList">
        SELECT
            bgmk.id,
            bgmk.bgid,
            bgmk.mkid,
            CASE

                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END AS mkmc,
            bgmk.mklx,
            bgmk.cmm,
            bgmk.zbly,
            bgmk.yjzbid,
            bgmk.yjzb,
            bgmk.ejzbid,
            bgmk.ejzb,
            bgmk.px,
            bgmk.bgmkzt ,
            bbgl.id as bbglId,
            bbgl.bblx as bblx
        FROM
            t_dt_jxpj_zpbg_bgmk bgmk
                INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON bgmk.id = jdmkgl.bgmkid
                INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl on bgmk.bgid = bbgl.bgid and bbgl.jdglid = jdmkgl.jdglid and bbgl.bgmkid = bgmk.id and bblx = 1
        WHERE
            bgmk.SCBJ = 0
          AND jdmkgl.scbj = 0
          AND jdmkgl.JDGLID = #{jdglid}
          AND EXISTS
            (
                SELECT
                    1
                FROM
                    t_dt_jxpj_zpbg_rwfg rwfg
                WHERE
                    ( fglx = 1 OR FGLX = 2 )
                  AND rwfg.ryid = #{userId}
                  AND bgid = bgmk.bgid
                  AND bgmkid = jdmkgl.bgmkid)
    </select>

    <!-- 获取专家模块 -->
    <select id="getZjMkList" resultType="com.xpaas.zpbg.vo.OnlyOfficeTreeList">
        SELECT
            bgmk.id,
            bgmk.bgid,
            bgmk.mkid,
            CASE

                WHEN IFNULL( bgmk.cmm, '' ) = '' THEN
                    bgmk.MKMC ELSE bgmk.cmm
                END AS mkmc,
            bgmk.mklx,
            bgmk.cmm,
            bgmk.zbly,
            bgmk.yjzbid,
            bgmk.yjzb,
            bgmk.ejzbid,
            bgmk.ejzb,
            bgmk.px,
            bgmk.bgmkzt ,
            bbgl.id as bbglId,
            bbgl.bblx as bblx
        FROM
            t_dt_jxpj_zpbg_bgmk bgmk
                INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON bgmk.id = jdmkgl.bgmkid
                INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl on bgmk.bgid = bbgl.bgid and bbgl.jdglid = jdmkgl.jdglid and bbgl.bgmkid = bgmk.id and bblx = 1
        WHERE
            bgmk.SCBJ = 0
          AND bbgl.bgmkzt in (30,40,60,80,90)
          AND jdmkgl.scbj = 0
          AND jdmkgl.JDGLID = #{jdglid}
          AND EXISTS
            (
                SELECT
                    1
                FROM
                    T_DT_JXPJ_ZPBG_SYZJ syzj
                WHERE
                    syzj.bgid = bgmk.bgid
                  and syzj.bbid = bbgl.id
                  and syzj.BGMKID = bgmk.id
                  and zjid = #{userId}
            )
    </select>

    <!-- 查询主要关注点 -->
    <select id="selectZygzdByBgmkId" resultType="map">
        SELECT
            CONVERT(mkgl.BZTXID, char) as bztxId,
            CONVERT(mkgl.YJZBID, char) as yjzbId,
            mkgl.YJZB as yjzb,
            CONVERT(mkgl.EJZBID, char) as ejzbId,
            mkgl.EJZB ejzb
        FROM
            t_dt_jxpj_zpbg_bgmk bgmk
                INNER JOIN t_dt_jxpj_zpbg_mkgl mkgl ON bgmk.mkid = mkgl.id
        WHERE
            bgmk.id = #{_parameter}
    </select>

    <!-- 查询点长列表 -->
    <select id="getDzList" resultType="java.util.Map">
        SELECT
            CONVERT(rwfg.ryid,char) as userId,
            rwfg.ryxm as userName
        FROM
            t_dt_jxpj_zpbg_bgmk bgmk
                INNER JOIN T_DT_JXPJ_ZPBG_RWFG rwfg ON bgmk.bgid = rwfg.bgid and rwfg.BGMKID = bgmk.id
        WHERE
            bgmk.id = #{_parameter}
        and rwfg.scbj = 0
        and rwfg.FGLX = 1
    </select>

    <!-- 获取报告的报告模块ID -->
    <select id="getJdmkIds" resultType="java.lang.String">
        SELECT
            BGMKID
        FROM
            t_dt_jxpj_zpbg_jdmkgl
        WHERE
            JDGLID IN ( SELECT id FROM t_dt_jxpj_zpbg_jdgl WHERE BGID = #{_parameter} AND scbj = 0 )
          AND SCBJ = 0
        GROUP BY
            BGMKID
    </select>

    <!-- 取报告模块状态 -->
    <select id="getBgmkzt" resultType="java.lang.String">
        select bgmkzt from T_DT_JXPJ_ZPBG_BGMK where id = #{_parameter}
    </select>

    <select id="getMainIdByBgmkId" resultType="java.lang.String">
        SELECT
            bbgl.id
        FROM
            t_dt_jxpj_zpbg_bgmk bgmk
                INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bgmk.id = bbgl.bgmkid
                AND bgmk.bgid = bbgl.bgid
                AND bbgl.bblx = 1
        WHERE
            bgmk.id = #{_parameter}
          AND bgmk.scbj = 0
          AND bbgl.scbj = 0
    </select>

</mapper>
