package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bbpz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-版本批注视图实体类
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BbpzVO对象", description = "教学评价-自评报告-版本批注")
public class BbpzVO extends Bbpz {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
