package com.xpaas.zpbg.service.impl;

import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.vo.ZtProcStatus;

import java.util.Date;
import java.util.List;

/**
 * 教学评价-自评报告-状态管理 数据缓存
 *
 * <AUTHOR>
 * @since 2024-06-14
 */

public class ZtglStatusContext {

    // 入参信息
    public Long bgid = null;
    public Long bbid = null;

    // 操作者信息
    public Long userId = null;
    public String userName = null;
    public Long deptId = null;
    public String deptName = null;


    // 基本信息
    public Bggl bggl = null; // 报告
    public Bbgl bbgl = null; // 版本

    // 版本信息详情
    public ZtProcStatus procStatus = ZtProcStatus.WKS0;
    public Long bgmkid = null; // 报告模块ID
    public Long jdglid = null; //进度管理ID

    // 进度信息
    public Jdgl jdgl = null;
    // 进度详情
    public Date kssj = null;
    public Date zxjssj = null;
    public Date syjssj = null;
    public List<Jdmkgl> jdmkglList = null; // 报告模块进度关联列表

    // 分工、专家信息
    public List<Rwfg> rwfgList = null; // 人员分工列表
    public List<Syzj> syzjList = null; // 审阅专家列表
    public Syzj syzj = null; // 本身审阅信息 从syzjList筛取

    // 报告模块信息 - 用于版本名称生成、消息发送
    public Bgmk bgmk = null;

    // 操作后结果
    public int newBjdzsbc = 1;
    public ZtProcStatus newProcStatus = null;

}
