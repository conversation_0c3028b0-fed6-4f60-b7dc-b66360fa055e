package com.xpaas.zlkgl.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.vo.FlglWjjVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-资料库平台-分类管理文件夹树表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Component
public class FlglWjjWrapper extends BaseEntityWrapper<FlglWjj, FlglWjjVO> {


    /**
     * 将entity转换成 entityVO
     *
     * @return 转换后的entityVO对象
     * <AUTHOR>
     * @since 2025-07-24
     */
    @Override
    public FlglWjjVO entityVO(FlglWjj flglWjj) {
        FlglWjjVO flglWjjVO = Objects.requireNonNull(BeanUtil.copy(flglWjj, FlglWjjVO.class));
        //User cjr = UserCache.getUser(flglWjj.getCjr());
        //if (cjr != null){
        //	flglWjjVO.setCjrName(cjr.getName());
        //}
        //User gxr = UserCache.getUser(flglWjj.getGxr());
        //if (gxr != null){
        //	flglWjjVO.setGxrName(gxr.getName());
        //}
/**  **/
        return flglWjjVO;
    }


    @Override
    public FlglWjjVO wrapperVO(FlglWjjVO flglWjjVO) {
        //User cjr = UserCache.getUser(flglWjjVO.getCjr());
        //if (cjr != null){
        //	flglWjjVO.setCjrName(cjr.getName());
        //}
        //User gxr = UserCache.getUser(flglWjjVO.getGxr());
        //if (gxr != null){
        //	flglWjjVO.setGxrName(gxr.getName());
        //}
/**  */
        return flglWjjVO;
    }

}
