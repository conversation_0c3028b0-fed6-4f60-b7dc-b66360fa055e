<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZjyjMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zjyjResultMap" type="com.xpaas.zpbg.entity.Zjyj">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="GLWZ" property="glwz"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="YJKEY" property="yjkey"/>
        <result column="GLCLLX" property="glcllx"/>
        <result column="GLXXID" property="glxxid"/>
        <result column="GLXXMC" property="glxxmc"/>
        <result column="YJQF" property="yjqf"/>
        <result column="CLQF" property="clqf"/>
        <result column="YJLX" property="yjlx"/>
        <result column="KJBQ" property="kjbq"/>
        <result column="PJNR" property="pjnr"/>
        <result column="SYZJID" property="syzjid"/>
        <result column="SYZJXM" property="syzjxm"/>
        <result column="XGSM" property="xgsm"/>
        <result column="XGZT" property="xgzt"/>
        <result column="XGRID" property="xgrid"/>
        <result column="XGRXM" property="xgrxm"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zjyjResultMapVO" type="com.xpaas.zpbg.vo.ZjyjVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="GLWZ" property="glwz"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="YJKEY" property="yjkey"/>
        <result column="GLCLLX" property="glcllx"/>
        <result column="GLXXID" property="glxxid"/>
        <result column="GLXXMC" property="glxxmc"/>
        <result column="YJQF" property="yjqf"/>
        <result column="CLQF" property="clqf"/>
        <result column="YJLX" property="yjlx"/>
        <result column="KJBQ" property="kjbq"/>
        <result column="PJNR" property="pjnr"/>
        <result column="SYZJID" property="syzjid"/>
        <result column="SYZJXM" property="syzjxm"/>
        <result column="XGZT" property="xgzt"/>
        <result column="XGSM" property="xgsm"/>
        <result column="XGRID" property="xgrid"/>
        <result column="XGRXM" property="xgrxm"/>
    </resultMap>


    <!-- 通用查询映射结果 -->
    <resultMap id="ZjyjExcelResultMap" type="com.xpaas.zpbg.dto.ZjyjExcelDTO">
        <result column="YJZB" property="yjzb"/>
        <result column="EJZB" property="ejzb"/>
        <result column="MKMC" property="zygzd"/>
        <result column="ZXRORDZ" property="zxrordz"/>
        <result column="SYZJXM" property="syzjxm"/>
        <result column="CLMC" property="clmc"/>
        <result column="YJQF" property="yjqf"/>
        <result column="YJLX" property="yjlx"/>
        <result column="KJBQ" property="kjbq"/>
        <result column="PJNR" property="pjnr"/>
        <result column="CJRQ" property="sytime"/>

    </resultMap>

    <select id="selectZjyjPage"  resultMap="zjyjResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZJYJ  ZJYJ
        left join T_DT_JXPJ_ZPBG_BBPZ bbpz on ZJYJ.BBID = bbpz.BBID and ZJYJ.yjkey = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        where ZJYJ.scbj = 0 AND (ZJYJ.yjkey is null or ZJYJ.yjkey = '' OR  bbpz.COMMENT_ID is not null)
        <if test="zjyj.bbid != null ">
            AND ZJYJ.bbid = #{zjyj.bbid}
        </if>
        <if test="zjyj.bgid != null ">
            AND  ZJYJ.bgid = #{zjyj.bgid}
        </if>
        <if test="zjyj.bgmkid != null ">
            AND  ZJYJ.bgmkid = #{zjyj.bgmkid}
        </if>
        <if test="zjyj.xgrid != null">
            AND  ZJYJ.xgrid = #{zjyj.xgrid}
        </if>
        <if test="zjyj.syzjid != null">
            AND  ZJYJ.syzjid = #{zjyj.syzjid}
        </if>
        <if test="zjyj.yjlx != null and zjyj.yjlx!=''">
          <if test="zjyj.yjlx ==5">
              AND  ZJYJ.yjlx in (1,2,3,4,5)
          </if>
          <if test="zjyj.yjlx !=5">
                AND  ZJYJ.yjlx = #{zjyj.yjlx}
          </if>
        </if>
        <if test="zjyj.yjkey != null and zjyj.yjkey!=''">
            AND  ZJYJ.yjkey = #{zjyj.yjkey}
        </if>
        ORDER BY bbpz.px
    </select>
    <select id="selectOtherzjPage"  resultMap="zjyjResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZJYJ  ZJYJ  where ZJYJ.scbj = 0
        <if test="zjyj.syzjid!= null">
            AND ZJYJ.syzjid != #{zjyj.syzjid}
        </if>
        <if test="zjyj.bgid != null ">
            AND ZJYJ.bgid = #{zjyj.bgid}
        </if>
        <if test="zjyj.bbid != null ">
            AND ZJYJ.bbid = #{zjyj.bbid}
        </if>
        <if test="zjyj.bgmkid != null ">
            AND ZJYJ.bgmkid = #{zjyj.bgmkid}
        </if>

        ORDER BY YJQF
    </select>

    <select id="getZjyjExcel" parameterType="String" resultMap="ZjyjExcelResultMap">
        SELECT
	        bgmk.YJZB,
	        bgmk.EJZB,
	        bgmk.MKMC,
	        zxrordz.nn AS ZXRORDZ,
	        zjyj.SYZJXM,
	        zjyj.CJRQ,
	     	CAST(zjyj.YJQF AS CHAR) as YJQF,
	        CAST(zjyj.YJLX AS CHAR) as YJLX,
	        zjyj.KJBQ,
	        zjyj.PJNR,
	        zjyj.GLXXMC AS CLMC

        FROM
	        t_dt_jxpj_zpbg_zjyj AS zjyj
	    JOIN t_dt_jxpj_zpbg_bgmk AS bgmk ON zjyj.bgmkid = bgmk.ID
	    JOIN (SELECT
	        bb.bgmkid,
        	bb.NAMES,
	        GROUP_CONCAT( bb.NAMES SEPARATOR '/' ) AS nn
        FROM
	        (
            	SELECT
	        	    bgmkid,
	        	    GROUP_CONCAT( RYXM ) AS NAMES
	            FROM
		             t_dt_jxpj_zpbg_rwfg AS rwfg
	            WHERE
		             scbj = 0
		        AND BGID = #{bgid}
	    GROUP BY
	    	bgmkid,
	    	FGLX
	        ) AS bb
        GROUP BY bb.bgmkid) as zxrordz on zxrordz.bgmkid = bgmk.ID
        WHERE
	    zjyj.scbj = 0
	        AND zjyj.BGID = #{bgid}
	        AND zjyj.bbid = #{bbid}
	        AND zjyj.bgmkid = #{bgmkid}
	    ORDER BY YJQF
    </select>

    <select id="getzjyjList" resultMap="zjyjResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZJYJ ZJYJ
        inner JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on ZJYJ.BBID = bbpz.BBID and ZJYJ.yjkey = bbpz.COMMENT_ID and bbpz.SCBJ = 0
         where ZJYJ.scbj = 0 AND ZJYJ.bbid = #{bbid}
        <foreach collection="glkeyList" item="item" open=" AND ZJYJ.YJKEY IN (" separator="," close=")">
            #{item}
        </foreach>
        order by ZJYJ.cjrq
    </select>
    <select id="getZjyjCount" resultType="int">
        SELECT
        ( SELECT count( 1 ) FROM T_DT_JXPJ_ZPBG_ZJYJ ZJYJ
        left JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on ZJYJ.BBID = bbpz.BBID and ZJYJ.yjkey = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        where ZJYJ.scbj = 0
        AND (ZJYJ.yjkey is null or ZJYJ.yjkey = '' OR  bbpz.COMMENT_ID is not null)
        and ZJYJ.BBID = #{bbid} and ZJYJ.BGID = #{bgid} and ZJYJ.BGMKID = #{bgmkid}
        <if test="syzjid != null ">
            AND ZJYJ.syzjid = #{syzjid}
        </if>
        <if test="glkeyList != null and glkeyList.size() > 0">
            <foreach collection="glkeyList" item="item" open=" AND ZJYJ.yjkey IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <select id="getyjChecking" resultType="int">
        select COUNT(1) from T_DT_JXPJ_ZPBG_ZJYJ ZJYJ
           left join T_DT_JXPJ_ZPBG_BBPZ bbpz on ZJYJ.BBID = bbpz.BBID and ZJYJ.yjkey = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        where ZJYJ.scbj = 0 AND (ZJYJ.yjkey is null or ZJYJ.yjkey = '' OR  bbpz.COMMENT_ID is not null)
         AND ZJYJ.scbj = 0
	        AND ZJYJ.BGID = #{bgid}
	        AND ZJYJ.bbid = #{bbid}
	        AND ZJYJ.bgmkid = #{bgmkid}
	        AND ZJYJ.XGZT ='1'
			AND (ZJYJ.XGSM is null or ZJYJ.XGSM ='')
    </select>

    <select id="getLsyj" resultMap="zjyjResultMap">
       select * from T_DT_JXPJ_ZPBG_ZJYJ ZJYJ
        left join T_DT_JXPJ_ZPBG_BBPZ bbpz on ZJYJ.BBID = bbpz.BBID and ZJYJ.yjkey = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        where ZJYJ.scbj = 0 AND (ZJYJ.yjkey is null OR ZJYJ.yjkey = '' OR  bbpz.COMMENT_ID is not null)
      AND ZJYJ.scbj = 0
     AND ZJYJ.BGID = #{bgid}
     AND ZJYJ.bbid = #{bbid}
     AND ZJYJ.bgmkid = #{bgmkid}
        <if test="syzjid != null">
            AND syzjid = #{syzjid}
        </if>
     ORDER BY YJLX DESC
</select>

</mapper>
