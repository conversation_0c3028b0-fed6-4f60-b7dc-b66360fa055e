package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Wzlljl;
import com.xpaas.zpbg.mapper.WzlljlMapper;
import com.xpaas.zpbg.service.IWzlljlService;
import com.xpaas.zpbg.vo.WzlljlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-文章浏览记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Slf4j
@Service
public class WzlljlServiceImpl extends BaseServiceImpl<WzlljlMapper, Wzlljl> implements IWzlljlService {

	@Override
	public IPage<WzlljlVO> selectWzlljlPage(IPage<WzlljlVO> page, WzlljlVO wzlljl) {
		return page.setRecords(baseMapper.selectWzlljlPage(page, wzlljl));
	}

}
