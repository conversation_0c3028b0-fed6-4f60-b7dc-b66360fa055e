package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-关键词检测记录实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_GJCJCJL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Gjcjcjl对象", description = "教学评价-自评报告-关键词检测记录")
public class Gjcjcjl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 进度管理ID
	*/
	@ExcelProperty("进度管理ID")
	@ApiModelProperty(value = "进度管理ID")
	@TableField("JDGLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long jdglid;

	/**
	* 关键词个数
	*/
	@ExcelProperty("关键词个数")
	@ApiModelProperty(value = "关键词个数")
	@TableField("GJCGS")
	private Integer gjcgs;



}
