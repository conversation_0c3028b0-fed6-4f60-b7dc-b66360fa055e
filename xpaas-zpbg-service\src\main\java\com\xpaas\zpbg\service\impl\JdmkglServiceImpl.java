package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Jdmkgl;
import com.xpaas.zpbg.mapper.JdmkglMapper;
import com.xpaas.zpbg.service.IJdmkglService;
import com.xpaas.zpbg.service.IXxfbService;
import com.xpaas.zpbg.vo.JdmkglVO;
import com.xpaas.zpbg.vo.XxfbUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-进度模块关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class JdmkglServiceImpl extends BaseServiceImpl<JdmkglMapper, Jdmkgl> implements IJdmkglService {

	@Autowired
	IXxfbService xxfbService;

	/**
	 * 数据查询
	 */
	@Override
	public IPage<JdmkglVO> selectJdmkglPage(IPage<JdmkglVO> page, JdmkglVO jdmkgl) {
		return page.setRecords(baseMapper.selectJdmkglPage(page, jdmkgl));
	}

	/**
	 * 保存进度模块
	 */
	@Override
	public boolean jdmkglSave(JdmkglVO jdmkgl) {
		// 删除进度模块关联
		baseMapper.jdmkglDelete(jdmkgl);

		// 保存进度模块关联
		for(String bgmkid : jdmkgl.getBgmkidList()) {
			Jdmkgl item = new Jdmkgl();
			item.setJdglid(jdmkgl.getJdglid());
			item.setBgmkid(Long.parseLong(bgmkid));
			super.save(item);
		}

		// 获取当前年月日
		LocalDate date = LocalDate.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

		// 创建当日开始的进度，关联模块时发送消息
		if(date.format(formatter).equals(jdmkgl.getKssj())){
			List<Map<String, Object>> dataList;
			List<XxfbUserVO> userList = new ArrayList<>();
			Long lastBbid = -999L;
			// 1.
			// 创建当日开始的进度，关联模块时
			// 该进度下关联的所有模块的点长和撰写人都收到消息
			// 管理员发布了一条自评报告撰写任务，请您及时处理。
			dataList = baseMapper.selectMessage(jdmkgl);
			if (dataList != null) {
				userList.clear();
				lastBbid = -999L;

				for (Map<String, Object> map : dataList) {
					Long bbid = MapUtils.getLong(map, "bbid");
					if (!bbid.equals(lastBbid)) {
						if (!userList.isEmpty()) {
							xxfbService.sendMessageYw(
									"管理员发布了一条自评报告撰写任务，请您及时处理。",
									"zxrOrDz",
									userList,
									"撰写人或点长",
									lastBbid);
							userList.clear();
						}
					}
					String userId = MapUtils.getString(map, "userId");
					XxfbUserVO user = new XxfbUserVO();
					userList.add(user);
					user.setUserId(userId);
					lastBbid = bbid;
				}

				if (!userList.isEmpty()) {
					xxfbService.sendMessageYw(
							"管理员发布了一条自评报告撰写任务，请您及时处理。",
							"zxrOrDz",
							userList,
							"撰写人或点长",
							lastBbid);
					userList.clear();
				}
			}
		}

		return true;
	}

}
