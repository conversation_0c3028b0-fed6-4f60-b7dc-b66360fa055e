package com.xpaas.zlkgl.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;
import java.util.Objects;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
/**
 * 教学评价-资料库平台-资料管理表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Component
public class ZlglWrapper extends BaseEntityWrapper<Zlgl, ZlglVO>  {


	/**
	* 将entity转换成 entityVO
	 * <AUTHOR>
	 * @since 2025-07-25
    * @return 转换后的entityVO对象
    */
	@Override
	public ZlglVO entityVO(Zlgl zlgl) {
		ZlglVO zlglVO = Objects.requireNonNull(BeanUtil.copy(zlgl, ZlglVO.class));
		//User cjr = UserCache.getUser(zlgl.getCjr());
		//if (cjr != null){
		//	zlglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zlgl.getGxr());
		//if (gxr != null){
		//	zlglVO.setGxrName(gxr.getName());
		//}
/**  **/
		return zlglVO;
	}





    @Override
    public ZlglVO wrapperVO(ZlglVO zlglVO) {
		//User cjr = UserCache.getUser(zlglVO.getCjr());
		//if (cjr != null){
		//	zlglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zlglVO.getGxr());
		//if (gxr != null){
		//	zlglVO.setGxrName(gxr.getName());
		//}
/**  */
        return zlglVO;
    }

}
