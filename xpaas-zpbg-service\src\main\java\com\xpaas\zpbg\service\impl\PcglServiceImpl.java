package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Pcgl;
import com.xpaas.zpbg.mapper.PcglMapper;
import com.xpaas.zpbg.service.IPcglService;
import com.xpaas.zpbg.vo.PcglVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-批次管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@Service
public class PcglServiceImpl extends BaseServiceImpl<PcglMapper, Pcgl> implements IPcglService {

	/**
	 * 自定义分页
	 */
	@Override
	public IPage<PcglVO> selectPcglPage(IPage<PcglVO> page, @Param("pcgl") Pcgl pcgl) {
		return page.setRecords(baseMapper.selectPcglPage(page, pcgl));
	}
	/**
	 * 自定义分页
	 */
	@Override
	public IPage<PcglVO> selectPcglPageCjrqDesc(IPage<PcglVO> page,@Param("pcgl") Pcgl pcgl) {
		return page.setRecords(baseMapper.selectPcglPageCjrqDesc(page, pcgl));
	}

}
