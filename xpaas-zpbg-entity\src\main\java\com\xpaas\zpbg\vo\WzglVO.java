package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Wzgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 自评报告-文章管理 视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZtwzVO对象", description = "自评报告-文章管理 视图实体类")
public class WzglVO extends Wzgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "浏览人数")
    private String llrs;

    @ApiModelProperty(value = "附件文件")
    private List<Map<String,Object>> fileList;

    @ApiModelProperty(value = "数据来源 1：慕课 2：文章")
    private Integer sjly;
}
