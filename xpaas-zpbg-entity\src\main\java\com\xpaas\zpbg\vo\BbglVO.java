package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Bbgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-版本管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BbglVO对象", description = "教学评价-自评报告-版本管理")
public class BbglVO extends Bbgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
        private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "进度id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jdglid;

    @ApiModelProperty(value = "进度名称")
    private String jdmc;

    @ApiModelProperty(value = "专家姓名")
    private String zjxm;

    @ApiModelProperty(value = "所属角色")
    private ZtxxRoleType role;
}
