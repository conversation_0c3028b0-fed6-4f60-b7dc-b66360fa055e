package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Joiner;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Sjzbgl;
import com.xpaas.zpbg.service.ISjzbglService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.SjclVO;
import com.xpaas.zpbg.vo.SjzbglVO;
import com.xpaas.zpbg.wrapper.SjzbglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-数据指标关联 控制器
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sjzbgl")
@Api(value = "教学评价-自评报告-数据指标关联", tags = "教学评价-自评报告-数据指标关联接口")
public class SjzbglController extends BaseController {

	private SjzbglWrapper sjzbglWrapper;
	private ISjzbglService sjzbglService;

	@Autowired
	private ResourceLoader resourceLoader;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-详情")
	public R<SjzbglVO> detail(Sjzbgl sjzbgl) {
		Sjzbgl detail = sjzbglService.getOne(Condition.getQueryWrapper(sjzbgl));
		return R.data(sjzbglWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-数据指标关联 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-列表")
	public R<IPage<SjzbglVO>> list(Sjzbgl sjzbgl, Query query) {
		IPage<Sjzbgl> pages = sjzbglService.page(Condition.getPage(query), Condition.getQueryWrapper(sjzbgl));
		return R.data(sjzbglWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-数据指标关联
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-自定义分页")
    public R<IPage<SjzbglVO>> page(SjzbglVO sjzbglVO, Query query) {
    	if(StringUtil.isNotBlank(sjzbglVO.getGlkeyStr())) {
			sjzbglVO.setGlkeyList(Arrays.asList(sjzbglVO.getGlkeyStr().split(",")));
		}

        IPage<SjzbglVO> pages = sjzbglService.selectSjzbglPage(Condition.getPage(query), sjzbglVO);
        return R.data(pages);
    }

	/**
	 * 新增多条记录 教学评价-自评报告-不同的内容引用了相同的数据表
	 */
	@PostMapping("/sameGl")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增多条记录", notes = "传入sjbgl")
	@ApiLog("数据指标关联-不同的内容引用了相同的数据表")
	public R sameGl(@Valid @RequestBody SjzbglVO sjzbglVO) {
		String msg = sjzbglService.sameGl(sjzbglVO);

		return R.data(msg);
	}

	/**
	 * 新增 教学评价-自评报告-数据指标关联
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-新增")
	public R save(@Valid @RequestBody SjzbglVO sjzbglVO) {
		boolean b = sjzbglService.save(sjzbglVO);

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-数据指标关联
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-修改")
	public R update(@Valid @RequestBody SjzbglVO sjzbglVO) {
		boolean b = sjzbglService.updateById(sjzbglVO);
		return R.status(b);
	}

	/**
	 * 更新排序顺序
	 */
	@PostMapping("/updataPx")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "更新排序顺序", notes = "传入id和排序")
	@ApiLog("数据指标关联-更新排序顺序")
	public R updataPx(@Valid @RequestBody SjzbglVO sjzbglVO) {
		Sjzbgl sjzbgl = sjzbglService.getById(sjzbglVO.getId());
		sjzbgl.setPx(sjzbglVO.getPx());
		sjzbglService.updateById(sjzbgl);

		return R.status(true);
	}

	/**
	 * 新增或修改 教学评价-自评报告-数据指标关联 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入sjzbgl")
	@ApiLog("数据指标关联-新增或修改")
	public R submit(@Valid @RequestBody Sjzbgl sjzbgl) {
		return R.status(sjzbglService.saveOrUpdate(sjzbgl));
	}

	
	/**
	 * 删除 教学评价-自评报告-数据指标关联
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("数据指标关联-删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		sjzbglService.deleteGl(deleteVO.getIds());

		return R.status(true);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("数据指标关联-高级查询")
	public R<IPage<SjzbglVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Sjzbgl> queryWrapper = Condition.getQueryWrapper(map, Sjzbgl.class);
		IPage<Sjzbgl> pages = sjzbglService.page(Condition.getPage(query), queryWrapper);
		return R.data(sjzbglWrapper.pageVO(pages));
	}

	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("数据指标关联-导入")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Sjzbgl> list = ExcelUtil.read(file, Sjzbgl.class);
		//TODO 此处需要根据具体业务添加代码
		sjzbglService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("数据指标关联-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Sjzbgl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Sjzbgl> list = sjzbglService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Sjzbgl导入模板", "Sjzbgl导入模板",columnFiledNames, list, Sjzbgl.class);
	}

	/**
	 * 取得关键词
	 */
	@GetMapping("/searchGjc")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入sjzbglVO")
	@ApiLog("数据指标关联-取得关键词")
	public R searchGjc(SjzbglVO sjzbglVO) {
		List<SjzbglVO> list = sjzbglService.searchGjc(sjzbglVO);
		return R.data(list);
	}

	/**
	 * 校验关键词是否关联
	 */
	@GetMapping("/checkGlkey")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入sjzbglVO")
	@ApiLog("数据指标关联-校验关键词是否关联")
	public R checkGlkey(SjzbglVO sjzbglVO) {
		if(StringUtil.isNotBlank(sjzbglVO.getGlkeyStr())) {
			sjzbglVO.setGlkeyList(Arrays.asList(sjzbglVO.getGlkeyStr().split(",")));
		}

		List<String> list = sjzbglService.checkGlkey(sjzbglVO);
		return R.data(Joiner.on(",").join(list));
	}

	/**
	 * 查询数据材料列表
	 */
	@GetMapping("/searchSjlc")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入sjzbglVO")
	@ApiLog("数据指标关联-查询数据材料列表")
	public R searchSjlc(SjzbglVO sjzbglVO) {
		List<SjclVO> list = sjzbglService.searchSjlc(sjzbglVO);
		return R.data(list);
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/exportExcel")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("数据指标关联-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "报告ID") String bgid,
							@ApiParam(value = "报告模块ID") String bgmkid,
							@ApiParam(value = "版本ID") String bbid,
							@ApiParam(value = "关联KEY") String glkeyStr) {

		SjzbglVO sjzbglVO = new SjzbglVO();
		sjzbglVO.setBgid(Long.parseLong(bgid));
		sjzbglVO.setBgmkid(Long.parseLong(bgmkid));
		sjzbglVO.setBbid(Long.parseLong(bbid));
		sjzbglVO.setGlkeyStr(glkeyStr);

		Query query = new Query();
		query.setCurrent(1);
		query.setSize(-1);

		IPage<SjzbglVO> pages = sjzbglService.selectSjzbglPage(Condition.getPage(query), sjzbglVO);

		export(response, pages.getRecords());
	}

	private void export(HttpServletResponse response, List<SjzbglVO> dataList) {
		try {
			String path = "classpath:doc/sjzbgl.xlsx";
			org.springframework.core.io.Resource resource = resourceLoader.getResource(path);
			InputStream inStream= resource.getInputStream();
			XSSFWorkbook wb = new XSSFWorkbook(inStream);
			response.reset();
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(Charsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=sjzbgl.xlsx");

			writeData(wb, dataList);
			wb.write(response.getOutputStream());
			wb.close();

		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private void writeData(XSSFWorkbook wb, List<SjzbglVO> list) {
		XSSFSheet sheet = wb.getSheetAt(0);
		int rowIndex = 1;
		Row row = null;

		if(list != null) {
			for(int i = 0; i < list.size(); i++) {
				SjzbglVO vo = list.get(i);

				int colIndex = 0;
				// 插入行
				row = sheet.createRow(rowIndex);

				// 序号
				setCellValue(wb, row, (i + 1), colIndex);
				colIndex++;

				if(vo.getSjzblx() == 1) {
					// 指标名称
					setCellValue(wb, row, vo.getZbmc(), colIndex);
					colIndex++;

					// 参考结果
					setCellValue(wb, row, vo.getCkjg(), colIndex);
					colIndex++;

					// 数据来源
					setCellValue(wb, row, vo.getSjlymc(), colIndex);
					colIndex++;

					// 关键词名称
					setCellValue(wb, row, "", colIndex);
					colIndex++;
				} else {
					// 指标名称
					setCellValue(wb, row, "", colIndex);
					colIndex++;

					// 参考结果
					setCellValue(wb, row, "", colIndex);
					colIndex++;

					// 数据来源
					setCellValue(wb, row, "", colIndex);
					colIndex++;

					// 关键词名称
					setCellValue(wb, row, vo.getGjcmc(), colIndex);
					colIndex++;
				}

				rowIndex++;
			}
		}
	}

	private void setCellValue(XSSFWorkbook wb, Row row, Object str, int col) {
		Font titleFont = wb.createFont();
		titleFont.setFontName("等线");
		titleFont.setFontHeightInPoints((short) 12);
		titleFont.setColor(IndexedColors.BLACK.index);
		XSSFCellStyle titleStyle = wb.createCellStyle();
		titleStyle.setWrapText(true);
		titleStyle.setAlignment(HorizontalAlignment.CENTER);
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		titleStyle.setFont(titleFont);

		byte[] rgb = new byte[]{(byte) 0, (byte) 0, (byte) 0};
		setBorder(titleStyle, BorderStyle.THIN, new XSSFColor(rgb ,null));
		Cell cell = row.createCell(col);
		if(str == null) {
			cell.setCellValue("");
		} else {
			cell.setCellValue(String.valueOf(str));
		}

		cell.setCellStyle(titleStyle);
	}

	private void setBorder(XSSFCellStyle style, BorderStyle border, XSSFColor color) {
		style.setBorderTop(border);
		style.setBorderLeft(border);
		style.setBorderRight(border);
		style.setBorderBottom(border);
		style.setBorderColor(XSSFCellBorder.BorderSide.TOP, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, color);
	}
}
