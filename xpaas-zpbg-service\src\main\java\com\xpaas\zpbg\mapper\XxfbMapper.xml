<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.XxfbMapper">

<!--    &lt;!&ndash; 通用查询映射结果 &ndash;&gt;-->
<!--    <resultMap id="xxfbResultMap" type="com.xpaas.zpbg.entity.Xxfb">-->
<!--        <result column="ID" property="id"/>-->
<!--        <result column="ZHID" property="zhid"/>-->
<!--        <result column="CJR" property="cjr"/>-->
<!--        <result column="CJBM" property="cjbm"/>-->
<!--        <result column="CJDW" property="cjdw"/>-->
<!--        <result column="CJRQ" property="cjrq"/>-->
<!--        <result column="GXR" property="gxr"/>-->
<!--        <result column="GXRQ" property="gxrq"/>-->
<!--        <result column="SCBJ" property="scbj"/>-->
<!--        <result column="ZT" property="zt"/>-->

<!--    </resultMap>-->

<!--    &lt;!&ndash; 通用查询映射结果 &ndash;&gt;-->
<!--    <resultMap id="xxfbResultMapVO" type="com.xpaas.zpbg.vo.XxfbVO">-->
<!--        <result column="ID" property="id"/>-->
<!--        <result column="ZHID" property="zhid"/>-->
<!--        <result column="CJR" property="cjr"/>-->
<!--        <result column="CJBM" property="cjbm"/>-->
<!--        <result column="CJDW" property="cjdw"/>-->
<!--        <result column="CJRQ" property="cjrq"/>-->
<!--        <result column="GXR" property="gxr"/>-->
<!--        <result column="GXRQ" property="gxrq"/>-->
<!--        <result column="SCBJ" property="scbj"/>-->
<!--        <result column="ZT" property="zt"/>-->

<!--    </resultMap>-->

<!--    <select id="getBgWarnList" resultType="map">-->
<!--        SELECT-->
<!--            bgmk.ID as id,-->
<!--            bgmk.BGID as bgid,-->
<!--            jdmkgl.ID as jdmkglid,-->
<!--            bbgl.ID as bbglid,-->
<!--            jdmkgl.JDGLID as jdglid,-->
<!--            bgmk.MKMC as mkmc,-->
<!--            DATEDIFF(date_format( jdgl.ZXJSSJ, '%Y%m%d'), curdate()) as wcsx-->
<!--        FROM-->
<!--            T_DT_JXPJ_ZPBG_BGGL bggl-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDGL jdgl-->
<!--            ON jdgl.BGID = bggl.id-->
<!--            AND jdgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BGMK bgmk-->
<!--            ON bgmk.BGID = bggl.id-->
<!--            AND bgmk.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl-->
<!--            ON jdmkgl.JDGLID = jdgl.id-->
<!--            AND jdmkgl.BGMKID = bgmk.ID-->
<!--            AND jdmkgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_RWFG rwfg-->
<!--            ON rwfg.BGID = bggl.id-->
<!--            AND rwfg.BGMKID = bgmk.ID-->
<!--            AND rwfg.scbj = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BBGL bbgl-->
<!--            ON bbgl.BGID = jdgl.id-->
<!--            AND bbgl.BGMKID = bgmk.ID-->
<!--            AND bbgl.JDGLID = jdgl.ID-->
<!--            AND bbgl.BBLX = 1-->
<!--            AND bbgl.SCBJ = 0-->
<!--        WHERE-->
<!--            bggl.SCBJ = 0-->
<!--            AND bgmk.BGMKZT in (0, 1)-->
<!--        GROUP BY bgmk.ID-->
<!--        HAVING-->
<!--            WCSX = 1-->
<!--            OR WCSX = 2-->

<!--    </select>-->

<!--    <select id="getBgStartWarnList" resultType="map">-->
<!--        SELECT-->
<!--            bgmk.ID as id,-->
<!--            bgmk.BGID as bgid,-->
<!--            jdmkgl.ID as jdmkglid,-->
<!--            bbgl.ID as bbglid,-->
<!--            jdmkgl.JDGLID as jdglid,-->
<!--            bgmk.MKMC as mkmc-->
<!--        FROM-->
<!--            T_DT_JXPJ_ZPBG_BGGL bggl-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDGL jdgl-->
<!--            ON jdgl.BGID = bggl.id-->
<!--            AND jdgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BGMK bgmk-->
<!--            ON bgmk.BGID = bggl.id-->
<!--            AND bgmk.scbj = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl-->
<!--            ON jdmkgl.JDGLID = jdgl.id-->
<!--            AND jdmkgl.BGMKID = bgmk.ID-->
<!--            AND jdmkgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_RWFG rwfg-->
<!--            ON rwfg.BGID = bggl.id-->
<!--            AND rwfg.BGMKID = bgmk.ID-->
<!--            AND rwfg.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BBGL bbgl-->
<!--            ON bbgl.BGID = jdgl.id-->
<!--            AND bbgl.BGMKID = bgmk.ID-->
<!--            AND bbgl.JDGLID = jdgl.ID-->
<!--            AND bbgl.BBLX = 1-->
<!--            AND bbgl.SCBJ = 0-->
<!--        WHERE-->
<!--            bggl.SCBJ = 0-->
<!--            AND DATEDIFF(date_format(jdgl.KSSJ, '%Y%m%d'), curdate()) = 0-->
<!--        GROUP BY bgmk.ID-->

<!--    </select>-->

<!--    <select id="getBgDelayWarnList" resultType="map">-->
<!--        SELECT-->
<!--            bgmk.ID as id,-->
<!--            bgmk.BGID as bgid,-->
<!--            jdgl.id as jdid,-->
<!--            jdmkgl.ID as jdmkglid,-->
<!--            bbgl.ID as bbglid,-->
<!--            jdmkgl.JDGLID as jdglid,-->
<!--            bgmk.BGMKZT as bgmkzt,-->
<!--            bgmk.MKMC as mkmc-->
<!--        FROM-->
<!--            T_DT_JXPJ_ZPBG_BGGL bggl-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDGL jdgl-->
<!--            ON jdgl.BGID = bggl.id-->
<!--            AND jdgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BGMK bgmk-->
<!--            ON bgmk.BGID = bggl.id-->
<!--            AND bgmk.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_JDMKGL jdmkgl-->
<!--            ON jdmkgl.JDGLID = jdgl.id-->
<!--            AND jdmkgl.BGMKID = bgmk.ID-->
<!--            AND jdmkgl.SCBJ = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_RWFG rwfg-->
<!--            ON rwfg.BGID = bggl.id-->
<!--            AND rwfg.BGMKID = bgmk.ID-->
<!--            AND rwfg.scbj = 0-->
<!--            INNER JOIN-->
<!--                T_DT_JXPJ_ZPBG_BBGL bbgl-->
<!--            ON bbgl.BGID = jdgl.id-->
<!--            AND bbgl.BGMKID = bgmk.ID-->
<!--            AND bbgl.JDGLID = jdgl.ID-->
<!--            AND bbgl.BJDZSBC = 1-->
<!--            AND bbgl.BBLX = 1-->
<!--            AND bbgl.SCBJ = 0-->
<!--        WHERE-->
<!--            bggl.SCBJ = 0-->

<!--            AND bgmk.BGMKZT = 10-->
<!--            AND DATEDIFF(date_format(jdgl.ZXJSSJ, '%Y%m%d'), curdate()) = -1-->

<!--        GROUP BY bgmk.ID-->

<!--    </select>-->

<!--    <select id="getRwfgList" resultType="map">-->
<!--        select * from T_DT_JXPJ_ZPBG_RWFG-->
<!--        where-->
<!--            scbj = 0-->
<!--        <if test="bgId != null and bgId != ''">-->
<!--            and BGID = #{bgId}-->
<!--        </if>-->
<!--        <if test="bgmkId != null and bgmkId != ''">-->
<!--            and BGMKID = #{bgmkId}-->
<!--        </if>-->
<!--    </select>-->

<!--    <update id="updatBgmkById">-->
<!--        update T_DT_JXPJ_ZPBG_BGMK set BGMKZT = 20 where id = #{bgmkId}-->
<!--    </update>-->

<!--    <update id="updatBbglById">-->
<!--        update T_DT_JXPJ_ZPBG_BBGL-->
<!--        set BGMKZT = 20-->
<!--        where-->
<!--            BGID = #{bgId}-->
<!--            AND BGMKID = #{bgmkId}-->
<!--            AND JDGLID = #{jdglId}-->
<!--            AND BBLX = 1-->
<!--            AND scbj = 0-->
<!--    </update>-->

</mapper>
