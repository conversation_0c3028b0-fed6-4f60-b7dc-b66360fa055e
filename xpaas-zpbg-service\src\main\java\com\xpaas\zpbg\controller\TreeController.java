package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.ITreeService;
import com.xpaas.zpbg.vo.TreeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 教学评价-自评报告配置管理-树 控制器
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/tree")
@Api(value = "教学评价-自评报告配置管理-树", tags = "教学评价-自评报告配置管理-树 控制器")
public class TreeController extends BaseController {

    private ITreeService treeService;

    /**
     * 模块树
     */
    @GetMapping("/getMkTree")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "板块树", notes = "传入treeVO")
    @ApiLog("模块树")
    public R<List<TreeVO>> getMkTree(TreeVO treeVO) {
        // 需要参数，模块类型
        List<TreeVO> list = treeService.getMkTree(treeVO);
        return R.data(list);
    }

    /**
     * 报告树
     */
    @GetMapping("/getBgTree")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "报告树", notes = "传入treeVO")
    @ApiLog("报告模块树")
    public R<List<TreeVO>> getBgTree(TreeVO treeVO) {
        // 需要参数，报告ID
        List<TreeVO> list = treeService.getBgTree(treeVO);
        return R.data(list);
    }
}
