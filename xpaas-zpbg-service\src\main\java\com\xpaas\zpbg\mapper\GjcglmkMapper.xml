<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.GjcglmkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcglmkResultMap" type="com.xpaas.zpbg.entity.Gjcglmk">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="GJCID" property="gjcid"/>
        <result column="MKID" property="mkid"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcglmkResultMapVO" type="com.xpaas.zpbg.vo.GjcglmkVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="GJCID" property="gjcid"/>
        <result column="MKID" property="mkid"/>
    </resultMap>

    <!-- 自定义分页 -->
    <select id="selectGjcglmkPage" resultMap="gjcglmkResultMapVO">
        select * from T_DT_JXPJ_ZPBG_GJCGLMK where scbj = 0
    </select>

    <!-- 关键词关联模块查询 -->
    <select id="selectGjcglmkList" resultType="com.xpaas.zpbg.vo.GjcglmkVO">
        select * from T_DT_JXPJ_ZPBG_GJCGLMK where scbj = 0 and gjcid = #{gjcid}
    </select>

    <!-- 关键词关联模块删除 -->
    <delete id="gjcglmkDelete">
        delete from T_DT_JXPJ_ZPBG_GJCGLMK where gjcid = #{gjcid}
    </delete>

    <!-- 删除关键词智能检测表中该关键词数据 -->
    <update id="removeByGjcidMkid">
        update T_DT_JXPJ_ZPBG_GJCZNJC
        <set>
            scbj = 1
        </set>
        where gjcid = #{gjcid}
        and bgmkid in (
            select id from t_dt_jxpj_zpbg_bgmk
            where scbj = 0
            and mkid = #{mkid})
    </update>

    <!-- 复原关键词智能检测表中该关键词数据 -->
    <update id="restoreByGjcidMkid">
        update T_DT_JXPJ_ZPBG_GJCZNJC
        <set>
            scbj = 0
        </set>
        where gjcid = #{gjcid}
        and bgmkid in (
            select id from t_dt_jxpj_zpbg_bgmk
            where scbj = 0
            and mkid = #{mkid})
    </update>

</mapper>
