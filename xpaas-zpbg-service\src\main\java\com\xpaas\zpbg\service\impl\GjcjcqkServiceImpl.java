package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Gjcjcqk;
import com.xpaas.zpbg.mapper.GjcjcqkMapper;
import com.xpaas.zpbg.service.IGjcjcqkService;
import com.xpaas.zpbg.vo.GjcjcqkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-关键词检测情况 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@Service
public class GjcjcqkServiceImpl extends BaseServiceImpl<GjcjcqkMapper, Gjcjcqk> implements IGjcjcqkService {

	@Override
	public IPage<GjcjcqkVO> selectGjcjcqkPage(IPage<GjcjcqkVO> page, GjcjcqkVO gjcjcqk) {
		return page.setRecords(baseMapper.selectGjcjcqkPage(page, gjcjcqk));
	}

}
