package com.xpaas.zlkgl.mapper;

import com.xpaas.zlkgl.entity.ZlkJdgl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

import com.xpaas.zlkgl.vo.ZlkJdglVO;
import org.springframework.stereotype.Repository;

/**
 * 教学评价-资料库平台-节点管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface ZlkJdglMapper extends BaseMapper<ZlkJdgl> {

	/**
	 * 自定义分页
	 * 分页查询教学评价-资料库平台-节点管理表表数据
	 * @param page
	 * @param jdgl
	 * <AUTHOR>
	 * @since 2025-07-24
	 * @return
	 */
	List<ZlkJdglVO> selectJdglPage(IPage page, ZlkJdglVO jdgl);

}
