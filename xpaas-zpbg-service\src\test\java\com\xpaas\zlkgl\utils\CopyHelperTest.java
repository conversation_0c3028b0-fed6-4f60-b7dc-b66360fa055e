package com.xpaas.zlkgl.utils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 复制工具类测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
@ActiveProfiles("test")
public class CopyHelperTest {

    @Autowired
    private CopyHelper copyHelper;

    /**
     * 测试名称校验功能
     */
    @Test
    public void testValidateName() {
        // 测试合法名称
        assertNull(copyHelper.validateName("正常文件名.txt"));
        assertNull(copyHelper.validateName("Normal File Name"));
        assertNull(copyHelper.validateName("文件123"));

        // 测试非法名称
        assertNotNull(copyHelper.validateName(""));
        assertNotNull(copyHelper.validateName("   "));
        assertNotNull(copyHelper.validateName(null));
        assertNotNull(copyHelper.validateName("文件/名称"));
        assertNotNull(copyHelper.validateName("文件\\名称"));
        assertNotNull(copyHelper.validateName("文件:名称"));
        assertNotNull(copyHelper.validateName("文件*名称"));
        assertNotNull(copyHelper.validateName("文件?名称"));
        assertNotNull(copyHelper.validateName("文件\"名称"));
        assertNotNull(copyHelper.validateName("文件<名称"));
        assertNotNull(copyHelper.validateName("文件>名称"));
        assertNotNull(copyHelper.validateName("文件|名称"));
        assertNotNull(copyHelper.validateName(".隐藏文件"));
        assertNotNull(copyHelper.validateName("文件名."));

        // 测试长度限制
        String longName = "a".repeat(256);
        assertNotNull(copyHelper.validateName(longName));
    }

    /**
     * 测试唯一名称生成功能
     */
    @Test
    public void testGenerateUniqueName() {
        // 模拟名称检查器
        java.util.Set<String> existingNames = new java.util.HashSet<>();
        existingNames.add("测试文件副本");
        existingNames.add("测试文件副本(1)");

        // 测试生成唯一名称
        String uniqueName = copyHelper.generateUniqueName("测试文件", "folder1", 
            name -> existingNames.contains(name));

        assertEquals("测试文件副本(2)", uniqueName);

        // 测试没有冲突的情况
        String uniqueName2 = copyHelper.generateUniqueName("新文件", "folder1", 
            name -> false);

        assertEquals("新文件副本", uniqueName2);
    }

    /**
     * 测试属性复制功能
     */
    @Test
    public void testCopyProperties() {
        // 创建测试对象
        TestEntity source = new TestEntity();
        source.setName("源对象");
        source.setValue(100);

        TestEntity target = new TestEntity();

        // 使用属性映射器复制属性
        copyHelper.copyProperties(source, target, (src, tgt) -> {
            tgt.setName(src.getName());
            tgt.setValue(src.getValue());
        });

        assertEquals("源对象", target.getName());
        assertEquals(100, target.getValue());
    }

    /**
     * 测试实体类
     */
    private static class TestEntity {
        private String name;
        private int value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }
}
