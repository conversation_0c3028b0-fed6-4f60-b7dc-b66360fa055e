package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bar;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

;

/**
 * 自评报告-首页甘特图视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BarVO对象", description = "自评报告-首页-甘特图")
public class BarVO extends Bar {
	private static final long serialVersionUID = 1L;

	private String caption;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTime;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTime;

//	private Integer percent;
}
