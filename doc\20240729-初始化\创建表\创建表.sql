/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50736
 Source Host           : *************:3306
 Source Schema         : xpaas_jxpj_dev

 Target Server Type    : MySQL
 Target Server Version : 50736
 File Encoding         : 65001

 Date: 29/07/2024 10:48:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bbgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bbgl`;
CREATE TABLE `t_dt_jxpj_zpbg_bbgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `JDGLID` bigint(20) NULL DEFAULT NULL COMMENT '进度管理ID',
  `BBLX` tinyint(4) NULL DEFAULT NULL COMMENT '版本类型',
  `BBMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本名称',
  `BBSM` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本说明',
  `BBSJ` datetime(0) NULL DEFAULT NULL COMMENT '版本时间',
  `TJRID` bigint(20) NULL DEFAULT NULL COMMENT '提交人ID',
  `TJR` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人',
  `SSJS` tinyint(4) NULL DEFAULT NULL COMMENT '所属角色',
  `BGMKZT` tinyint(4) NULL DEFAULT NULL COMMENT '报告模块状态',
  `BJDZSBC` int(11) NULL DEFAULT 1 COMMENT '本进度撰审版次',
  `WJKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件KEY',
  `WJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `BBXX` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '版本信息',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BBGL_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BBGL_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BBGL_K3`(`JDGLID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BBGL_K4`(`TJRID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-版本管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bbwjnr
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bbwjnr`;
CREATE TABLE `t_dt_jxpj_zpbg_bbwjnr`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `WJNR` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件内容',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BBGL_K1`(`BBID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-版本文件内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bggl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bggl`;
CREATE TABLE `t_dt_jxpj_zpbg_bggl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报告名称',
  `ND` int(11) NULL DEFAULT NULL COMMENT '年度',
  `MKLX` tinyint(4) NULL DEFAULT NULL COMMENT '模块类型',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `BGZT` tinyint(4) NULL DEFAULT 1 COMMENT '报告状态',
  `JDGLID` bigint(20) NULL DEFAULT NULL COMMENT '进度管理ID',
  `BZ` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-报告管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bgmk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bgmk`;
CREATE TABLE `t_dt_jxpj_zpbg_bgmk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  `MKMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `MKLX` tinyint(4) NULL DEFAULT NULL COMMENT '模块类型',
  `CMM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重命名',
  `SFWFM` tinyint(4) NULL DEFAULT NULL COMMENT '是否为封面',
  `ZBLY` tinyint(4) NULL DEFAULT NULL COMMENT '指标来源',
  `YJZBID` bigint(20) NULL DEFAULT NULL COMMENT '一级指标ID',
  `YJZB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级指标',
  `EJZBID` bigint(20) NULL DEFAULT NULL COMMENT '二级指标ID',
  `EJZB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级指标',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `SJLY` tinyint(4) NULL DEFAULT 2 COMMENT '数据来源',
  `MBWJKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版文件KEY',
  `MBWJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版文件路径',
  `BGMKZT` tinyint(4) NULL DEFAULT NULL COMMENT '报告模块状态',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_BGMK_K1`(`BGID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-报告模块' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bgmkztjl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bgmkztjl`;
CREATE TABLE `t_dt_jxpj_zpbg_bgmkztjl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `JDGLID` bigint(20) NULL DEFAULT NULL COMMENT '进度管理ID',
  `CZLX` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `BGMKJDZT` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报告模块进度状态',
  `KSRQ` datetime(0) NULL DEFAULT NULL COMMENT '开始日期',
  `JSRQ` datetime(0) NULL DEFAULT NULL COMMENT '结束日期',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-报告模块状态记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bqgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bqgl`;
CREATE TABLE `t_dt_jxpj_zpbg_bqgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `KJBQ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快捷标签',
  `BQLX` tinyint(4) NULL DEFAULT NULL COMMENT '标签类型',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `TJR` bigint(20) NULL DEFAULT NULL COMMENT '添加人',
  `TJRXM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加人姓名',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-标签管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_gjcgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_gjcgl`;
CREATE TABLE `t_dt_jxpj_zpbg_gjcgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `GJCMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词名称',
  `GJCLX` tinyint(4) NULL DEFAULT NULL COMMENT '关键词类型',
  `GLFW` tinyint(4) NULL DEFAULT NULL COMMENT '关联范围',
  `PC` int(11) NULL DEFAULT 0 COMMENT '频次',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-关键词管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_gjcglmk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_gjcglmk`;
CREATE TABLE `t_dt_jxpj_zpbg_gjcglmk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `GJCID` bigint(20) NULL DEFAULT NULL COMMENT '关键词ID',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-关键词关联模块' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_gjcjcjl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_gjcjcjl`;
CREATE TABLE `t_dt_jxpj_zpbg_gjcjcjl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `JDGLID` bigint(20) NULL DEFAULT NULL COMMENT '进度管理ID',
  `GJCGS` int(11) NULL DEFAULT NULL COMMENT '关键词个数',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-关键词检测记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_gjcjcqk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_gjcjcqk`;
CREATE TABLE `t_dt_jxpj_zpbg_gjcjcqk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `JCJLID` bigint(20) NULL DEFAULT NULL COMMENT '检测记录ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GJCID` bigint(20) NULL DEFAULT NULL COMMENT '关键词ID',
  `GJCMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词名称',
  `PC` int(11) NULL DEFAULT 0 COMMENT '频次',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_GJCJCQK_K1`(`JCJLID`, `GJCID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-关键词检测情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_gjcznjc
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_gjcznjc`;
CREATE TABLE `t_dt_jxpj_zpbg_gjcznjc`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GJCID` bigint(20) NULL DEFAULT NULL COMMENT '关键词ID',
  `GJCMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词名称',
  `PC` int(11) NULL DEFAULT 0 COMMENT '频次',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_GJCZNJC_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_GJCZNJC_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_GJCZNJC_K3`(`BBID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-关键词智能检测' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_jdgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_jdgl`;
CREATE TABLE `t_dt_jxpj_zpbg_jdgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `JDMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '进度名称',
  `KSSJ` date NULL DEFAULT NULL COMMENT '开始时间',
  `ZXJSSJ` date NULL DEFAULT NULL COMMENT '撰写结束时间',
  `SYJSSJ` date NULL DEFAULT NULL COMMENT '审阅结束时间',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `ZXJD` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '撰写进度',
  `SYJD` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审阅进度',
  `JDZT` tinyint(4) NULL DEFAULT NULL COMMENT '进度状态',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_JDGL_K1`(`BGID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-进度管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_jdmkgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_jdmkgl`;
CREATE TABLE `t_dt_jxpj_zpbg_jdmkgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `JDGLID` bigint(20) NULL DEFAULT NULL COMMENT '进度管理ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_JDMKGL_K1`(`JDGLID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-进度模块关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_kcqk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_kcqk`;
CREATE TABLE `t_dt_jxpj_zpbg_kcqk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT 0 COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT 1 COMMENT '状态',
  `KCSJ` datetime(0) NULL DEFAULT NULL COMMENT '考察时间',
  `TJJLDJ` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推荐结论等级',
  `KCQK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '考察情况',
  `JSMBDCD` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '建设目标的达成度',
  `ZDKD` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值得肯定的地方',
  `ZYWTZZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要问题及相关佐证',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `ZYGZD` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要关注点',
  `ZYGZDID` bigint(20) NULL DEFAULT NULL COMMENT '主要关注点ID',
  `PJZJ` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价专家',
  `PJZJID` bigint(20) NULL DEFAULT NULL COMMENT '评价专家ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-考察情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_mkgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_mkgl`;
CREATE TABLE `t_dt_jxpj_zpbg_mkgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `MKMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `MKLX` tinyint(4) NULL DEFAULT NULL COMMENT '模块类型',
  `CMM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重命名',
  `SFWFM` tinyint(4) NULL DEFAULT NULL COMMENT '是否为封面',
  `ZBLY` tinyint(4) NULL DEFAULT NULL COMMENT '指标来源',
  `YJZBID` bigint(20) NULL DEFAULT NULL COMMENT '一级指标ID',
  `YJZB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级指标',
  `EJZBID` bigint(20) NULL DEFAULT NULL COMMENT '二级指标ID',
  `EJZB` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级指标',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `SJLY` tinyint(4) NULL DEFAULT 2 COMMENT '数据来源',
  `BZTXID` bigint(20) NULL DEFAULT NULL COMMENT '标准体系ID',
  `MBWJKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版文件KEY',
  `MBWJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版文件路径',
  `MBBCKSSJ` datetime(0) NULL DEFAULT NULL COMMENT '模板保存开始时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-模块管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_oo_callback_params
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_oo_callback_params`;
CREATE TABLE `t_dt_jxpj_zpbg_oo_callback_params`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `OO_KEY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一标识',
  `BBGLID` bigint(20) NULL DEFAULT NULL COMMENT '版本管理ID',
  `NEW_BBGLID` bigint(20) NULL DEFAULT NULL COMMENT '新版本管理ID',
  `OPERATE` tinyint(4) NULL DEFAULT NULL COMMENT '操作',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_OO_CALLBACK_PARAMS_K1`(`OO_KEY`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-onlyoffice回调参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_rwfg
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_rwfg`;
CREATE TABLE `t_dt_jxpj_zpbg_rwfg`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `FGLX` tinyint(4) NULL DEFAULT NULL COMMENT '分工类型',
  `DWID` bigint(20) NULL DEFAULT NULL COMMENT '单位ID',
  `DWMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位名称',
  `RYID` bigint(20) NULL DEFAULT NULL COMMENT '人员ID',
  `RYXM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员姓名',
  `CLZT` tinyint(4) NULL DEFAULT 1 COMMENT '处理状态',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_RWFG_K1`(`BGID`, `BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_RWFG_K2`(`FGLX`, `RYID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-任务分工' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_sjbgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_sjbgl`;
CREATE TABLE `t_dt_jxpj_zpbg_sjbgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GLKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联KEY',
  `GLWZ` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联文字',
  `SJLY` tinyint(4) NULL DEFAULT NULL COMMENT '数据来源',
  `SJB` bigint(20) NULL DEFAULT NULL COMMENT '数据表',
  `SJBMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据表名称',
  `SJCLID` bigint(20) NULL DEFAULT NULL COMMENT '数据材料ID',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJBGL_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJBGL_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJBGL_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJBGL_K4`(`GLKEY`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-数据表关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_sjcl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_sjcl`;
CREATE TABLE `t_dt_jxpj_zpbg_sjcl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `CLMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料名称',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  `SSMKMC` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属模块名称',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  `CLDZ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料地址',
  `YYZT` tinyint(4) NULL DEFAULT NULL COMMENT '引用状态',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJCL_K1`(`MKID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-数据材料' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_sjcljgb
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_sjcljgb`;
CREATE TABLE `t_dt_jxpj_zpbg_sjcljgb`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `SJCLID` bigint(20) NULL DEFAULT NULL COMMENT '数据材料ID',
  `FIELD_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名',
  `FIELD_MEANING_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段名',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJCLJGB_K1`(`SJCLID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-数据材料结构表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_sjclssmk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_sjclssmk`;
CREATE TABLE `t_dt_jxpj_zpbg_sjclssmk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `SJCLID` bigint(20) NULL DEFAULT NULL COMMENT '数据材料ID',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLSSMK_K1`(`MKID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-数据材料所属模块' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_sjzbgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_sjzbgl`;
CREATE TABLE `t_dt_jxpj_zpbg_sjzbgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GLKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联KEY',
  `GLWZ` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联文字',
  `SJZBLX` tinyint(4) NULL DEFAULT NULL COMMENT '数据指标类型',
  `ZBMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标名称',
  `CKJG` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参考结果',
  `SJLY` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
  `SJLYMC` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源名称',
  `GJCID` bigint(20) NULL DEFAULT NULL COMMENT '关键词ID',
  `GJCMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键词名称',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJZBGL_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJZBGL_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJZBGL_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SJZBGL_K4`(`GLKEY`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-数据指标关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_syzj
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_syzj`;
CREATE TABLE `t_dt_jxpj_zpbg_syzj`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `ZJID` bigint(20) NULL DEFAULT NULL COMMENT '专家ID',
  `ZJXM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专家姓名',
  `ZJDWID` bigint(20) NULL DEFAULT NULL COMMENT '专家单位ID',
  `ZJDWMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专家单位名称',
  `SYZT` tinyint(4) NULL DEFAULT 1 COMMENT '审阅状态',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SYZJ_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SYZJ_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SYZJ_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_SYZJ_K4`(`ZJID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-审阅专家' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_wzgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_wzgl`;
CREATE TABLE `t_dt_jxpj_zpbg_wzgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `WZBT` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章标题',
  `WZZY` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章摘要',
  `WZNR` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文章内容',
  `FBR` bigint(20) NULL DEFAULT NULL COMMENT '发布人ID',
  `FBRXM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人姓名',
  `FMLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面路径',
  `PDFMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PDF文件名称',
  `PDFLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'PDF文件路径',
  `FJMC` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件名称',
  `FJLJ` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件路径',
  `YSBLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '压缩包路径',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-文章管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_wzlljl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_wzlljl`;
CREATE TABLE `t_dt_jxpj_zpbg_wzlljl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `WZID` bigint(20) NULL DEFAULT NULL COMMENT '文章ID',
  `LLR` bigint(20) NULL DEFAULT NULL COMMENT '浏览人',
  `LLRXM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览人姓名',
  `LLRDWID` bigint(20) NULL DEFAULT NULL COMMENT '浏览人单位ID',
  `LLRDWMC` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览人单位名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-文章浏览记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zbsjly
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zbsjly`;
CREATE TABLE `t_dt_jxpj_zpbg_zbsjly`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `ZBJK` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标接口',
  `ZBMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标名称',
  `SJLY` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
  `SJBMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据表名称',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZBSJLY_K1`(`ZBMC`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-指标数据来源' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zccl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zccl`;
CREATE TABLE `t_dt_jxpj_zpbg_zccl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `CLMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料名称',
  `BCSM` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补充说明',
  `CFWZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存放位置',
  `WJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-支撑材料' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zcclgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zcclgl`;
CREATE TABLE `t_dt_jxpj_zpbg_zcclgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GLKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联KEY',
  `GLWZ` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联文字',
  `ZCCLID` bigint(20) NULL DEFAULT NULL COMMENT '支撑材料ID',
  `CLMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料名称',
  `BCSM` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补充说明',
  `CFWZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存放位置',
  `WJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLGL_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLGL_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLGL_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLGL_K4`(`GLKEY`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLGL_K5`(`ZCCLID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-支撑材料关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zcclssmk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zcclssmk`;
CREATE TABLE `t_dt_jxpj_zpbg_zcclssmk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `ZCCLID` bigint(20) NULL DEFAULT NULL COMMENT '支撑材料ID',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZCCLSSMK_K1`(`MKID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-支撑材料所属模块' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zjbw
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zjbw`;
CREATE TABLE `t_dt_jxpj_zpbg_zjbw`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT 0 COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT 1 COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `NR` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `BGMC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报告名称',
  `ZJID` bigint(20) NULL DEFAULT NULL COMMENT '专家id',
  `ZJMC` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专家名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-专家备忘' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zjpz
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zjpz`;
CREATE TABLE `t_dt_jxpj_zpbg_zjpz`  (
  `ID` bigint(20) NOT NULL,
  `BGMC` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报告名称',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告id',
  `YJZBID` bigint(20) NULL DEFAULT NULL COMMENT '一级指标ID',
  `YJZB` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级指标',
  `EJZB` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级指标',
  `EJZBID` bigint(20) NULL DEFAULT NULL COMMENT '二级指标ID',
  `ZYGZD` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主要关注点',
  `ZYGZDID` bigint(20) NULL DEFAULT NULL COMMENT '主要关注点ID',
  `YYWZ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '引用文字',
  `PZSM` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批注说明',
  `PZLX` int(3) NULL DEFAULT NULL COMMENT '批注类型 0：内容存疑 1：提供资料 2:需要核查',
  `YQXW` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要求行为',
  `ZJMC` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责点长名称',
  `ZJID` bigint(20) NULL DEFAULT NULL COMMENT '负责点长ID',
  `PZRQ` datetime(0) NULL DEFAULT NULL COMMENT '批注日期',
  `PZJG` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果',
  `PZR` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批注人名称',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记 0:未删除, 1:删除',
  `ZT` int(3) NULL DEFAULT NULL COMMENT '状态 0：未处理 1：已催办 2：已处理',
  `PZRID` bigint(20) NULL DEFAULT NULL COMMENT '批注人ID',
  `ZHID` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(6) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `GXRQ` datetime(6) NULL DEFAULT NULL COMMENT '更新日期',
  `CBRQ` datetime(0) NULL DEFAULT NULL COMMENT '催办日期',
  `CLRQ` datetime(0) NULL DEFAULT NULL COMMENT '处理日期',
  `BBGLID` bigint(255) NULL DEFAULT NULL COMMENT '版本管理ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-专家批注' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zjyj
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zjyj`;
CREATE TABLE `t_dt_jxpj_zpbg_zjyj`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GLKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联KEY',
  `GLWZ` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联文字',
  `YJKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意见KEY',
  `GLCLLX` tinyint(4) NULL DEFAULT NULL COMMENT '关联信息类型',
  `GLXXID` bigint(20) NULL DEFAULT NULL COMMENT '关联信息ID',
  `GLXXMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联信息名称',
  `YJQF` tinyint(4) NULL DEFAULT NULL COMMENT '意见区分',
  `CLQF` tinyint(4) NULL DEFAULT NULL COMMENT '材料区分',
  `YJLX` tinyint(4) NULL DEFAULT NULL COMMENT '意见类型',
  `KJBQ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快捷标签',
  `PJNR` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审阅意见',
  `SYZJID` bigint(20) NULL DEFAULT NULL COMMENT '审阅专家ID',
  `SYZJXM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审阅专家姓名',
  `XGZT` tinyint(4) NULL DEFAULT 1 COMMENT '修改状态',
  `XGSM` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改说明',
  `XGRID` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `XGRXM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZJYJ_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZJYJ_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZJYJ_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZJYJ_K4`(`GLKEY`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZJYJ_K5`(`YJKEY`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-专家意见' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zzcl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zzcl`;
CREATE TABLE `t_dt_jxpj_zpbg_zzcl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `CLMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料名称',
  `CMM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重命名',
  `WJH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件号',
  `WJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-佐证材料' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zzclgl
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zzclgl`;
CREATE TABLE `t_dt_jxpj_zpbg_zzclgl`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BGID` bigint(20) NULL DEFAULT NULL COMMENT '报告ID',
  `BGMKID` bigint(20) NULL DEFAULT NULL COMMENT '报告模块ID',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `GLKEY` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联KEY',
  `GLWZ` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联文字',
  `ZZCLID` bigint(20) NULL DEFAULT NULL COMMENT '佐证材料ID',
  `CLMC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料名称',
  `CMM` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重命名',
  `WJH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件号',
  `WJLJ` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLGL_K1`(`BGID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLGL_K2`(`BGMKID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLGL_K3`(`BBID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLGL_K4`(`GLKEY`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLGL_K5`(`ZZCLID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-佐证材料关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_zzclssmk
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_zzclssmk`;
CREATE TABLE `t_dt_jxpj_zpbg_zzclssmk`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `ZZCLID` bigint(20) NULL DEFAULT NULL COMMENT '佐证材料ID',
  `MKID` bigint(20) NULL DEFAULT NULL COMMENT '模块ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `IDX_T_DT_JXPJ_ZPBG_ZZCLSSMK_K1`(`MKID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-佐证材料所属模块' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
