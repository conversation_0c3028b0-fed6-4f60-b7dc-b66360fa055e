package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-文章浏览记录实体类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_WZLLJL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Wzlljl对象", description = "教学评价-自评报告-文章浏览记录")
public class Wzlljl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 文章ID
	*/
	@ExcelProperty("文章ID")
	@ApiModelProperty(value = "文章ID")
	@TableField("WZID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long wzid;

	/**
	* 浏览人
	*/
	@ExcelProperty("浏览人")
	@ApiModelProperty(value = "浏览人")
	@TableField("LLR")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long llr;

	/**
	* 浏览人姓名
	*/
	@ExcelProperty("浏览人姓名")
	@ApiModelProperty(value = "浏览人姓名")
	@TableField("LLRXM")
	private String llrxm;

	/**
	* 浏览人单位ID
	*/
	@ExcelProperty("浏览人单位ID")
	@ApiModelProperty(value = "浏览人单位ID")
	@TableField("LLRDWID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long llrdwid;

	/**
	* 浏览人单位名称
	*/
	@ExcelProperty("浏览人单位名称")
	@ApiModelProperty(value = "浏览人单位名称")
	@TableField("LLRDWMC")
	private String llrdwmc;



}
