package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.impl.ProgressInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 教学评价-自评报告-处理进度信息 控制器
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/progress")
@Api(value = "教学评价-自评报告-处理进度信息", tags = "教学评价-自评报告-处理进度信息接口")
public class ProgressController extends BaseController {

	private com.xpaas.zpbg.service.impl.ProgressProc ProgressProc;

	/**
	 * 详情
	 */
	@PostMapping("/selectProgress")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取进度信息", notes = "传入任务ID")
	@ApiLog("处理进度信息-获取进度信息")
	public R<ProgressInfo> selectProgress( @RequestBody (required = false) ProgressInfo progressInfo) {
		String taskId = null;
		if (progressInfo !=null){
			taskId = progressInfo.getTaskId();
		}
		return R.data(ProgressProc.selectProgress(taskId));
	}
}
