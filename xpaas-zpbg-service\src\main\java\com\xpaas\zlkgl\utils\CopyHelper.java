package com.xpaas.zlkgl.utils;

import com.xpaas.core.tool.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * 复制功能通用工具类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
public class CopyHelper {

    /**
     * 非法文件名字符正则表达式
     */
    private static final Pattern INVALID_NAME_PATTERN = Pattern.compile("[/\\\\:*?\"<>|]");

    /**
     * 最大名称长度
     */
    private static final int MAX_NAME_LENGTH = 255;

    /**
     * 最小名称长度
     */
    private static final int MIN_NAME_LENGTH = 1;

    /**
     * 校验名称是否合法
     *
     * @param name 待校验的名称
     * @return 校验结果，null表示合法，否则返回错误信息
     */
    public String validateName(String name) {
        if (StringUtil.isBlank(name)) {
            return "名称不能为空";
        }

        String trimmedName = name.trim();
        if (trimmedName.length() < MIN_NAME_LENGTH) {
            return "名称不能为空";
        }

        if (trimmedName.length() > MAX_NAME_LENGTH) {
            return "名称长度不能超过" + MAX_NAME_LENGTH + "个字符";
        }

        if (INVALID_NAME_PATTERN.matcher(trimmedName).find()) {
            return "名称不能包含以下字符：/ \\ : * ? \" < > |";
        }

        if (trimmedName.startsWith(".") || trimmedName.endsWith(".")) {
            return "名称不能以点号开头或结尾";
        }

        return null; // 校验通过
    }

    /**
     * 生成唯一名称（处理命名冲突）
     *
     * @param baseName 基础名称
     * @param parentId 父级ID
     * @param nameChecker 名称检查器
     * @return 唯一名称
     */
    public String generateUniqueName(String baseName, String parentId, Function<String, Boolean> nameChecker) {
        String candidateName = baseName + "副本";
        int counter = 1;

        while (nameChecker.apply(candidateName)) {
            candidateName = baseName + "副本(" + counter + ")";
            counter++;
            
            // 防止无限循环
            if (counter > 1000) {
                candidateName = baseName + "副本(" + System.currentTimeMillis() + ")";
                break;
            }
        }

        return candidateName;
    }

    /**
     * 复制实体属性的通用方法
     *
     * @param source 源实体
     * @param target 目标实体
     * @param propertyMapper 属性映射器
     * @param <T> 实体类型
     */
    public <T> void copyProperties(T source, T target, PropertyMapper<T> propertyMapper) {
        propertyMapper.mapProperties(source, target);
    }

    /**
     * 属性映射器接口
     *
     * @param <T> 实体类型
     */
    @FunctionalInterface
    public interface PropertyMapper<T> {
        /**
         * 映射属性
         *
         * @param source 源实体
         * @param target 目标实体
         */
        void mapProperties(T source, T target);
    }

    /**
     * 名称检查器接口
     */
    @FunctionalInterface
    public interface NameChecker {
        /**
         * 检查名称是否存在
         *
         * @param name 名称
         * @param parentId 父级ID
         * @return 是否存在
         */
        boolean exists(String name, String parentId);
    }

    /**
     * 处理复制操作的通用方法
     *
     * @param sourceId 源ID
     * @param targetParentId 目标父级ID
     * @param newName 新名称
     * @param sourceGetter 源实体获取器
     * @param nameValidator 名称校验器
     * @param nameGenerator 名称生成器
     * @param entityCreator 实体创建器
     * @param entitySaver 实体保存器
     * @param <T> 实体类型
     * @return 复制是否成功
     */
    public <T> boolean handleCopy(String sourceId, 
                                  String targetParentId, 
                                  String newName,
                                  Function<String, T> sourceGetter,
                                  Function<String, String> nameValidator,
                                  Function<String, String> nameGenerator,
                                  Function<T, T> entityCreator,
                                  Function<T, Boolean> entitySaver) {
        try {
            // 1. 获取源实体
            T source = sourceGetter.apply(sourceId);
            if (source == null) {
                log.error("源实体不存在，ID: {}", sourceId);
                return false;
            }

            // 2. 处理名称
            String finalName;
            if (StringUtil.isNotBlank(newName)) {
                // 校验自定义名称
                String validationError = nameValidator.apply(newName.trim());
                if (validationError != null) {
                    log.error("名称校验失败: {}", validationError);
                    return false;
                }
                finalName = newName.trim();
            } else {
                // 生成唯一名称
                finalName = nameGenerator.apply(targetParentId);
            }

            // 3. 创建新实体
            T newEntity = entityCreator.apply(source);
            if (newEntity == null) {
                log.error("创建新实体失败");
                return false;
            }

            // 4. 保存新实体
            return entitySaver.apply(newEntity);

        } catch (Exception e) {
            log.error("复制操作失败，sourceId: {}, targetParentId: {}, newName: {}", sourceId, targetParentId, newName, e);
            return false;
        }
    }
}
