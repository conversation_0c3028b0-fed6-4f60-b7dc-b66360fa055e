package com.xpaas.zpbg.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Zcclgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料关联视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZcclglVO对象", description = "教学评价-自评报告-备查材料关联")
public class ZcclglVO extends Zcclgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "关键词名称")
    private String gjcmc;

    @ApiModelProperty(value = "关联KEY字符串")
    private String glkeyStr;

    @ApiModelProperty(value = "关联KEY列表")
    private List<String> glkeyList;

    @ApiModelProperty(value = "多条记录")
    private List<Zcclgl> selectList;

    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ssmkid;
    @ApiModelProperty(value = "序号")
    private Integer xh;


    @ApiModelProperty(value = "批次")
    private String pc;
}
