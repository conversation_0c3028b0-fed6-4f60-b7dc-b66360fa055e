package com.xpaas.zlkgl.dto;

import lombok.Data;

/**
 * 复制结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CopyResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 新创建的ID
     */
    private String newId;

    /**
     * 新名称
     */
    private String newName;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 复制的文件数量
     */
    private int copiedFileCount;

    /**
     * 复制的文件夹数量
     */
    private int copiedFolderCount;

    public static CopyResult success(String newId, String newName) {
        CopyResult result = new CopyResult();
        result.setSuccess(true);
        result.setNewId(newId);
        result.setNewName(newName);
        result.setCopiedFileCount(0);
        result.setCopiedFolderCount(0);
        return result;
    }

    public static CopyResult success(String newId, String newName, int fileCount, int folderCount) {
        CopyResult result = new CopyResult();
        result.setSuccess(true);
        result.setNewId(newId);
        result.setNewName(newName);
        result.setCopiedFileCount(fileCount);
        result.setCopiedFolderCount(folderCount);
        return result;
    }

    public static CopyResult failure(String errorMessage) {
        CopyResult result = new CopyResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
