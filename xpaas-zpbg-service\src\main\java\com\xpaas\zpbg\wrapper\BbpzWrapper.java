package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bbpz;
import com.xpaas.zpbg.vo.BbpzVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-版本批注包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Component
public class BbpzWrapper extends BaseEntityWrapper<Bbpz, BbpzVO>  {


	/**
	* 将entity转换成 entityVO
	 * <AUTHOR>
	 * @since 2024-09-02
    * @return 转换后的entityVO对象
    */
	@Override
	public BbpzVO entityVO(Bbpz bbpz) {
		BbpzVO bbpzVO = Objects.requireNonNull(BeanUtil.copy(bbpz, BbpzVO.class));
		//User cjr = UserCache.getUser(bbpz.getCjr());
		//if (cjr != null){
		//	bbpzVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbpz.getGxr());
		//if (gxr != null){
		//	bbpzVO.setGxrName(gxr.getName());
		//}
/**  **/
		return bbpzVO;
	}





    @Override
    public BbpzVO wrapperVO(BbpzVO bbpzVO) {
		//User cjr = UserCache.getUser(bbpzVO.getCjr());
		//if (cjr != null){
		//	bbpzVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbpzVO.getGxr());
		//if (gxr != null){
		//	bbpzVO.setGxrName(gxr.getName());
		//}
/**  */
        return bbpzVO;
    }

}
