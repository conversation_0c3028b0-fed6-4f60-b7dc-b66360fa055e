package com.xpaas.zpbg.dto;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/09/26 14:14
 **/
public class TreeBuilder<T> {
    /**
     * 构建前端所需要树结构
     *
     * @param basCostClassList
     * @return 树结构列表
     */
    public List<T> make(List<T> basCostClassList) {
        List<T> returnList = new ArrayList<T>();
        List<String> tempList = new ArrayList<String>();

        Class clazz = null;
        Method getId = null;
        Method getParentId = null;
        Method setChildren = null;

        try {
            for (T t : basCostClassList) {
                if (clazz == null) {
                    clazz = t.getClass();
                }
                if (getId == null) {
                    getId = clazz.getDeclaredMethod("getId");
                }
                if (getParentId == null) {
                    getParentId = clazz.getDeclaredMethod("getParentId");
                }
                if (setChildren == null) {
                    setChildren = clazz.getDeclaredMethod("setChildren", new Class[]{List.class});
                }

                if (getId == null || getParentId == null || setChildren == null) {
                    return returnList;
                }

                tempList.add(String.valueOf(getId.invoke(t)));
            }
            for (T t : basCostClassList) {
                // 如果是顶级节点, 遍历该父节点的所有子节点
                if (!tempList.contains(getParentId.invoke(t))) {
                    recursionFn(basCostClassList, t, setChildren, getParentId, getId);
                    returnList.add(t);
                }
            }

        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        if (returnList.isEmpty()) {
            returnList = basCostClassList;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<T> list, T t, Method setChildren, Method getParentId, Method getId) throws InvocationTargetException, IllegalAccessException {
        // 得到子节点列表
        List<T> childList = getChildList(list, t, getParentId, getId);
        setChildren.invoke(t, childList);
        for (T tChild : childList) {
            if (hasChild(list, tChild, getParentId, getId)) {
                recursionFn(list, tChild, setChildren, getParentId, getId);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<T> getChildList(List<T> list, T t, Method getParentId, Method getId) throws InvocationTargetException, IllegalAccessException {
        List<T> tlist = new ArrayList<T>();
        Iterator<T> it = list.iterator();
        while (it.hasNext()) {
            T n = (T) it.next();
            if (getParentId.invoke(n) != null && getParentId.invoke(n).equals(getId.invoke(t)) ) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<T> list, T t, Method getParentId, Method getId) throws InvocationTargetException, IllegalAccessException {
        return getChildList(list, t, getParentId, getId).size() > 0 ? true : false;
    }
}
