package com.xpaas.zpbg.feign.fallback;

import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Demo;
import com.xpaas.zpbg.feign.ILotteryClient;

import java.util.List;

/**
 * @program: xpaas-lottery
 * @description: ILotteryClientFallback
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-12-26 08:41
 */
public class ILotteryClientFallback implements ILotteryClient {


    @Override
    public R<Demo> getCqjg(List<Long> ids) {
        return R.fail("调用失败");
    }
}
