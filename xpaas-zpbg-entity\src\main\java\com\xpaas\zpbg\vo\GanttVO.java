package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Gantt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

;

/**
 * 自评报告-首页甘特图视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GanttVO对象", description = "自评报告-首页-甘特图")
public class GanttVO extends Gantt {
	private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "任务状态（其它)")
//    private List<String> queryDqzt;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	/**
	 * 左侧开始日期
	 */
	@ApiModelProperty(value = "左侧开始日期")
	private String __leftDate;

	private Integer gridWidth;

	private Boolean showUnitLine;

	private String unitLineRange;

	private Boolean showItemLine;

	private Boolean allowCreateBar;

	private Boolean allowLinkBars;

	private Boolean allowUnitWidthResizing;

	private Boolean allowSectionResizing;

	private TimescaleVO timescale;

	private List<NodeVO> nodes;
}
