package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Zjyj;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告-专家意见视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZjyjVO对象", description = "教学评价-自评报告-专家意见")
public class ZjyjVO extends Zjyj {
    @JsonSerialize(using = ToStringSerializer.class)
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;

    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "关联KEY字符串")
    private String glkeyStr;

    @ApiModelProperty(value = "关联KEY列表")
    private List<String> glkeyList;
}
