package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-佐证材料关联实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_ZZCLGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zzclgl对象", description = "教学评价-自评报告-佐证材料关联")
public class Zzclgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 关联KEY
	*/
	@ExcelProperty("关联KEY")
	@ApiModelProperty(value = "关联KEY")
	@TableField("GLKEY")
	private String glkey;

	/**
	 * 关联文字
	 */
	@ExcelProperty("关联文字")
	@ApiModelProperty(value = "关联文字")
	@TableField("GLWZ")
	private String glwz;

	/**
	* 佐证材料ID
	*/
	@ExcelProperty("佐证材料ID")
	@ApiModelProperty(value = "佐证材料ID")
	@TableField("ZZCLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long zzclid;

	/**
	* 材料名称
	*/
	@ExcelProperty("材料名称")
	@ApiModelProperty(value = "材料名称")
	@TableField("CLMC")
	private String clmc;

	/**
	* 重命名
	*/
	@ExcelProperty("重命名")
	@ApiModelProperty(value = "重命名")
	@TableField("CMM")
	private String cmm;

	/**
	* 文件号
	*/
	@ExcelProperty("文件号")
	@ApiModelProperty(value = "文件号")
	@TableField("WJH")
	private String wjh;

	/**
	* 文件路径
	*/
	@ExcelProperty("文件路径")
	@ApiModelProperty(value = "文件路径")
	@TableField("WJLJ")
	private String wjlj;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	 * 存放位置
	 */
	@ExcelProperty("存放位置")
	@ApiModelProperty(value = "存放位置")
	@TableField("CFWZ")
	private String cfwz;


}
