package com.xpaas.zpbg.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-状态管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Repository
public interface ZtglMapper {

    /**
     * 加锁报告记录
     */
    int lockBg(@Param("bgId") Long bgId);

    /**
     * 获取活跃报告ID
     */
    List<Long> selectBgidList();

    /**
     * 获取报告当前进度管理ID
     */
    Long selectCurJdglid(@Param("bgId") Long bgid);

    /**
     * 更新报告进度管理ID
     */
    int updateBgJdglid(@Param("bgId") Long bgid, @Param("jdglid") Long jdglid);

    /**
     * 更新报告各模块主线版本进度管理ID
     */
    int updateBbJdglid(@Param("bgId") Long bgid, @Param("jdglid") Long jdglid);

    /**
     * 获取进度记录
     */
    List<Map<String, Object>> selectZtjl(@Param("bgid") Long bgid, @Param("bbid") Long bbid);

    /**
     * 各类消息发送信息 - 撰写开始
     */
    List<Map<String,Object>> selectMessageZxks();
    /**
     * 各类消息发送信息 - 撰写快结束
     */
    List<Map<String,Object>> selectMessageZxjs();
    /**
     * 各类消息发送信息 - 撰写催提交
     */
    List<Map<String,Object>> selectMessageZxtj();
    /**
     * 各类消息发送信息 - 撰写延期
     */
    List<Map<String,Object>> selectMessageZxtjGly();

    /**
     * 根据专家意见数检查是否可定稿
     */
    int selectZjyjCnt(@Param("bbid") Long bbid);
}
