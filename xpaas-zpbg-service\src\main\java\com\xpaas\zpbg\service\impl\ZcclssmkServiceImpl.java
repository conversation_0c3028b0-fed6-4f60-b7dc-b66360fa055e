package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zcclssmk;
import com.xpaas.zpbg.mapper.ZcclssmkMapper;
import com.xpaas.zpbg.service.IZcclssmkService;
import com.xpaas.zpbg.vo.ZcclssmkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-备查材料所属模块 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class ZcclssmkServiceImpl extends BaseServiceImpl<ZcclssmkMapper, Zcclssmk> implements IZcclssmkService {

	@Override
	public IPage<ZcclssmkVO> selectZcclssmkPage(IPage<ZcclssmkVO> page, ZcclssmkVO zcclssmk) {
		return page.setRecords(baseMapper.selectZcclssmkPage(page, zcclssmk));
	}

}
