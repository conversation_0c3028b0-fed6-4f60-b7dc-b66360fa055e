package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zygzdgl;
import com.xpaas.zpbg.mapper.CfbztxMapper;
import com.xpaas.zpbg.mapper.ZygzdglMapper;
import com.xpaas.zpbg.service.IBgmkService;
import com.xpaas.zpbg.service.ICfbztxService;
import com.xpaas.zpbg.service.IMkglService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * 刷数据相关接口
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@Service
public class CfbztxServiceImpl implements ICfbztxService {

	@Resource
	private IMkglService mkglService;

	@Resource
	private IBgmkService bgmkService;

	@Resource
	private CfbztxMapper baseMapper;
	@Autowired
	private ZygzdglMapper zygzdglMapper;

	/*
	 * 模块管理刷新数据
	 *
	 * @author: luzhaojun
	 * @time: 2025-03-24 17:03:43
	 */
	@Override
	public R mkglSxsj() {
		//日志打印
		List<String> logList = new LinkedList<>();
		List<Mkgl> mkglList = mkglService.list();
		for (Mkgl mkgl : mkglList) {
			if (mkgl.getMklx()!=null &&mkgl.getMklx()!=1){
				//修改记录
				List<String> updateLog = new LinkedList<>();
				//更新一级指标
				if(mkgl.getYjzbid()!=null){
					Zygzdgl zygzdgl = selectNewZbtxId(mkgl.getYjzbid(), mkgl.getMklx());
					if(zygzdgl!=null){
						updateLog.add(String.format("%s原一级指标id为%s,修改为%s",mkgl.getYjzb(),mkgl.getYjzbid(),zygzdgl.getId()));
						baseMapper.updateTable(mkgl.getId(),"T_DT_JXPJ_ZPBG_MKGL","YJZBID="+zygzdgl.getId());
					}
				}
				//更新二级指标
				if(mkgl.getEjzbid()!=null){
					Zygzdgl zygzdgl = selectNewZbtxId(mkgl.getEjzbid(), mkgl.getMklx());
					if(zygzdgl!=null){
						updateLog.add(String.format("%s原二级指标id为%s,修改为%s",mkgl.getEjzb(),mkgl.getEjzbid(),zygzdgl.getId()));
						baseMapper.updateTable(mkgl.getId(),"T_DT_JXPJ_ZPBG_MKGL","EJZBID="+zygzdgl.getId());
					}
				}
				if (updateLog.size()>0){
					logList.add(String.join(",", updateLog));
				}
			}
		}
		return R.fail(200,String.join("\\r\\n",logList));
	}

	/**
	 * 查询新的指标体系id
	 * @return
	 */
	private Zygzdgl selectNewZbtxId(Long oldId,Integer newZbly){
		Zygzdgl oldZygzdgl = zygzdglMapper.selectById(oldId);
		if(oldZygzdgl!=null){
			return zygzdglMapper.selectOne(new LambdaQueryWrapper<Zygzdgl>().eq(Zygzdgl::getZbmc, oldZygzdgl.getZbmc()).eq(Zygzdgl::getZbly, newZbly).last("limit 1"));
		}
		return null;
	}

	/*
	 * 报告管理刷新数据
	 *
	 * @author: luzhaojun
	 * @time: 2025-03-24 17:03:43
	 */
	@Override
	public R bgglSxsj() {
		//日志打印
		List<String> logList = new LinkedList<>();
		List<Bgmk> bgmkList = bgmkService.list();
		for (Bgmk bgmk : bgmkList) {
			if (bgmk.getMklx()!=null && bgmk.getMklx()!=1){
				//修改记录
				List<String> updateLog = new LinkedList<>();
				//更新一级指标
				if(bgmk.getYjzbid()!=null){
					Zygzdgl zygzdgl = selectNewZbtxId(bgmk.getYjzbid(), bgmk.getMklx());
					if(zygzdgl!=null){
						updateLog.add(String.format("%s原一级指标id为%s,修改为%s",bgmk.getYjzb(),bgmk.getYjzbid(),zygzdgl.getId()));
						baseMapper.updateTable(bgmk.getId(),"T_DT_JXPJ_ZPBG_BGMK","YJZBID="+zygzdgl.getId());
					}
				}
				//更新二级指标
				if(bgmk.getEjzbid()!=null){
					Zygzdgl zygzdgl = selectNewZbtxId(bgmk.getEjzbid(), bgmk.getMklx());
					if(zygzdgl!=null){
						updateLog.add(String.format("%s原二级指标id为%s,修改为%s",bgmk.getEjzb(),bgmk.getEjzbid(),zygzdgl.getId()));
						baseMapper.updateTable(bgmk.getId(),"T_DT_JXPJ_ZPBG_BGMK","EJZBID="+zygzdgl.getId());
					}
				}
				if (updateLog.size()>0){
					logList.add(String.join(",", updateLog));
				}
			}
		}
		return R.fail(200,String.join("\\r\\n",logList));
	}
}
