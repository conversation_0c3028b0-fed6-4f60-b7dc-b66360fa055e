package com.xpaas.zpbg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 教学评价-自评报告-专家解读实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@ApiModel(value = "ZjjdVO对象", description = "教学评价-自评报告-专家解读实体类")
public class ZjjdVO {
	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模块类型")
    private String mklx;

    @ApiModelProperty(value = "名称")
    private String label;

    @ApiModelProperty(value = "数值")
    private String value;

    @ApiModelProperty(value = "级别")
    private String jb;

    @ApiModelProperty(value = "上级ID")
    private String sjid;

    @ApiModelProperty(value = "树形结构-子节点")
    private List<ZjjdVO> children;

    @ApiModelProperty(value = "是否有子节点")
    private boolean hasChildren;

}
