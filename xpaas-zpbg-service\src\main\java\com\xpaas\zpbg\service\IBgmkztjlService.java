package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.vo.BgmkztjlVO;

/**
 * 教学评价-自评报告-报告模块状态记录 服务类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
public interface IBgmkztjlService extends BaseService<Bgmkztjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmkztjl
	 * @return
	 */
	IPage<BgmkztjlVO> selectBgmkztjlPage(IPage<BgmkztjlVO> page, BgmkztjlVO bgmkztjl);

}
