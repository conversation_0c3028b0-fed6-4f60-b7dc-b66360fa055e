
package com.xpaas.zpbg.client;
import com.xpaas.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign接口类
 *
 */
@FeignClient(

		value = "xpaas-zjjy"
)
public interface IZjjyClient {

	String API_PREFIX = "/clientZjjy";
	String DELETE_BY_BGID = API_PREFIX + "/deleteByBgid";


	//根据报告ID删除同步的报告
	@GetMapping(DELETE_BY_BGID)
	R deleteByBgid(@RequestParam("bgid")Long bgid);

}
