<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.WzglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wzglResultMap" type="com.xpaas.zpbg.entity.Wzgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="WZBT" property="wzbt"/>
        <result column="WZZY" property="wzzy"/>
        <result column="WZNR" property="wznr"/>
        <result column="FBR" property="fbr"/>
        <result column="FBRXM" property="fbrxm"/>
        <result column="FMLJ" property="fmlj"/>
        <result column="PDFMC" property="pdfmc"/>
        <result column="PDFLJ" property="pdflj"/>
        <result column="FJMC" property="fjmc"/>
        <result column="FJLJ" property="fjlj"/>
        <result column="YSBLJ" property="ysblj"/>
        <result column="PX" property="px"/>
        <result column="ZSWZ" property="zswz"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="wzglResultMapVO" type="com.xpaas.zpbg.vo.WzglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="WZBT" property="wzbt"/>
        <result column="WZZY" property="wzzy"/>
        <result column="WZNR" property="wznr"/>
        <result column="FBR" property="fbr"/>
        <result column="FBRXM" property="fbrxm"/>
        <result column="FMLJ" property="fmlj"/>
        <result column="PDFMC" property="pdfmc"/>
        <result column="PDFLJ" property="pdflj"/>
        <result column="FJMC" property="fjmc"/>
        <result column="FJLJ" property="fjlj"/>
        <result column="YSBLJ" property="ysblj"/>
        <result column="PX" property="px"/>
        <result column="ZSWZ" property="zswz"/>

        <result column="SJLY" property="sjly"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectWzPage" resultMap="wzglResultMapVO">
        select
            wz.*,
            count(distinct lljl.ID) LLRS,
            2 as SJLY
        from
            t_dt_jxpj_zpbg_wzgl wz
        left join
            t_dt_jxpj_zpbg_wzlljl lljl
                on lljl.WZID = wz.ID
        where
            wz.scbj = 0
            <if test="wzgl.wzbt != null and wzgl.wzbt != ''">
                and wz.WZBT like concat('%',#{wzgl.wzbt},'%')
            </if>
            <if test="wzgl.zswz != null and wzgl.zswz != ''">
                and wz.ZSWZ = #{wzgl.zswz}
            </if>
        group by
            wz.ID

        <if test="wzgl.zswz == null">
            order by
                wz.PX, wz.ID DESC
        </if>

        <if test="wzgl.zswz != null and wzgl.zswz != ''">
            order by
                wz.ID DESC
        </if>
    </select>

    <!--取得慕课列表-->
    <select id="getKcList" resultType="com.xpaas.zpbg.vo.WzglVO">
        SELECT
            ID,
            KCMC AS WZBT,
            KCFM AS FMLJ,
            1 AS SJLY
        FROM
            t_dt_jxpj_zjmk_kc
        WHERE
            SCBJ = 0
            AND TJ = 1
        ORDER BY
            PX,
            ID DESC
    </select>

    <!--取得浏览人数-->
    <select id="getLlrs" resultType="int">
        SELECT
            count( 1 )
        FROM
            t_dt_jxpj_zpbg_wzlljl
        WHERE
            WZID = #{id}
            AND SCBJ = 0
    </select>
</mapper>
