package com.xpaas.zpbg.entity;

import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 评建跟踪辅助报告 目录实体类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Object对象", description = "评建跟踪辅助报告")
public class WordMenu extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 目录名
	*/
	@ApiModelProperty(value = "目录名")
	private String name;

	/**
	* 目录级别
	*/
	@ApiModelProperty(value = "目录级别")
	private Integer level;

	/**
	* 目录页码
	*/
	@ApiModelProperty(value = "目录页码")
	private Integer page;
}
