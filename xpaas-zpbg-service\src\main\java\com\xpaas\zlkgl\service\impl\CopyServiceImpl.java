package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.dto.CopyRequest;
import com.xpaas.zlkgl.dto.CopyResult;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.service.ICopyService;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 复制服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CopyServiceImpl implements ICopyService {

    private final IZlglService zlglService;
    private final IFlglWjjService flglWjjService;
    private final IWxzlWjjService wxzlWjjService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CopyResult copy(CopyRequest request) {
        try {
            switch (request.getCopyType()) {
                case FILE:
                    return copyFile(request.getSourceId(), request.getTargetParentId(), request.getNewName());
                case FOLDER_FLGL:
                    return copyFlglFolder(request.getSourceId(), request.getTargetParentId(), request.getNewName());
                case FOLDER_WXZL:
                    return copyWxzlFolder(request.getSourceId(), request.getTargetParentId(), request.getNewName());
                default:
                    return CopyResult.failure("不支持的复制类型");
            }
        } catch (Exception e) {
            log.error("复制操作失败", e);
            return CopyResult.failure("复制操作失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CopyResult copyFile(String sourceId, String targetParentId, String newName) {
        // 1. 查询源文件
        Zlgl sourceFile = zlglService.getById(sourceId);
        if (sourceFile == null) {
            return CopyResult.failure("源文件不存在");
        }

        // 2. 生成新名称
        String finalName = StringUtil.isBlank(newName) ? 
            generateUniqueName(sourceFile.getZlMc(), targetParentId, CopyRequest.CopyType.FILE) : newName;

        // 3. 创建新文件记录
        Zlgl newFile = new Zlgl();
        copyFileProperties(sourceFile, newFile);
        newFile.setId(null);
        newFile.setWjjId(targetParentId);
        newFile.setZlMc(finalName);
        newFile.setZlCmm(finalName);

        // 4. 保存新文件
        boolean saved = zlglService.save(newFile);
        if (!saved) {
            return CopyResult.failure("保存文件失败");
        }

        return CopyResult.success(String.valueOf(newFile.getId()), finalName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CopyResult copyFlglFolder(String sourceId, String targetParentId, String newName) {
        // 1. 查询源文件夹
        FlglWjj sourceFolder = flglWjjService.getById(sourceId);
        if (sourceFolder == null) {
            return CopyResult.failure("源文件夹不存在");
        }

        // 2. 生成新名称
        String finalName = StringUtil.isBlank(newName) ? 
            generateUniqueName(sourceFolder.getWjjMc(), targetParentId, CopyRequest.CopyType.FOLDER_FLGL) : newName;

        // 3. 创建新文件夹记录
        FlglWjj newFolder = new FlglWjj();
        copyFlglFolderProperties(sourceFolder, newFolder);
        newFolder.setId(null);
        newFolder.setFjWjjId(targetParentId);
        newFolder.setWjjMc(finalName);

        // 4. 保存新文件夹
        boolean saved = flglWjjService.save(newFolder);
        if (!saved) {
            return CopyResult.failure("保存文件夹失败");
        }

        int copiedFileCount = 0;
        int copiedFolderCount = 1;

        // 5. 如果是普通文件夹(wjjLx=1)，递归复制子项
        if ("1".equals(sourceFolder.getWjjLx())) {
            CopyResult subResult = copyFolderContents(sourceId, String.valueOf(newFolder.getId()), CopyRequest.CopyType.FOLDER_FLGL);
            copiedFileCount += subResult.getCopiedFileCount();
            copiedFolderCount += subResult.getCopiedFolderCount();
        }

        return CopyResult.success(String.valueOf(newFolder.getId()), finalName, copiedFileCount, copiedFolderCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CopyResult copyWxzlFolder(String sourceId, String targetParentId, String newName) {
        // 1. 查询源文件夹
        WxzlWjj sourceFolder = wxzlWjjService.getById(sourceId);
        if (sourceFolder == null) {
            return CopyResult.failure("源文件夹不存在");
        }

        // 2. 生成新名称
        String finalName = StringUtil.isBlank(newName) ? 
            generateUniqueName(sourceFolder.getWjjMc(), targetParentId, CopyRequest.CopyType.FOLDER_WXZL) : newName;

        // 3. 创建新文件夹记录
        WxzlWjj newFolder = new WxzlWjj();
        copyWxzlFolderProperties(sourceFolder, newFolder);
        newFolder.setId(null);
        newFolder.setFjWjjId(targetParentId);
        newFolder.setWjjMc(finalName);

        // 4. 保存新文件夹
        boolean saved = wxzlWjjService.save(newFolder);
        if (!saved) {
            return CopyResult.failure("保存文件夹失败");
        }

        int copiedFileCount = 0;
        int copiedFolderCount = 1;

        // 5. 如果是普通文件夹(wjjLx=1)，递归复制子项
        if ("1".equals(sourceFolder.getWjjLx())) {
            CopyResult subResult = copyFolderContents(sourceId, String.valueOf(newFolder.getId()), CopyRequest.CopyType.FOLDER_WXZL);
            copiedFileCount += subResult.getCopiedFileCount();
            copiedFolderCount += subResult.getCopiedFolderCount();
        }

        return CopyResult.success(String.valueOf(newFolder.getId()), finalName, copiedFileCount, copiedFolderCount);
    }

    @Override
    public String generateUniqueName(String baseName, String parentId, CopyRequest.CopyType copyType) {
        String candidateName = baseName + "副本";
        int counter = 1;

        while (isNameExists(candidateName, parentId, copyType)) {
            candidateName = baseName + "副本(" + counter + ")";
            counter++;
        }

        return candidateName;
    }

    /**
     * 复制文件夹内容（子文件夹和文件）
     */
    private CopyResult copyFolderContents(String sourceFolderId, String targetFolderId, CopyRequest.CopyType folderType) {
        int totalFileCount = 0;
        int totalFolderCount = 0;

        try {
            // 1. 复制子文件夹
            if (folderType == CopyRequest.CopyType.FOLDER_FLGL) {
                List<FlglWjj> subFolders = flglWjjService.list(
                    new LambdaQueryWrapper<FlglWjj>().eq(FlglWjj::getFjWjjId, sourceFolderId)
                );
                for (FlglWjj subFolder : subFolders) {
                    CopyResult result = copyFlglFolder(String.valueOf(subFolder.getId()), targetFolderId, null);
                    if (result.isSuccess()) {
                        totalFileCount += result.getCopiedFileCount();
                        totalFolderCount += result.getCopiedFolderCount();
                    }
                }
            } else if (folderType == CopyRequest.CopyType.FOLDER_WXZL) {
                List<WxzlWjj> subFolders = wxzlWjjService.list(
                    new LambdaQueryWrapper<WxzlWjj>().eq(WxzlWjj::getFjWjjId, sourceFolderId)
                );
                for (WxzlWjj subFolder : subFolders) {
                    CopyResult result = copyWxzlFolder(String.valueOf(subFolder.getId()), targetFolderId, null);
                    if (result.isSuccess()) {
                        totalFileCount += result.getCopiedFileCount();
                        totalFolderCount += result.getCopiedFolderCount();
                    }
                }
            }

            // 2. 复制文件
            List<Zlgl> files = zlglService.list(
                new LambdaQueryWrapper<Zlgl>().eq(Zlgl::getWjjId, sourceFolderId)
            );
            for (Zlgl file : files) {
                CopyResult result = copyFile(String.valueOf(file.getId()), targetFolderId, null);
                if (result.isSuccess()) {
                    totalFileCount++;
                }
            }

        } catch (Exception e) {
            log.error("复制文件夹内容失败", e);
            throw new ServiceException("复制文件夹内容失败：" + e.getMessage());
        }

        return CopyResult.success(null, null, totalFileCount, totalFolderCount);
    }

    /**
     * 检查名称是否已存在
     */
    private boolean isNameExists(String name, String parentId, CopyRequest.CopyType copyType) {
        switch (copyType) {
            case FILE:
                return zlglService.count(
                    new LambdaQueryWrapper<Zlgl>()
                        .eq(Zlgl::getWjjId, parentId)
                        .eq(Zlgl::getZlMc, name)
                ) > 0;
            case FOLDER_FLGL:
                return flglWjjService.count(
                    new LambdaQueryWrapper<FlglWjj>()
                        .eq(FlglWjj::getFjWjjId, parentId)
                        .eq(FlglWjj::getWjjMc, name)
                ) > 0;
            case FOLDER_WXZL:
                return wxzlWjjService.count(
                    new LambdaQueryWrapper<WxzlWjj>()
                        .eq(WxzlWjj::getFjWjjId, parentId)
                        .eq(WxzlWjj::getWjjMc, name)
                ) > 0;
            default:
                return false;
        }
    }

    /**
     * 复制文件属性
     */
    private void copyFileProperties(Zlgl source, Zlgl target) {
        target.setZlMc(source.getZlMc());
        target.setZlCmm(source.getZlCmm());
        target.setZlPx(source.getZlPx());
        target.setZlDz(source.getZlDz());
        target.setZlMj(source.getZlMj());
        target.setWxzlLx(source.getWxzlLx());
        target.setPjLx(source.getPjLx());
    }

    /**
     * 复制普通文件夹属性
     */
    private void copyFlglFolderProperties(FlglWjj source, FlglWjj target) {
        target.setJdId(source.getJdId());
        target.setFlglLb(source.getFlglLb());
        target.setPjLx(source.getPjLx());
        target.setWjjMc(source.getWjjMc());
        target.setWjjLx(source.getWjjLx());
        target.setWbljDz(source.getWbljDz());
        target.setWjjPx(source.getWjjPx());
    }

    /**
     * 复制外校文件夹属性
     */
    private void copyWxzlFolderProperties(WxzlWjj source, WxzlWjj target) {
        target.setPjLx(source.getPjLx());
        target.setWjjMc(source.getWjjMc());
        target.setWjjLx(source.getWjjLx());
        target.setWbljDz(source.getWbljDz());
        target.setWjjPx(source.getWjjPx());
    }
}
