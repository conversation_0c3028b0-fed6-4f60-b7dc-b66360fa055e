package com.xpaas.zpbg.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-任务人员数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RwryDTO extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "报告id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	@ApiModelProperty(value = "报告名称")
	private String bgmc;

	@ApiModelProperty(value = "报告模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	@ApiModelProperty(value = "报告年度")
	private String nd;

	@ApiModelProperty(value = "开始时间")
	private Date kssj;

	@ApiModelProperty(value = "模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;

	@ApiModelProperty(value = "模块名称")
	private String mkmc;

	@ApiModelProperty(value = "分工类型")
	private Integer fglx;

	@ApiModelProperty(value = "单位ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long dwid;

	@ApiModelProperty(value = "单位名称")
	private String dwmc;

	@ApiModelProperty(value = "人员ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long ryid;

	@ApiModelProperty(value = "人员姓名")
	private String ryxm;

	@ApiModelProperty(value = "排序")
	private Integer px;

}
