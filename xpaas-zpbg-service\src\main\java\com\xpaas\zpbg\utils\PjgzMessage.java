package com.xpaas.zpbg.utils;

import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.msg.entity.Message;
import com.xpaas.msg.feign.IMsgCenterClient;
import com.xpaas.zpbg.vo.XxfbUserVO;

import java.util.Date;
import java.util.List;
import java.util.UUID;

public class PjgzMessage {

    public static void sendMsgYwid(IMsgCenterClient msgCenterClient, String title, String content, List<XxfbUserVO> userList, int type, String typeStr, Long ywid) {
        for (int i = 0; i < userList.size(); i++) {
            Message msg = getDefaultMessage();
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String userId = userList.get(i).getUserId();
//            int level = userList.get(i).getLevel();
            // 目前系统只有一种跳转
            int level = 1;

            // 接收者id
            msg.setUserId(Long.parseLong(userId));

            // 标题
            msg.setTitle(title);

            // 通知类型
            msg.setType("自评报告通知" + typeStr);

            // 事件内容
            msg.setContent(content);

            if (type != 22 && type != 99 && ywid != null) {
                // 路径类型(1:子系统，2:外链系统 3:内嵌系统 4:只显示消息)
                msg.setUrlType(1);

                if (level == 1) {
                    // 路径参数
                    //msg.setParam("{\"pjjsrw/pjjsrw\":\"sub-zdgz\"}");

                    // 跳转子应用业务主键
                    msg.setBusinessKey("sub-zpbg-zxzpbg");
                }

                // 业务id
                msg.setYwid(ywid);
            } else {
                // 路径类型(1:子系统，2:外链系统 3:内嵌系统 4:只显示消息)
                msg.setUrlType(4);
            }

            // 唯一标识,用于更新状态
            msg.setUniqueKey(uuid);
            // 通知事件id
            msg.setCid(uuid);

            // 定时任务时
//            if(type == 3) {
//                // 租户ID
//                msg.setZhid("000000");
//            }

            msgCenterClient.sendUser(msg);
        }
    }

    private static Message getDefaultMessage() {
        LoginUser user = AuthUtil.getUser();
        Long userId = new Long(0);
        if (user != null) {
            userId = user.getUserId();
        }

        Message msg = new Message();

        // 需要定义的数据--------------------------------------
        // 接收者id
        msg.setUserId(null);

        // 标题
        msg.setTitle(null);

        // 事件内容
        msg.setContent(null);

        // 路径类型(1:子系统，2:外链系统 3:内嵌系统 4:只显示消息)
        msg.setUrlType(null);

        // 路径参数
        msg.setParam(null);

        // 跳转子应用业务主键
        msg.setBusinessKey(null);

        // 唯一标识,用于更新状态
        msg.setUniqueKey(null);

        // 回调地址
        msg.setCallBackUrl(null);

        // 默认定义的数据--------------------------------------
        // 发送者id
        msg.setFromuserId(userId);

        // 操作时间
        msg.setCtime(new Date());

        // 类型 todo:代办 notity:通知
        msg.setType("自评报告通知");

        // 留言
        msg.setRemark(null);

        // 是否完成 1:完成或已读
        msg.setIsdone(0);

        // 完成时间
        msg.setDtime(null);

        // 路径
        msg.setUrl(null);

        // 通知事件id
        msg.setCid(null);

        // 项目名称
        msg.setName(null);

        // 标星 1:标星 0:未标星 2:催办(待办中使用)
        msg.setStar(1);

        // 分类：1：消息，2：我的待办，3：我的发起，4：我的已办 5：我的已办
        msg.setCategory("5");

        // 来源，1：我方系统 2:第三方系统
        msg.setMsgSource("1");

        // 菜单ID
        msg.setMenuId(null);

        // 是否重要 0：不重要 ，1：重要
        msg.setIsImportant(1);

        if (user != null) {
            msg.setCjr(user.getUserId());
            msg.setGxr(user.getUserId());
            msg.setCjdw(Long.parseLong(user.getDeptId()));
        } else {
            msg.setCjr((long) 0);
            msg.setGxr((long) 0);
            msg.setCjdw((long) 0);
        }

        return msg;
    }
}
