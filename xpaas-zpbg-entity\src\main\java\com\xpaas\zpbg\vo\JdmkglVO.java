package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Jdmkgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-进度模块关联表视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "JdmkglVO对象", description = "教学评价-自评报告-进度模块关联表")
public class JdmkglVO extends Jdmkgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "报告模块ID数组")
    private String[] bgmkidList;

    @ApiModelProperty(value = "开始时间")
    private String kssj;

}
