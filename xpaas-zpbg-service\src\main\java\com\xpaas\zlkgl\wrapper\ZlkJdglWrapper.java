package com.xpaas.zlkgl.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zlkgl.entity.ZlkJdgl;
import com.xpaas.zlkgl.vo.ZlkJdglVO;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * 教学评价-资料库平台-节点管理表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Component
public class ZlkJdglWrapper extends BaseEntityWrapper<ZlkJdgl, ZlkJdglVO>  {


	/**
	* 将entity转换成 entityVO
	 * <AUTHOR>
	 * @since 2025-07-24
    * @return 转换后的entityVO对象
    */
	@Override
	public ZlkJdglVO entityVO(ZlkJdgl jdgl) {
		ZlkJdglVO jdglVO = Objects.requireNonNull(BeanUtil.copy(jdgl, ZlkJdglVO.class));
		//User cjr = UserCache.getUser(jdgl.getCjr());
		//if (cjr != null){
		//	jdglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdgl.getGxr());
		//if (gxr != null){
		//	jdglVO.setGxrName(gxr.getName());
		//}
/**  **/
		return jdglVO;
	}





    @Override
    public ZlkJdglVO wrapperVO(ZlkJdglVO jdglVO) {
		//User cjr = UserCache.getUser(jdglVO.getCjr());
		//if (cjr != null){
		//	jdglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdglVO.getGxr());
		//if (gxr != null){
		//	jdglVO.setGxrName(gxr.getName());
		//}
/**  */
        return jdglVO;
    }

}
