package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.mapper.JdglMapper;
import com.xpaas.zpbg.service.IJdglService;
import com.xpaas.zpbg.vo.JdglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-进度管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class JdglServiceImpl extends BaseServiceImpl<JdglMapper, Jdgl> implements IJdglService {

    /**
     * 进度查询
     */
    @Override
    public IPage<JdglVO> selectJdglPage(IPage<JdglVO> page, JdglVO jdgl) {
        return page.setRecords(baseMapper.selectJdglPage(page, jdgl));
    }

    /**
     * 报告信息取得
     */
    @Override
    public IPage<JdglVO> getHeaderInfo(IPage<JdglVO> page, JdglVO jdgl) {
        return page.setRecords(baseMapper.getHeaderInfo(page, jdgl));
    }

    /**
     * 报告模块取得
     */
    @Override
    public List<JdglVO> getBgmkInfo(JdglVO jdgl) {
        return baseMapper.getBgmkInfo(jdgl);
    }

    /**
     * 进度模块关联取得
     */
    @Override
    public List<JdglVO> getJdmkglInfo(JdglVO jdgl) {
        return baseMapper.getJdmkglInfo(jdgl);
    }

    /**
     * 进度模块关联取得
     */
    @Override
    public int checkExistByInfo(JdglVO jdgl) {
        int count = baseMapper.checkExistByInfo(jdgl);
        if (count > 0) {
            throw new ServiceException("进度名称已存在！");
        }
        return count;
    }

//    @Override
//    public List<Jdgl> selectJdglByBggl(List<Long> bgidList) {
//        return baseMapper.selectJdglByBggl(bgidList);
//    }
}
