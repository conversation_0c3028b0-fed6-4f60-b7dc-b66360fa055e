<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZjjdclMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zjjdclResultMap" type="com.xpaas.zpbg.entity.Zjjdcl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="SCR" property="scr"/>
        <result column="ZJ" property="zj"/>
        <result column="CLLX" property="cllx"/>
        <result column="SCRQ" property="scrq"/>
        <result column="SFYXXZ" property="sfyxxz"/>
        <result column="ZD" property="zd"/>
        <result column="ZDSJ" property="zdsj"/>
        <result column="SCFJ" property="scfj"/>
        <result column="PARENTID" property="parentid"/>
        <result column="FLAG" property="flag"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zjjdclResultMapVO" type="com.xpaas.zpbg.vo.ZjjdclVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="SCR" property="scr"/>
        <result column="ZJ" property="zj"/>
        <result column="CLLX" property="cllx"/>
        <result column="SCRQ" property="scrq"/>
        <result column="SFYXXZ" property="sfyxxz"/>
        <result column="ZD" property="zd"/>
        <result column="ZDSJ" property="zdsj"/>
        <result column="SCFJ" property="scfj"/>
        <result column="PARENTID" property="parentid"/>
        <result column="FLAG" property="flag"/>
    </resultMap>

    <select id="list1" resultMap="zjjdclResultMap">
        select t.* from
            (
            SELECT
            CAST( zjjdcl.ID AS CHAR ) AS ID,
            zjjdcl.ZHID,
            zjjdcl.CJR,
            zjjdcl.CJBM,
            zjjdcl.CJDW,
            zjjdcl.CJRQ,
            zjjdcl.GXR,
            zjjdcl.GXRQ,
            zjjdcl.SCBJ,
            zjjdcl.ZT,
            zjjdcl.CLMC,
            zjjdcl.SCR,
            zjjdcl.ZJ,
            zjjdcl.CLLX,
            zjjdcl.SCRQ,
            zjjdcl.SFYXXZ,
            zjjdcl.ZD,
            zjjdcl.ZDSJ,
            zjjdcl.SCFJ,
            zjjdcl.PARENTID,
            1 AS FLAG
        FROM
            T_DT_JXPJ_ZSKGL_BZTXGL_ZJJDCL zjjdcl
        WHERE
            zjjdcl.scbj = 0
            <if test="zjjdcl.parentid != null and zjjdcl.parentid !=''">
                AND zjjdcl.parentid = #{zjjdcl.parentid}
            </if>

        UNION

        SELECT
            CAST( ztwz.ID AS CHAR ) AS ID,
            ztwz.ZHID,
            ztwz.CJR,
            ztwz.CJBM,
            ztwz.CJDW,
            ztwz.CJRQ,
            ztwz.GXR,
            ztwz.GXRQ,
            ztwz.SCBJ,
            ztwz.ZT,
            ztwz.WZBT AS CLMC,
            ztwz.FBR AS SCR,
            '学习专题管理' AS ZJ,
            '文件材料' AS CLLX,
            ztwz.GXRQ AS SCRQ,
            0 AS SFYXXZ,
            ztwz.ZD,
            ztwz.ZDSJ,
            ztwz.YSBLJ AS SCFJ,
            null AS PARENTID,
            2 AS FLAG
        FROM
            t_dt_jxpj_zskgl_xxztgl_ztwz ztwz
        LEFT JOIN t_dt_jxpj_zskgl_bztxgl bztxgl ON ztwz.ZYGZDIDS LIKE concat( '%', bztxgl.YJZBID, '%' )
            AND ztwz.ZYGZDIDS LIKE concat( '%', bztxgl.EJZBID, '%' )
            AND ztwz.ZYGZDIDS LIKE concat( '%', bztxgl.ZYGZDID, '%' )
        WHERE
            ztwz.SCBJ = 0
            AND ztwz.ZLFL = '专家解读材料'
            AND bztxgl.SCBJ = 0
            <if test="zjjdcl.parentid != null and zjjdcl.parentid !=''">
                AND bztxgl.id = #{zjjdcl.parentid}
            </if>

        UNION

        SELECT
            CAST( wz.ID AS CHAR ) AS ID,
            wz.ZHID,
            wz.CJR,
            wz.CJBM,
            wz.CJDW,
            wz.CJRQ,
            wz.GXR,
            wz.GXRQ,
            wz.SCBJ,
            wz.ZT,
            wz.WZBT AS CLMC,
            wz.FBR AS SCR,
            '文章管理' AS ZJ,
            '文件材料' AS CLLX,
            wz.GXRQ AS SCRQ,
            0 AS SFYXXZ,
            wz.ZD,
            wz.ZDSJ,
            wz.YSBLJ AS SCFJ,
            null AS PARENTID,
            3 AS FLAG
        FROM
            t_dt_jxpj_zskgl_xxztgl_wz wz
        LEFT JOIN t_dt_jxpj_zskgl_bztxgl bztxgl ON wz.ZYGZDIDS LIKE concat( '%', bztxgl.YJZBID, '%' )
            AND wz.ZYGZDIDS LIKE concat( '%', bztxgl.EJZBID, '%' )
            AND wz.ZYGZDIDS LIKE concat( '%', bztxgl.ZYGZDID, '%' )
        WHERE
            wz.SCBJ = 0
            AND wz.ZLFL = '专家解读材料'
            AND bztxgl.SCBJ = 0
            <if test="zjjdcl.parentid != null and zjjdcl.parentid !=''">
                AND bztxgl.id = #{zjjdcl.parentid}
            </if>
            ) t
        order by
            t.ZD asc,
            (case when t.ZD = 0 then t.ZDSJ else t.SCRQ end) desc
    </select>

</mapper>
