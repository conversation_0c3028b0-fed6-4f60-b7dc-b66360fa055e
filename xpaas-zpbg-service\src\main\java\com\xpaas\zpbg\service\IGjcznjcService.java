package com.xpaas.zpbg.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Gjcznjc;
import com.xpaas.zpbg.vo.GjcznjcVO;

import java.util.List;

/**
 * 教学评价-自评报告-关键词智能检测 服务类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
public interface IGjcznjcService extends BaseService<Gjcznjc> {

	/**
	 * 智能检测-查询
	 *
	 * @param gjcznjc
	 * @return
	 */
	List<GjcznjcVO> selectGjcznjc(GjcznjcVO gjcznjc);

	/**
	 * 智能检测-删除
	 *
	 * @param gjcznjc
	 * @return
	 */
	boolean removeGjcznjc(Gjcznjc gjcznjc);

}
