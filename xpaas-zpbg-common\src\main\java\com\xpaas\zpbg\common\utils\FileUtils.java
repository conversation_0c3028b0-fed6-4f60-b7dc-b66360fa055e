package com.xpaas.zpbg.common.utils;

import cn.afterturn.easypoi.word.WordExportUtil;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

public class FileUtils {

    /**
     * EasyPoi 替换数据 导出 word
     * @param templatePath word模板地址
     * @param tempDir      临时文件存放地址
     * @param filename     文件名称
     * @param data         替换参数
     * @param request
     * @param response
     */
    public static void easyPoiExport(String templatePath, String tempDir, String filename, Map<String, Object> data, HttpServletRequest request, HttpServletResponse response) {
        Assert.notNull(templatePath, "模板路径不能为空");
        Assert.notNull(tempDir, "临时文件路径不能为空");
        Assert.notNull(filename, "文件名称不能为空");
        Assert.isTrue(filename.endsWith(".docx"), "文件名称仅支持docx格式");

        if (!tempDir.endsWith("/")) {
            tempDir = tempDir + File.separator;
        }

        File file = new File(tempDir);
        if (!file.exists()) {
            file.mkdirs();
        }

        try {
            String userAgent = request.getHeader("user-agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
                filename = URLEncoder.encode(filename, "UTF-8");
            } else {
                filename = new String(filename.getBytes("utf-8"), "ISO-8859-1");
            }

            XWPFDocument document = WordExportUtil.exportWord07(templatePath, data);
//                List<XWPFTable> tables = document.getTables();
//
//
//            for (int i=0;i<tables.size();i++){
//                if (tables.get(i).getRow(i) == null || tables.get(i).getRow(i).getCell(0) == null){
//                    continue;
//                }
//                XWPFTableCell cell=tables.get(i).getRow(i).getCell(0);
//                int endRowNum = 0;
//                for (int j = i + 1; j< tables.size(); j++ ){
//                    XWPFTableCell desCloumn=tables.get(i).getRow(i).getCell(i);
//                    XWPFTableCell nextDesCloumn=tables.get(j).getRow(j).getCell(j);
//                    if (!desCloumn.getBodyElements().equals(nextDesCloumn.getBodyElements())){
//                        break;
//                    }
//                    endRowNum++;
//                }
//            }
//
//                for (XWPFTable table : tables) {
//                    FileUtils.mergeCellsVertically(table,0,9,10);
//                    FileUtils.mergeCellsVertically(table,0,11,12);
//                }
            String tempPath = tempDir + filename;
            FileOutputStream out = new FileOutputStream(tempPath);
            document.write(out);

            // 设置响应规则
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            response.setCharacterEncoding("UTF-8");
            OutputStream stream = response.getOutputStream();
            document.write(stream);
            stream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            deleteTempFile(tempDir, filename);
        }
    }

    public static void easyPoiExportDykcxx(String templatePath, String tempDir, String filename, Map<String, Object> data, HttpServletRequest request, HttpServletResponse response,int row,int dcjlNum,int bcjlNum) {
        Assert.notNull(templatePath, "模板路径不能为空");
        Assert.notNull(tempDir, "临时文件路径不能为空");
        Assert.notNull(filename, "文件名称不能为空");
        Assert.isTrue(filename.endsWith(".docx"), "文件名称仅支持docx格式");

        if (!tempDir.endsWith("/")) {
            tempDir = tempDir + File.separator;
        }

        File file = new File(tempDir);
        if (!file.exists()) {
            file.mkdirs();
        }

        try {
            String userAgent = request.getHeader("user-agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
                filename = URLEncoder.encode(filename, "UTF-8");
            } else {
                filename = new String(filename.getBytes("utf-8"), "ISO-8859-1");
            }

            XWPFDocument document = WordExportUtil.exportWord07(templatePath, data);
            List<XWPFTable> tables = document.getTables();
            for (XWPFTable table : tables) {

                FileUtils.mergeCellsVertically(table,0,row,row+dcjlNum);
                FileUtils.mergeCellsVertically(table,0,row+dcjlNum+1,row+dcjlNum+1+bcjlNum);
            }
            String tempPath = tempDir + filename;
            FileOutputStream out = new FileOutputStream(tempPath);
            document.write(out);

            // 设置响应规则
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            OutputStream stream = response.getOutputStream();
            document.write(stream);
            stream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            deleteTempFile(tempDir, filename);
        }
    }
    public static void easyPoiExportHb(String templatePath, String tempDir, String filename, Map<String, Object> data, HttpServletRequest request, HttpServletResponse response,List<Map<String,Object>> hbList) {
        Assert.notNull(templatePath, "模板路径不能为空");
        Assert.notNull(tempDir, "临时文件路径不能为空");
        Assert.notNull(filename, "文件名称不能为空");
        Assert.isTrue(filename.endsWith(".docx"), "文件名称仅支持docx格式");

        if (!tempDir.endsWith("/")) {
            tempDir = tempDir + File.separator;
        }

        File file = new File(tempDir);
        if (!file.exists()) {
            file.mkdirs();
        }

        try {
            String userAgent = request.getHeader("user-agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
                filename = URLEncoder.encode(filename, "UTF-8");
            } else {
                filename = new String(filename.getBytes("utf-8"), "ISO-8859-1");
            }

            XWPFDocument document = WordExportUtil.exportWord07(templatePath, data);
            List<XWPFTable> tables = document.getTables();
            int index = 0;
            for (XWPFTable table : tables) {
                for(Map<String,Object> map : hbList) {
                    int startCol = Integer.parseInt(map.get("startCol").toString());
                    int endCol = Integer.parseInt(map.get("endCol").toString());
                    int startRow = Integer.parseInt(map.get("startRow").toString());
                    int endRow = Integer.parseInt(map.get("endRow").toString());
                    int tnum = Integer.parseInt(map.get("table").toString());
                    if(index == tnum){
                        for(int i=0;i<=endCol-startCol;i++){
                            FileUtils.mergeCellsVertically(table, startCol+i, startRow, endRow);
                        }
                        for(int i=0;i<=endRow-startRow;i++){
                            FileUtils.mergeCellsHorizontal(table, startRow+i, startCol, endCol);
                        }
                    }
                }
                index++;
            }
            String tempPath = tempDir + filename;
            FileOutputStream out = new FileOutputStream(tempPath);
            document.write(out);

            // 设置响应规则
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            OutputStream stream = response.getOutputStream();
            document.write(stream);
            stream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            deleteTempFile(tempDir, filename);
        }
    }
    /**
     * 删除临时生成的文件
     */
    public static void deleteTempFile(String filePath, String fileName) {
        File file = new File(filePath + fileName);
        File f = new File(filePath);
        file.delete();
        f.delete();
    }

    /**
     *   word跨列合并单元格
     *   table 表单对象
     *   row  合并行
     *   fromCell 起始列
     *   toCell  结束列
     */
    public static void mergeCellsHorizontal(XWPFTable table, Integer row, Integer fromCell, Integer toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
            if ( cellIndex == fromCell ) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     *   word跨行合并单元格
     *   table 表单对象
     *   row  合并列
     *   fromCell 起始行
     *   toCell  结束行
     */
    public static void mergeCellsVertically(XWPFTable table, Integer col, Integer fromRow, Integer toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if ( rowIndex == fromRow ) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 合并列
     * @param table
     * @param rowNumber 需要合并的列在的行
     * @param fromCol 开始列
     * @param toCol 结束列
     */
    public static void mergeColumns(XWPFTable table, int rowNumber, int fromCol, int toCol) {
        XWPFTableRow row = table.getRow(rowNumber);
        for(int index = fromCol; index < toCol; index++){
            CTHMerge hMerge = CTHMerge.Factory.newInstance();
            if(index == fromCol){
                hMerge.setVal(STMerge.RESTART);
            } else {
                hMerge.setVal(STMerge.CONTINUE);
            }
            XWPFTableCell cell = row.getCell(index);
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr != null) {
                tcPr.setHMerge(hMerge);
            } else {
                tcPr = CTTcPr.Factory.newInstance();
                tcPr.setHMerge(hMerge);
                cell.getCTTc().setTcPr(tcPr);
            }
        }
    }

    /**
     * 合并行
     * @param table 当前表
     * @param col 需要合并的行
     * @param fromRow 开始行
     * @param toRow 结束行
     * */
    public static void  mergeRow(XWPFTable table, int col, int fromRow, int toRow){
        //index 为 行索引
        for (int index = fromRow; index <= toRow ; index++) {
            // 重新创建类加载---类似于new()
            CTVMerge merge = CTVMerge.Factory.newInstance();
            if(index == fromRow){
                merge.setVal(STMerge.RESTART);
            }else{
                merge.setVal(STMerge.CONTINUE);
            }
            XWPFTableCell tableCell = table.getRow(index).getCell(col);
            CTTcPr tcPr = tableCell.getCTTc().getTcPr();
            if(tcPr != null){
                tcPr.setVMerge(merge);
            }else{
                tcPr = CTTcPr.Factory.newInstance();
                tcPr.setVMerge(merge);
                tableCell.getCTTc().setTcPr(tcPr);
            }
        }
    }
}
