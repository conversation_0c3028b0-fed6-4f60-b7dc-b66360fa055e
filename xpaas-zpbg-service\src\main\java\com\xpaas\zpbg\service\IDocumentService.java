package com.xpaas.zpbg.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Demt;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自评报告-文档 服务类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
public interface IDocumentService extends BaseService<Demt> {

    /**
     * 取得word格式的模版
     *
     * @param demt
     * @return
     */
    void exportMbWord(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 取得word文档的URL
     *
     * @param demt
     * @return
     */
    String previewMbWord(Demt demt) throws Exception;

    /**
     * 取得word格式的报告
     *
     * @param demt
     * @return
     */
    void exportBgWord(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 取得pdf格式的报告
     *
     * @param demt
     * @return
     */
    void exportBgPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 取得pdf格式的模块
     *
     * @param demt
     * @return
     */
    void exportMkPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 取得word文档的URL
     *
     * @param demt
     * @return
     */
    String previewBgPdf(Demt demt) throws Exception;
}
