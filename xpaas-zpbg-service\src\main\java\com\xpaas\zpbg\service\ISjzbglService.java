package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Sjzbgl;
import com.xpaas.zpbg.vo.SjclVO;
import com.xpaas.zpbg.vo.SjzbglVO;

import java.util.List;

/**
 * 教学评价-自评报告-数据指标关联 服务类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface ISjzbglService extends BaseService<Sjzbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjzbgl
	 * @return
	 */
	IPage<SjzbglVO> selectSjzbglPage(IPage<SjzbglVO> page, SjzbglVO sjzbgl);

	/**
	 * 不同的内容引用了相同的数据表
	 *
	 * @param sjzbglVO
	 * @return
	 */
	String sameGl(SjzbglVO sjzbglVO);

	/**
	 * 新增
	 *
	 * @param sjzbglVO
	 * @return
	 */
	boolean save(SjzbglVO sjzbglVO);

	/**
	 * 取得关键词
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> searchGjc(SjzbglVO sjzbglVO);

	/**
	 * 校验关键词是否关联
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<String> checkGlkey(SjzbglVO sjzbglVO);

	/**
	 * 取得关联信息
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> getGlAllList(SjzbglVO sjzbglVO);

	/**
	 * 查询数据材料列表
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjclVO> searchSjlc(SjzbglVO sjzbglVO);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	void deleteGl(String ids);
}
