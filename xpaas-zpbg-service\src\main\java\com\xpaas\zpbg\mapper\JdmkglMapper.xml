<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.JdmkglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="jdmkglResultMap" type="com.xpaas.zpbg.entity.Jdmkgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BGMKID" property="bgmkid"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="jdmkglResultMapVO" type="com.xpaas.zpbg.vo.JdmkglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BGMKID" property="bgmkid"/>
    </resultMap>

    <!-- 数据查询 -->
    <select id="selectJdmkglPage" resultMap="jdmkglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_JDMKGL where scbj = 0
    </select>

    <!-- 删除当前进度关联的模块 -->
    <delete id="jdmkglDelete">
        delete from T_DT_JXPJ_ZPBG_JDMKGL where jdglid = #{jdglid}
    </delete>

    <!-- 消息发送信息 -->
    <select id="selectMessage" resultType="map">
        SELECT
        	bbgl.id AS bbid,
        	rwfg.ryid AS userId
        FROM
        	t_dt_jxpj_zpbg_bggl bggl
        	INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON jdgl.bgid = bggl.id
        	AND jdgl.scbj = 0
        	INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.jdglid = jdgl.id
        	AND jdmkgl.scbj = 0
        	INNER JOIN t_dt_jxpj_zpbg_bgmk bgmk ON bgmk.id = jdmkgl.bgmkid
        	AND bgmk.scbj = 0
        	INNER JOIN t_dt_jxpj_zpbg_rwfg rwfg ON rwfg.bgmkid = bgmk.id
        	AND rwfg.scbj = 0
        	INNER JOIN t_dt_jxpj_zpbg_bbgl bbgl ON bbgl.bgmkid = bgmk.id
        	AND bbgl.bblx = 1
        	AND bbgl.scbj = 0
        WHERE
        	bggl.scbj = 0
        	AND jdgl.id = #{jdglid}
        GROUP BY
        	bbgl.id,
        	rwfg.ryid
        ORDER BY
        	bbgl.id
    </select>

</mapper>
