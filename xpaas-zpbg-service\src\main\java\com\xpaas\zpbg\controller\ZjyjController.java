package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.dto.ZjyjExcelDTO;
import com.xpaas.zpbg.entity.Zjyj;
import com.xpaas.zpbg.service.IZjyjService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.ZjyjVO;
import com.xpaas.zpbg.wrapper.ZjyjWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
/**
 * 教学评价-自评报告-专家意见 控制器
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zjyj")
@Api(value = "教学评价-自评报告-专家意见", tags = "教学评价-自评报告-专家意见接口")
public class ZjyjController extends BaseController {

	@Autowired
	private ResourceLoader resourceLoader;
	private ZjyjWrapper zjyjWrapper;
	private IZjyjService zjyjService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入zjyj")
	@ApiLog("专家意见-详情")
	public R<ZjyjVO> detail(Zjyj zjyj) {
		Zjyj detail = zjyjService.getOne(Condition.getQueryWrapper(zjyj));
		return R.data(zjyjWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-专家意见 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("专家意见-列表")
	public R<IPage<ZjyjVO>> list(Zjyj zjyj, Query query) {
		IPage<Zjyj> pages = zjyjService.page(Condition.getPage(query), Condition.getQueryWrapper(zjyj));
		return R.data(zjyjWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-专家意见
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("专家意见-列表")
    public R<IPage<ZjyjVO>> page(ZjyjVO zjyj, Query query) {
		LoginUser user = AuthUtil.getUser();
		zjyj.setSyzjid(user.getUserId());
        IPage<ZjyjVO> pages = zjyjService.selectZjyjPage(Condition.getPage(query), zjyj);
        return R.data(zjyjWrapper.wrapperPageVO(pages));
    }

	/**
	 * 新增 教学评价-自评报告-专家意见
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入zjyj")
	@ApiLog("专家意见-新增")
	public R save(@Valid @RequestBody ZjyjVO zjyjVO) {
		LoginUser user = AuthUtil.getUser();
		zjyjVO.setSyzjid(user.getUserId());
		zjyjVO.setSyzjxm(user.getUserName());
		boolean b = zjyjService.save(zjyjVO);
		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-专家意见
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入zjyj")
	@ApiLog("专家意见-修改")
	public R update(@Valid @RequestBody ZjyjVO zjyjVO) {
		LoginUser user = AuthUtil.getUser();
		zjyjVO.setSyzjid(user.getUserId());
		zjyjVO.setSyzjxm(user.getUserName());
		boolean b = zjyjService.updateById(zjyjVO);
		return R.status(b);
	}


	/**
	 * 新增或修改 教学评价-自评报告-专家意见 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入zjyj")
	@ApiLog("专家意见-新增或修改")
	public R submit(@Valid @RequestBody Zjyj zjyj) {
		return R.status(zjyjService.saveOrUpdate(zjyj));
	}

	
	/**
	 * 删除 教学评价-自评报告-专家意见
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("专家意见-逻辑删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = zjyjService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("专家意见-高级查询")
	public R<IPage<ZjyjVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Zjyj> queryWrapper = Condition.getQueryWrapper(map, Zjyj.class);
		IPage<Zjyj> pages = zjyjService.page(Condition.getPage(query), queryWrapper);

		return R.data(zjyjWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("专家意见-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Zjyj> queryWrapper = Condition.getQueryWrapper(map, Zjyj.class);
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<ZjyjExcelDTO> list = zjyjService.getzjyjExcel( map);

		export(response, fileName, list );
	}
	public  void export(HttpServletResponse response, String fileName,  List<ZjyjExcelDTO> dataList) {
		try {
			Collections.sort(dataList, new Comparator<ZjyjExcelDTO>() {
				@Override
				public int compare(ZjyjExcelDTO p1, ZjyjExcelDTO p2) {
					// 升序排序
					return Integer.compare(Integer.parseInt(p1.getYjqf()), Integer.parseInt(p2.getYjqf()));
					// 如果需要降序排序，则交换p1和p2的位置
					// return Integer.compare(p2.getAge(), p1.getAge());
				}
			});
			// 1:缺少数据, 2:缺少佐证, 3:缺少备查, 4:专家意见, 5:文字修改, 6:数据引用, 7:数据表, 8:佐证材料, 9:备查材料, 10:专家整体性意见
			for(int i=0;i<dataList.size();i++){
				switch (dataList.get(i).getYjlx()){
					case "0":{
						dataList.get(i).setYjlxcopy("--");
					break;
					}
					case "1":{
						dataList.get(i).setYjlxcopy("缺少数据");
						break;
					}
					case "2":{
						dataList.get(i).setYjlxcopy("缺少佐证");
						break;
					}
					case "3":{
						dataList.get(i).setYjlxcopy("缺少备查");
						break;
					}
					case "4":{
						dataList.get(i).setYjlxcopy("专家意见");
						break;
					}
					case "5":{
						dataList.get(i).setYjlxcopy("建议修改");
						break;
					}
					case "6":{
						dataList.get(i).setYjlxcopy("数据引用");
						break;
					}
					case "7":{
						dataList.get(i).setYjlxcopy("数据表");
						break;
					}
					case "8":{
						dataList.get(i).setYjlxcopy("佐证材料");
						break;
					}
					case "9":{
						dataList.get(i).setYjlxcopy("备查材料");
						break;
					}
					case "10":{
						dataList.get(i).setYjlxcopy("专家整体性意见");
						break;
					}

				}
				//1:审阅报告整体意见, 2:审阅报告内容意见, 3:审阅材料意见
				switch (dataList.get(i).getYjqf()){
					case "0":{
						dataList.get(i).setYjqf("--");
						break;
					}
					case "1":{
						dataList.get(i).setYjqf("审阅报告整体意见");
						break;
					}
					case "2":{
						dataList.get(i).setYjqf("审阅报告内容意见");
						break;
					}
					case "3":{
						dataList.get(i).setYjqf("审阅材料意见");
						dataList.get(i).setYjlxcopy(dataList.get(i).getClmc());
						break;
					}
				}
			}
			Collections.sort(dataList, Comparator.comparing(ZjyjExcelDTO::getYjlxcopy).thenComparing(ZjyjExcelDTO::getSyzjxm));
			String path = "classpath:doc/zjyj.xlsx";
			org.springframework.core.io.Resource resource = resourceLoader.getResource(path);
			InputStream inStream= resource.getInputStream();
			XSSFWorkbook wb = new XSSFWorkbook(inStream);
			response.reset();
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(Charsets.UTF_8.name());
			fileName = URLEncoder.encode(fileName, Charsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

			writeData(wb, dataList);

			if (dataList.size() !=0) {
				Sheet sheet = wb.getSheetAt(0);
				int width = (int) (20 * 256 * (1 + (1 / 4)));
				for (int i = 0; i < 8; i++) {
					sheet.setColumnWidth(0, width);
				}
				int currentRowIndex = 3;
				int firstRow=2; //开始位置
				while (currentRowIndex < sheet.getLastRowNum()) {
					Row row = sheet.getRow(firstRow);
					if (row != null) {
						Cell cell = row.getCell(6);
						if (cell!=null&&!cell.getStringCellValue().equals("") && currentRowIndex > 0) {
							int lastRow= firstRow+1;
							String firstalue = cell.getStringCellValue();
							int index =1;
							while (true){
								Row prevRow = sheet.getRow(lastRow);
								if(prevRow!= null){
									Cell prevCell = prevRow.getCell(6);
									if(prevCell!=null&& !prevCell.getStringCellValue().equals("")) {
										if (firstalue.equals(prevCell.getStringCellValue())) {
											lastRow++;
											index++;
										} else {
											break;
										}
									}else{
										break;
									}
								}else{
									break;
								}
							}
							if (index >1) {
								sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow-1 , 6, 6));
							}
							firstRow = lastRow ;
							currentRowIndex=firstRow;
						}else{
							break;
						}
					}else{
						break;
					}
				}
			}
			wb.write(response.getOutputStream());
			wb.close();

		} catch (IOException e) {
			e.printStackTrace();
		}

	}
	private void writeData(XSSFWorkbook wb, List<ZjyjExcelDTO> list) {
		XSSFSheet sheet = wb.getSheetAt(0);
		int rowIndex = 2;

		Row row = null;
		if(list != null) {
			for(int i = 0; i < list.size(); i++) {
				int colIndex = 0;
				// 插入行
				row = sheet.createRow(rowIndex);
				// 一级指标
				setCellValue(wb, row, list.get(i).getYjzb(), colIndex);
				colIndex++;

				// 二级指标
				setCellValue(wb, row, list.get(i).getEjzb(), colIndex);
				colIndex++;

				// 主要关注点
				setCellValue(wb, row, list.get(i).getZygzd(), colIndex);
				colIndex++;

				// 责任人/点长
				setCellValue(wb, row, list.get(i).getZxrordz(), colIndex);
				colIndex++;

				// 评价专家
				setCellValue(wb, row, list.get(i).getSyzjxm(), colIndex);
				colIndex++;

				// 审阅时间
				setCellValue(wb, row, list.get(i).getSytime(), colIndex);
				colIndex++;

				// 分类
				setCellValue(wb, row, list.get(i).getYjqf(), colIndex);
				colIndex++;

				// 意见类型
				setCellValue(wb, row, list.get(i).getYjlxcopy(), colIndex);
				colIndex++;

				// 审阅意见
				setCellValue(wb, row, list.get(i).getPjnr(), colIndex);
				colIndex++;
				rowIndex++;
			}
		}
	}

	private void setCellValue(XSSFWorkbook wb, Row row, Object str, int col) {
		Font titleFont = wb.createFont();
		titleFont.setFontName("微软雅黑");
		titleFont.setFontHeightInPoints((short) 12);
		titleFont.setColor(IndexedColors.BLACK.index);
		XSSFCellStyle titleStyle = wb.createCellStyle();
		titleStyle.setWrapText(true);
		titleStyle.setAlignment(HorizontalAlignment.CENTER);
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		titleStyle.setFont(titleFont);

		byte[] rgb = new byte[]{(byte) 0, (byte) 0, (byte) 0};
		setBorder(titleStyle, BorderStyle.THIN, new XSSFColor(rgb ,null));
		Cell cell = row.createCell(col);
		if(str == null) {
			cell.setCellValue("");
		} else {
			cell.setCellValue(String.valueOf(str));
		}

		cell.setCellStyle(titleStyle);
	}

	private void setBorder(XSSFCellStyle style, BorderStyle border, XSSFColor color) {
		style.setBorderTop(border);
		style.setBorderLeft(border);
		style.setBorderRight(border);
		style.setBorderBottom(border);
		style.setBorderColor(XSSFCellBorder.BorderSide.TOP, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, color);
		style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, color);
	}
	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("专家意见-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Zjyj> list = ExcelUtil.read(file, Zjyj.class);
		//TODO 此处需要根据具体业务添加代码
		zjyjService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("专家意见-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Zjyj> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Zjyj> list = zjyjService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Zjyj导入模板", "Zjyj导入模板",columnFiledNames, list, Zjyj.class);
	}

	/**
	 * 自定义分页 教学评价-自评报告-专家意见
	 */
	@GetMapping("/zjpage")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("专家意见-全部专家查询")
	public R<IPage<ZjyjVO>> zjpage(ZjyjVO zjyj, Query query) {
		IPage<ZjyjVO> pages = zjyjService.selectZxrPage(Condition.getPage(query), zjyj);
		return R.data(zjyjWrapper.wrapperPageVO(pages));
	}

	/**
	 * 修改 教学评价-自评报告-专家意见
	 */
	@PostMapping("/updatelist")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "修改", notes = "传入zjyj")
	@ApiLog("专家意见-多条修改")
	public R updatelist(@Valid @RequestBody List<Zjyj> zjyj) {
		LoginUser user = AuthUtil.getUser();
		Long id = user.getUserId();
		String name = user.getUserName();
		for (int i = 0; i < zjyj.size(); i++) {
			zjyj.get(i).setXgrid(id);
			zjyj.get(i).setXgrxm(name);
		}
		boolean b= zjyjService.updateBatchById(zjyj);
		return R.status(b);
	}
	/**
	 * 查询 教学评价-自评报告-专家意见
	 */
	@GetMapping("/otherzjlist")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("专家意见-查询当前专家以外的意见")
	public R<IPage<ZjyjVO>> otherzjlist(ZjyjVO zjyj, Query query) {
		LoginUser user = AuthUtil.getUser();
		zjyj.setSyzjid(user.getUserId());
		IPage<ZjyjVO> pages = zjyjService.selectOtherzjPage(Condition.getPage(query), zjyj);
		return R.data(zjyjWrapper.wrapperPageVO(pages));
	}
	/**
	 * 查询 教学评价-自评报告-专家意见
	 */
	@GetMapping("/getzjyjcount")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("查询批注意见个数")
	public R getZjyjCount(ZjyjVO zjyjvo,String isThereSyzj){
		if("1".equals(isThereSyzj)){
			LoginUser user = AuthUtil.getUser();
			zjyjvo.setSyzjid(user.getUserId());
		}
		if(zjyjvo.getGlkeyStr()!=null){
			zjyjvo.setGlkeyList(Arrays.asList(zjyjvo.getGlkeyStr().split(",")));
		}
		int flag = zjyjService.getZjyjCount(zjyjvo);
		return R.data(flag);
	}

	@GetMapping("/getyjChecking")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("验证报告中是否有未修改意见")
	public R getyjChecking(String bgid, String bbid,String bgmkid){
		int flag = zjyjService.getyjChecking( bgid, bbid, bgmkid);
		return R.data(flag);
	}

	@GetMapping("/getLsyj")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "分页", notes = "传入zjyj")
	@ApiLog("获取历史意见")
	public R getLsyj(ZjyjVO zjyjvo){
		LoginUser user = AuthUtil.getUser();
		zjyjvo.setSyzjid(user.getUserId());
		List<Zjyj> flag = zjyjService.getLsyj(zjyjvo);
		return R.data(flag);
	}

}
