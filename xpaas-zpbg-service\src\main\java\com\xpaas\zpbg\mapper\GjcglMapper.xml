<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.GjcglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcglResultMap" type="com.xpaas.zpbg.entity.Gjcgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="GJCLX" property="gjclx"/>
        <result column="GLFW" property="glfw"/>
        <result column="PC" property="pc"/>
        <result column="ZBLY" property="zbly"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="gjcglResultMapVO" type="com.xpaas.zpbg.vo.GjcglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="GJCLX" property="gjclx"/>
        <result column="GLFW" property="glfw"/>
        <result column="PC" property="pc"/>
        <result column="ZBLY" property="zbly"/>
    </resultMap>

    <!--自定义分页-->
    <select id="selectGjcglPage" resultType="com.xpaas.zpbg.vo.GjcglVO">
        SELECT
            gjcgl.*,
            GROUP_CONCAT( CASE WHEN mkgl.cmm IS NOT NULL AND mkgl.cmm != '' THEN mkgl.cmm ELSE mkgl.mkmc END ORDER BY mkgl.px,mkgl.id) AS yymk,
            IFNULL( pcinfo.pc, 0 ) AS pcSum
        FROM
            t_dt_jxpj_zpbg_gjcgl gjcgl
                LEFT JOIN t_dt_jxpj_zpbg_gjcglmk gjcglmk ON gjcgl.id = gjcglmk.gjcid
                AND gjcglmk.scbj = 0
                LEFT JOIN t_dt_jxpj_zpbg_mkgl mkgl ON gjcglmk.mkid = mkgl.id
                AND mkgl.scbj = 0
                LEFT JOIN (
                SELECT
                    gjcznjc.gjcid AS gjcid,
                    sum( gjcznjc.pc ) AS pc
                FROM
                    t_dt_jxpj_zpbg_gjcznjc gjcznjc
                        LEFT JOIN t_dt_jxpj_zpbg_bbgl bbgl ON gjcznjc.bbid = bbgl.id
                        AND bbgl.scbj = 0
                WHERE
                    gjcznjc.scbj = 0
                  AND bbgl.bblx = 1
                GROUP BY
                    gjcznjc.gjcid
            ) pcinfo ON pcinfo.gjcid = gjcgl.id
        WHERE
            gjcgl.scbj = 0
            <if test="gjcgl.gjcmc != null and gjcgl.gjcmc != ''">
                and gjcgl.gjcmc like concat('%', #{gjcgl.gjcmc}, '%')
            </if>
        GROUP BY
            gjcgl.id
        ORDER BY
            pcSum DESC,
            gjcgl.cjrq DESC
    </select>
    <!--模块列表查询-->
    <select id="selectMkglList" resultType="com.xpaas.zpbg.vo.GjcglVO">
        SELECT id AS mkid,( CASE WHEN cmm IS NOT NULL AND cmm != '' THEN cmm ELSE mkmc END ) AS mkmc
        FROM
            t_dt_jxpj_zpbg_mkgl
        WHERE
            scbj = 0
            and mklx = #{zbly}
        ORDER BY
            px,
            id
    </select>

    <!--报告列表查询-->
    <select id="selectBgglList" resultType="com.xpaas.zpbg.vo.GjcglVO">
        select id AS bgid,CONCAT(nd,"年度",bgmc) AS bgmc from t_dt_jxpj_zpbg_bggl where scbj = 0 ORDER BY px,id
    </select>

    <!--进度列表查询-->
    <select id="selectJdglList" resultType="com.xpaas.zpbg.vo.GjcglVO">
        select id AS jdid,jdmc from t_dt_jxpj_zpbg_jdgl where scbj = 0 and KSSJ &lt;= CURDATE() and bgid = #{bgid} ORDER BY px,id
    </select>

    <!--最新进度查询-->
    <select id="selectZxjd" resultType="com.xpaas.zpbg.vo.GjcglVO">
        select id AS jdid,jdmc from t_dt_jxpj_zpbg_jdgl where scbj = 0 and KSSJ &lt;= CURDATE() and bgid = #{bgid} ORDER BY kssj DESC LIMIT 1
    </select>

</mapper>
