package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.mapper.BgmkztjlMapper;
import com.xpaas.zpbg.service.IBgmkztjlService;
import com.xpaas.zpbg.vo.BgmkztjlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-报告模块状态记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@Service
public class BgmkztjlServiceImpl extends BaseServiceImpl<BgmkztjlMapper, Bgmkztjl> implements IBgmkztjlService {

	@Override
	public IPage<BgmkztjlVO> selectBgmkztjlPage(IPage<BgmkztjlVO> page, BgmkztjlVO bgmkztjl) {
		return page.setRecords(baseMapper.selectBgmkztjlPage(page, bgmkztjl));
	}

}
