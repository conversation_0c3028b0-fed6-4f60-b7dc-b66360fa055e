package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-版本批注实体类
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BBPZ")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bbpz对象", description = "教学评价-自评报告-版本批注")
public class Bbpz extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BBID")
	private Long bbid;

	/**
	* 批注ID
	*/
	@ExcelProperty("批注ID")
	@ApiModelProperty(value = "批注ID")
	@TableField("COMMENT_ID")
	private String commentId;

	/**
	 * 批注文本
	 */
	@ExcelProperty("批注文本")
	@ApiModelProperty(value = "批注文本")
	@TableField("COMMENT_TEXT")
	private String commentText;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;



}
