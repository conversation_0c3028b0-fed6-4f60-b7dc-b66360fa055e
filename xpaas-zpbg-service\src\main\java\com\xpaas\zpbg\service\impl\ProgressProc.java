package com.xpaas.zpbg.service.impl;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 处理进度信息
 */
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Slf4j
@Component
public class ProgressProc {

    // 可视化缓存前缀
    private static final String KEY_PREFIX_PROGRESS = ":zpbg_progress:";

    // 系统缓存前缀
    @Value("${spring.cache.redis.keyPrefix}")
    private String keyPrefix;

    @Autowired
    private RedisTemplate redisTemplate;

    //////////////////////////////////////////////
    // 公共方法
    //////////////////////////////////////////////

    /**
     * 创建新任务
     * 也可自行填充ProgressInfo直接调用updateProgress
     *
     * @param taskId
     * @return
     */
    public ProgressInfo createProgress(String taskId) {
        ProgressInfo progressInfo = new ProgressInfo();
        progressInfo.setTaskId(taskId);
        progressInfo.setStatus(0);
        progressInfo.setInfo("初始化中");
        redisSave(progressInfo);
        return progressInfo;
    }

    /**
     * 更新任务进度
     * 不要过于频繁调用
     *
     * @param progressInfo
     */
    public void updateProgress(ProgressInfo progressInfo) {
        redisSave(progressInfo);
    }

    /**
     * 获取任务进度
     *
     * @param taskId
     * @return
     */
    public ProgressInfo selectProgress(String taskId) {
        return redisGet(taskId);
    }

    //////////////////////////////////////////////
    // 内部处理
    //////////////////////////////////////////////
    private void redisSave(ProgressInfo progressInfo) {
        if (progressInfo == null || StringUtils.isBlank(progressInfo.getTaskId())) {
            log.error("处理进度信息 - 信息存储参数错误：未指定任务信息");
            return;
        }
        String taskId = progressInfo.getTaskId();
        if (StringUtils.isBlank(taskId)) {
            log.error("处理进度信息 - 信息存储参数错误：未指定任务ID");
            return;
        }
        String key = makeKey(taskId);

        try {
            redisTemplate.opsForValue().set(key, progressInfo, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("处理进度信息 - 信息存储异常", e);
        }
    }

    private ProgressInfo redisGet(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            ProgressInfo progressInfo = new ProgressInfo();
            progressInfo.setStatus(-2);
            log.error("处理进度信息 - 信息获取参数错误：未指定任务ID");
            return progressInfo;
        }

        String key = makeKey(taskId);
        ProgressInfo progressInfo = null;
        try {
            ValueOperations<String, ProgressInfo> operationData = redisTemplate.opsForValue();
            progressInfo = operationData.get(key);
        } catch (Exception e) {
            log.error("处理进度信息 - 信息获取异常", e);
        }

        if (progressInfo == null) {
            progressInfo = new ProgressInfo();
            progressInfo.setStatus(-2);
            log.error("处理进度信息 - 信息获取失败：指定的任务不存在：" + key);
        }

        return progressInfo;
    }


    /**
     * 生成缓存键值
     */
    private String makeKey(String taskId) {
        return (StringUtils.isBlank(keyPrefix) ? "zpbg" : keyPrefix) + KEY_PREFIX_PROGRESS + taskId.replaceAll(":", "_");
    }

}
