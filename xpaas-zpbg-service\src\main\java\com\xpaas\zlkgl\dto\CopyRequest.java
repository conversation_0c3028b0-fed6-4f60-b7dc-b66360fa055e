package com.xpaas.zlkgl.dto;

import lombok.Data;

/**
 * 复制请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CopyRequest {

    /**
     * 复制类型
     */
    private CopyType copyType;

    /**
     * 源ID
     */
    private String sourceId;

    /**
     * 目标父级ID（文件夹复制时使用）
     */
    private String targetParentId;

    /**
     * 新名称（可选，如果不提供则自动生成）
     */
    private String newName;

    /**
     * 复制类型枚举
     */
    public enum CopyType {
        /**
         * 文件复制
         */
        FILE,
        /**
         * 普通文件夹复制
         */
        FOLDER_FLGL,
        /**
         * 外校文件夹复制
         */
        FOLDER_WXZL
    }
}
