package com.xpaas.zlkgl.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 教学评价-资料库平台-外校资料文件夹树表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Component
public class WxzlWjjWrapper extends BaseEntityWrapper<WxzlWjj, WxzlWjjVO> {


    /**
     * 将entity转换成 entityVO
     *
     * @return 转换后的entityVO对象
     * <AUTHOR>
     * @since 2025-07-25
     */
    @Override
    public WxzlWjjVO entityVO(WxzlWjj wxzlWjj) {
        WxzlWjjVO wxzlWjjVO = Objects.requireNonNull(BeanUtil.copy(wxzlWjj, WxzlWjjVO.class));
        //User cjr = UserCache.getUser(wxzlWjj.getCjr());
        //if (cjr != null){
        //	wxzlWjjVO.setCjrName(cjr.getName());
        //}
        //User gxr = UserCache.getUser(wxzlWjj.getGxr());
        //if (gxr != null){
        //	wxzlWjjVO.setGxrName(gxr.getName());
        //}
/**  **/
        return wxzlWjjVO;
    }


    @Override
    public WxzlWjjVO wrapperVO(WxzlWjjVO wxzlWjjVO) {
        //User cjr = UserCache.getUser(wxzlWjjVO.getCjr());
        //if (cjr != null){
        //	wxzlWjjVO.setCjrName(cjr.getName());
        //}
        //User gxr = UserCache.getUser(wxzlWjjVO.getGxr());
        //if (gxr != null){
        //	wxzlWjjVO.setGxrName(gxr.getName());
        //}
/**  */
        return wxzlWjjVO;
    }

}
