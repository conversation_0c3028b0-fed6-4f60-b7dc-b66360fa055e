package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-数据材料实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_SJCL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Sjcl对象", description = "教学评价-自评报告-数据材料")
public class Sjcl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 材料名称
	*/
	@ExcelProperty("材料名称")
	@ApiModelProperty(value = "材料名称")
	@TableField("CLMC")
	private String clmc;

	/**
	* 模块ID
	*/
	@ExcelProperty("模块ID")
	@ApiModelProperty(value = "模块ID")
	@TableField("MKID")

	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;

	/**
	* 所属模块名称
	*/
	@ExcelProperty("所属模块名称")
	@ApiModelProperty(value = "所属模块名称")
	@TableField("SSMKMC")
	private String ssmkmc;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 材料地址
	*/
	@ExcelProperty("材料地址")
	@ApiModelProperty(value = "材料地址")
	@TableField("CLDZ")
	private String cldz;

	/**
	* 引用状态
	*/
	@ExcelProperty("引用状态")
	@ApiModelProperty(value = "引用状态")
	@TableField("YYZT")
	private Integer yyzt;



}
