package com.xpaas.zpbg.common.utils;


import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DateUtils {

    /**
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>   判断当前时间在时间区间内
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }




    }

    /**
     * LocalDateTime转换为Date
     * @param localDateTime
     */
    public static Date localDateTime2Date( LocalDateTime localDateTime){
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);//Combines this date-time with a time-zone to create a  ZonedDateTime.
        Date date = Date.from(zdt.toInstant());
        System.out.println(date.toString());//Tue Mar 27 14:17:17 CST 2018
        return  date;
    }

//    public static void main(String[] args) throws ParseException {
//        SimpleDateFormat ft = new SimpleDateFormat ("yyyy-MM-dd hh:mm:ss");
//        Date startTime = ft.parse("2020-12-14 00:00:00");
//        Date endTime = ft.parse("2020-12-14 00:00:00");
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String s = sdf.format(new Date());
//        Date date =  sdf.parse(s);
//        boolean effectiveDate = isEffectiveDate(date, startTime, endTime);
//        if (effectiveDate) {
//            System.out.println("当前时间在范围内");
//        }else {
//            System.out.println("当前时间不在范围内");
//        }
//    }


    /**
     * 封装 当前日期 所 需日期格式
     *  zq
     */
    public static Map<String,String> getMonthAngWeekNumber(){
        Map<String,String> map=new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        String years=String.valueOf(calendar.get(Calendar.YEAR));
        map.put("year",years);
        String weekNum = String.valueOf(calendar.get(Calendar.WEEK_OF_MONTH));
        map.put("weekNum", weekNum);
        int  mon=calendar.get(Calendar.MONTH)+1;
        int  da=calendar.get(Calendar.DAY_OF_MONTH);
        map.put("month",String.valueOf(mon));
        map.put("yearMonth",years+"-"+mon);
        map.put("dateTime",String.valueOf(calendar.getTime()));
        map.put("ymd",years+"-"+mon+"-"+da);
        //当前时间
        LocalTime time = LocalTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        map.put("currTime",time.format(formatter)+":00");
        map.put("dt",years+"-"+mon+"-"+da+" "+"00:00:00");
        return map;
    }

    /**
     * 字符串  yyyy-MM-dd  转为 Date yyyy-MM-dd HH:mm:ss
     *  zq
     */
    public static Date strToData(String str){
        Date date=null;
       // str=str+" 00:00:00";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date= formatter.parse(str);
        } catch (Exception e) {
           // throw new ServiceException("时间转换失败");
            throw new RuntimeException("时间转换失败");
        }
        return date;

    }
    /**
     * Date 转为  string  (yyyy-MM-dd HH:mm:ss)
     *  zq
     */
    public static String dateToStr(Date date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    /**
     * Date 转为  string  (yyyy-MM-dd)
     *  zq
     */
    public static String dateToStrYMD(Date date){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date);
    }



}
