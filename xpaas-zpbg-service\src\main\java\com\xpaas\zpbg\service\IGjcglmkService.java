package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Gjcglmk;
import com.xpaas.zpbg.vo.GjcglmkVO;

import java.util.List;

/**
 * 教学评价-自评报告-关键词关联模块 服务类
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
public interface IGjcglmkService extends BaseService<Gjcglmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcglmk
	 * @return
	 */
	IPage<GjcglmkVO> selectGjcglmkPage(IPage<GjcglmkVO> page, GjcglmkVO gjcglmk);

	/**
	 * 关键词关联模块查询
	 *
	 * @param gjcglmk
	 * @return
	 */
	List<GjcglmkVO> selectGjcglmkList(GjcglmkVO gjcglmk);

	/**
	 * 关键词关联模块保存
	 *
	 * @param gjcglmk
	 * @param glfw
	 * @param mode
	 * @return
	 */
	boolean gjcglmkSave(GjcglmkVO gjcglmk, Integer glfw ,String mode);

}
