package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.IZtxxService;
import com.xpaas.zpbg.vo.ZtxxParamVO;
import com.xpaas.zpbg.vo.ZtxxResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 教学评价-自评报告-状态信息 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/ztxx")
@Api(value = "教学评价-自评报告-状态信息", tags = "教学评价-自评报告-状态信息接口")
public class ZtxxController extends BaseController {

    private IZtxxService ztxxService;

    /**
     * 获取版本状态信息
     */
    @GetMapping("/ztxx")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取状态信息", notes = "传入版本ID和角色")
    @ApiLog("状态信息-详情")
    public R<ZtxxResultVO> ztxx(ZtxxParamVO param) {
        return R.data(ztxxService.ztxx(param));
    }

}
