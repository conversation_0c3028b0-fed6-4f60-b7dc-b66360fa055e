package com.xpaas.zlkgl.service;

import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 复制功能测试类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CopyFunctionTest {

    @Autowired
    private IZlglService zlglService;

    @Autowired
    private IFlglWjjService flglWjjService;

    @Autowired
    private IWxzlWjjService wxzlWjjService;

    /**
     * 测试文件复制功能
     */
    @Test
    public void testCopyFile() {
        // 1. 创建测试文件
        Zlgl testFile = new Zlgl();
        testFile.setWjjId("test-folder-1");
        testFile.setZlMc("测试文件.pdf");
        testFile.setZlCmm("测试文件.pdf");
        testFile.setZlDz("/path/to/test.pdf");
        testFile.setPjLx("1");
        zlglService.save(testFile);

        // 2. 复制文件
        boolean result = zlglService.copyFile(String.valueOf(testFile.getId()), "test-folder-2", null);

        // 3. 验证结果
        assertTrue(result, "文件复制应该成功");
    }

    /**
     * 测试文件夹复制功能
     */
    @Test
    public void testCopyFlglFolder() {
        // 1. 创建测试文件夹
        FlglWjj testFolder = new FlglWjj();
        testFolder.setFjWjjId("parent-folder");
        testFolder.setWjjMc("测试文件夹");
        testFolder.setWjjLx("1"); // 普通文件夹
        testFolder.setPjLx("1");
        flglWjjService.save(testFolder);

        // 2. 复制文件夹
        boolean result = flglWjjService.copyFolder(String.valueOf(testFolder.getId()), "target-parent", null);

        // 3. 验证结果
        assertTrue(result, "文件夹复制应该成功");
    }

    /**
     * 测试外校文件夹复制功能
     */
    @Test
    public void testCopyWxzlFolder() {
        // 1. 创建测试外校文件夹
        WxzlWjj testFolder = new WxzlWjj();
        testFolder.setFjWjjId("parent-folder");
        testFolder.setWjjMc("外校文件夹");
        testFolder.setWjjLx("1"); // 普通文件夹
        testFolder.setPjLx("1");
        wxzlWjjService.save(testFolder);

        // 2. 复制文件夹
        boolean result = wxzlWjjService.copyFolder(String.valueOf(testFolder.getId()), "target-parent", null);

        // 3. 验证结果
        assertTrue(result, "外校文件夹复制应该成功");
    }

    /**
     * 测试外链文件夹复制功能
     */
    @Test
    public void testCopyExternalLinkFolder() {
        // 1. 创建外链文件夹
        FlglWjj externalFolder = new FlglWjj();
        externalFolder.setFjWjjId("parent-folder");
        externalFolder.setWjjMc("外链文件夹");
        externalFolder.setWjjLx("0"); // 外链文件夹
        externalFolder.setWbljDz("https://example.com");
        externalFolder.setPjLx("1");
        flglWjjService.save(externalFolder);

        // 2. 复制外链文件夹
        boolean result = flglWjjService.copyFolder(String.valueOf(externalFolder.getId()), "target-parent", null);

        // 3. 验证结果
        assertTrue(result, "外链文件夹复制应该成功");
    }

    /**
     * 测试自定义名称复制
     */
    @Test
    public void testCopyWithCustomName() {
        // 1. 创建测试文件
        Zlgl testFile = new Zlgl();
        testFile.setWjjId("test-folder");
        testFile.setZlMc("原始文件.pdf");
        testFile.setZlCmm("原始文件.pdf");
        testFile.setPjLx("1");
        zlglService.save(testFile);

        // 2. 使用自定义名称复制
        boolean result = zlglService.copyFile(String.valueOf(testFile.getId()), "target-folder", "自定义名称.pdf");

        // 3. 验证结果
        assertTrue(result, "使用自定义名称复制应该成功");
    }
}
