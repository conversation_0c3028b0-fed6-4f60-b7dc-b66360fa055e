package com.xpaas.zpbg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自评报告-消息发布视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
@ApiModel(value = "XxfbUserVO对象", description = "自评报告-消息发布-用户")
public class XxfbUserVO {
	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "级别 1：部系负责任人 2：主任务人员 3：子任务人员")
    private Integer level;
}
