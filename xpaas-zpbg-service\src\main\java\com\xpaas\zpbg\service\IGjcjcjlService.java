package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Gjcjcjl;
import com.xpaas.zpbg.vo.GjcjcjlVO;

import java.util.List;

/**
 * 教学评价-自评报告-关键词检测记录 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IGjcjcjlService extends BaseService<Gjcjcjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcjcjl
	 * @return
	 */
	IPage<GjcjcjlVO> selectGjcjcjlPage(IPage<GjcjcjlVO> page, GjcjcjlVO gjcjcjl);

	/**
	 * 关键词检测
	 *
	 * @param gjcjcjlVO
	 * @return
	 */
	boolean detect(GjcjcjlVO gjcjcjlVO);

	/**
	 * 报告列表
	 *
	 * @return
	 */
	List<GjcjcjlVO> getBgList();
}
