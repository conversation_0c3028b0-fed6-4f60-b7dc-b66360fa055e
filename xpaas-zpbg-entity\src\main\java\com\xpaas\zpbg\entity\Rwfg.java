package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-任务分工实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_RWFG")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Rwfg对象", description = "教学评价-自评报告-任务分工")
public class Rwfg extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGID")
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGMKID")
	private Long bgmkid;

	/**
	* 分工类型
	*/
	@ExcelProperty("分工类型")
	@ApiModelProperty(value = "分工类型")
	@TableField("FGLX")
	private Integer fglx;

	/**
	* 单位ID
	*/
	@ExcelProperty("单位ID")
	@ApiModelProperty(value = "单位ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("DWID")
	private Long dwid;

	/**
	* 单位名称
	*/
	@ExcelProperty("单位名称")
	@ApiModelProperty(value = "单位名称")
	@TableField("DWMC")
	private String dwmc;

	/**
	* 人员ID
	*/
	@ExcelProperty("人员ID")
	@ApiModelProperty(value = "人员ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("RYID")
	private Long ryid;

	/**
	* 人员姓名
	*/
	@ExcelProperty("人员姓名")
	@ApiModelProperty(value = "人员姓名")
	@TableField("RYXM")
	private String ryxm;

	/**
	 * 处理状态
	 */
	@ExcelProperty("处理状态")
	@ApiModelProperty(value = "处理状态")
	@TableField("CLZT")
	private Integer clzt;

}
