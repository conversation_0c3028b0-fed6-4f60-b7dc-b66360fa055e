package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Pcgl;
import com.xpaas.zpbg.vo.PcglVO;

/**
 * 教学评价-自评报告-批次管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IPcglService extends BaseService<Pcgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param pcgl
	 * @return
	 */
	IPage<PcglVO> selectPcglPage(IPage<PcglVO> page, Pcgl pcgl);
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param pcgl
	 * @return
	 */
	IPage<PcglVO> selectPcglPageCjrqDesc(IPage<PcglVO> page, Pcgl pcgl);


}
