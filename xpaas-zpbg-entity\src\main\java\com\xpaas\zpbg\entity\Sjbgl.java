package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-数据表关联实体类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_SJBGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Sjbgl对象", description = "教学评价-自评报告-数据表关联")
public class Sjbgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 关联KEY
	*/
	@ExcelProperty("关联KEY")
	@ApiModelProperty(value = "关联KEY")
	@TableField("GLKEY")
	private String glkey;

	/**
	 * 关联文字
	 */
	@ExcelProperty("关联文字")
	@ApiModelProperty(value = "关联文字")
	@TableField("GLWZ")
	private String glwz;

	/**
	* 数据来源
	*/
	@ExcelProperty("数据来源")
	@ApiModelProperty(value = "数据来源")
	@TableField("SJLY")
	private Integer sjly;

	/**
	* 数据表
	*/
	@ExcelProperty("数据表")
	@ApiModelProperty(value = "数据表")
	@TableField("SJB")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long sjb;

	/**
	* 数据表名称
	*/
	@ExcelProperty("数据表名称")
	@ApiModelProperty(value = "数据表名称")
	@TableField("SJBMC")
	private String sjbmc;

	/**
	 * 数据表名称-不修改
	 */
	@ExcelProperty("数据表名称-不修改")
	@ApiModelProperty(value = "数据表名称-不修改")
	@TableField("SJBMCORG")
	private String sjbmcorg;

	/**
	* 数据材料ID
	*/
	@ExcelProperty("数据材料ID")
	@ApiModelProperty(value = "数据材料ID")
	@TableField("SJCLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long sjclid;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;



}
