package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Sjclssmk;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-数据材料所属模块视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SjclssmkVO对象", description = "教学评价-自评报告-数据材料所属模块")
public class SjclssmkVO extends Sjclssmk {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "模块ID数组")
    private Long[] mkidList;
}
