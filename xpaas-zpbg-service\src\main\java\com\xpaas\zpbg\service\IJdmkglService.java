package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Jdmkgl;
import com.xpaas.zpbg.vo.JdmkglVO;

/**
 * 教学评价-自评报告-进度模块关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IJdmkglService extends BaseService<Jdmkgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param jdmkgl
	 * @return
	 */
	IPage<JdmkglVO> selectJdmkglPage(IPage<JdmkglVO> page, JdmkglVO jdmkgl);

	/**
	 * 进度模块关联保存
	 *
	 * @param jdmkgl
	 * @return
	 */
	boolean jdmkglSave(JdmkglVO jdmkgl);

}
