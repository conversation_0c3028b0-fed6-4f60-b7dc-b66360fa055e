<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZjmkSpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zjmkSpResultMap" type="com.xpaas.zpbg.entity.ZjmkSp">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="KCID" property="kcid"/>
        <result column="SPMC" property="spmc"/>
        <result column="ZJR" property="zjr"/>
        <result column="ZYGZD" property="zygzd"/>
        <result column="ZYGZDMC" property="zygzdmc"/>
        <result column="GJC" property="gjc"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="SSZT" property="sszt"/>
        <result column="SSZTMC" property="ssztmc"/>
        <result column="SPJJ" property="spjj"/>
        <result column="SPJJTEXT" property="spjjtext"/>
        <result column="SP" property="sp"/>
        <result column="SPWJMC" property="spwjmc"/>
        <result column="FM" property="fm"/>
        <result column="SPSC" property="spsc"/>
        <result column="TJ" property="tj"/>
        <result column="TJSJ" property="tjsj"/>
        <result column="PX" property="px"/>
        <result column="SCRMC" property="scrmc"/>
        <result column="SCRQ" property="scrq"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zjmkSpResultMapVO" type="com.xpaas.zpbg.vo.ZjmkSpVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="KCID" property="kcid"/>
        <result column="SPMC" property="spmc"/>
        <result column="ZJR" property="zjr"/>
        <result column="ZYGZD" property="zygzd"/>
        <result column="ZYGZDMC" property="zygzdmc"/>
        <result column="GJC" property="gjc"/>
        <result column="GJCMC" property="gjcmc"/>
        <result column="SSZT" property="sszt"/>
        <result column="SSZTMC" property="ssztmc"/>
        <result column="SPJJ" property="spjj"/>
        <result column="SPJJTEXT" property="spjjtext"/>
        <result column="SP" property="sp"/>
        <result column="SPWJMC" property="spwjmc"/>
        <result column="FM" property="fm"/>
        <result column="SPSC" property="spsc"/>
        <result column="TJ" property="tj"/>
        <result column="TJSJ" property="tjsj"/>
        <result column="PX" property="px"/>
        <result column="SCRMC" property="scrmc"/>
        <result column="SCRQ" property="scrq"/>
    </resultMap>

    <select id="getZjmkSp" resultMap="zjmkSpResultMapVO">
        select * from T_DT_JXPJ_ZJMK_SP sp
        where sp.scbj = 0

        and sp.tszxpt = 1
        <if test="zygzdid != null and zygzdid != ''">
            and sp.ZYGZDIDS LIKE CONCAT('%', #{zygzdid}, '%')
        </if>

        limit 1
    </select>

</mapper>
