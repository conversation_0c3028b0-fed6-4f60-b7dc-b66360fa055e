package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.SummarySection;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

;

/**
 * 自评报告-首页甘特图视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SummarySectionVO对象", description = "自评报告-首页-甘特图")
public class SummarySectionVO extends SummarySection {
	private static final long serialVersionUID = 1L;

	private String caption;

	private List<SectionVO> sections;


}
