package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.entity.Wzgl;
import com.xpaas.zpbg.service.IWzglService;
import com.xpaas.zpbg.utils.ZipUtils;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.WzglVO;
import com.xpaas.zpbg.wrapper.WzglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 自评报告-文章管理 控制器
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wzgl")
@Api(value = "自评报告-文章管理", tags = "自评报告-文章管理 控制器")
public class WzglController extends BaseController {

	private WzglWrapper wzglWrapper;

	private IWzglService wzglService;

	private IUserClient userClient;

	private IOssClient client;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入wzgl")
	@ApiLog("文章管理-详情")
	public R<WzglVO> detail(Wzgl wzgl) {
		Wzgl detail = wzglService.getOne(Condition.getQueryWrapper(wzgl));
		WzglVO wzglVO = wzglWrapper.entityVO(detail);

		if(wzglVO != null) {
			int llrs = wzglService.getLlrs(wzglVO);
			wzglVO.setLlrs(String.valueOf(llrs));
		}

		return R.data(wzglVO);
	}

	/**
	 * 分页 自评报告-文章管理 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入wzgl")
	@ApiLog("文章管理-列表")
	public R list(WzglVO wzgl, Query query) {
		List<WzglVO> ret = new ArrayList<WzglVO>();

		// 取得慕课
		List<WzglVO> kcList = wzglService.getKcList();

		// 取得文章
		IPage<WzglVO> pages = wzglService.selectWzPage(Condition.getPage(query), wzgl);

		if(kcList != null && kcList.size() > 0) {
			ret.addAll(kcList);
		}

		if(pages != null && pages.getRecords() != null && pages.getRecords().size() > 0) {
			ret.addAll(pages.getRecords());
		}

		return R.data(ret);
	}

	/**
	 * 自评报告-文章管理-个性标准列表
	 */
	@GetMapping("/gxbzlist")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "个性标准列表", notes = "传入无")
	@ApiLog("文章管理-个性标准列表")
	public R gxbzlist() {
		WzglVO wzgl = new WzglVO();
		wzgl.setZswz(1);

		Query query = new Query();
		query.setCurrent(1);
		query.setSize(-1);

		// 取得文章
		IPage<WzglVO> pages = wzglService.selectWzPage(Condition.getPage(query), wzgl);

		return R.data(pages.getRecords());
	}

	/**
	 * 自定义分页 自评报告-文章管理
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入wzgl")
	@ApiLog("文章管理-自定义分页")
	public R<IPage<WzglVO>> page(WzglVO wzgl, Query query) {
		IPage<WzglVO> pages = wzglService.selectWzPage(Condition.getPage(query), wzgl);
		return R.data(wzglWrapper.wrapperPageVO(pages));
	}

	/**
	 * 新增 自评报告-文章管理
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入wzgl")
	@ApiLog("文章管理-新增")
	public R save(@Valid @RequestBody WzglVO wzglVO) {
		LoginUser user = AuthUtil.getUser();
		wzglVO.setFbr(user.getUserId());
		wzglVO.setFbrxm(user.getUserName());

		setYsblj(wzglVO);
		boolean b = wzglService.save(wzglVO);

		return R.status(b);
	}

	/**
	 * 修改 自评报告-文章管理
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入wzgl")
	@ApiLog("文章管理-修改")
	public R update(@Valid @RequestBody WzglVO wzglVO) {
		Wzgl wzglOld = wzglService.getById(wzglVO.getId());

		// 路径修改时需要重新打包
		if(!String.valueOf(wzglOld.getFjlj()).equals(String.valueOf(wzglVO.getFjlj()))) {
			setYsblj(wzglVO);
		}

		boolean b = wzglService.updateById(wzglVO);
		return R.status(b);
	}

	private void setYsblj(WzglVO wzglVO) {
		List<Map<String,Object>> fileList = wzglVO.getFileList();

		if(fileList != null && fileList.size() > 0) {
			if(fileList.size() == 1) {
				wzglVO.setYsblj(String.valueOf(fileList.get(0).get("wjlj")));
			} else {
				try {
					String ysblj = ZipUtils.filesToZipUpload(fileList, client);
					if ("".equals(ysblj)) {
						throw new ServiceException("远程下载失败，打包未成功！");
					}
					wzglVO.setYsblj(ysblj);
				} catch (Exception e) {
					e.printStackTrace();

					throw new ServiceException("远程下载失败，打包未成功！");
				}
			}
		} else {
			wzglVO.setYsblj("");
		}
	}

	/**
	 * 新增或修改 自评报告-文章管理 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入wzgl")
	@ApiLog("文章管理-新增或修改")
	public R submit(@Valid @RequestBody Wzgl wzgl) {
		return R.status(wzglService.saveOrUpdate(wzgl));
	}

	/**
	 * 删除 自评报告-文章管理
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("文章管理-删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = wzglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}

	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("文章管理-高级查询")
	public R<IPage<WzglVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Wzgl> queryWrapper = Condition.getQueryWrapper(map, Wzgl.class);
		IPage<Wzgl> pages = wzglService.page(Condition.getPage(query), queryWrapper);

		IPage<WzglVO> pagesVO = wzglWrapper.pageVO(pages);

		return R.data(pagesVO);
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("文章管理-导出")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Wzgl> queryWrapper = Condition.getQueryWrapper(map, Wzgl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Wzgl> list = wzglService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Wzgl.class);
	}

	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("文章管理-导入")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Wzgl> list = ExcelUtil.read(file, Wzgl.class);
		//TODO 此处需要根据具体业务添加代码
		wzglService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("文章管理-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Wzgl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Wzgl> list = wzglService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Wz导入模板", "Wz导入模板",columnFiledNames, list, Wzgl.class);
	}
}
