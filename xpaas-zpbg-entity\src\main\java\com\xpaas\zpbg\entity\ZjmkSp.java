package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-专家慕课-视频实体类
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@TableName("T_DT_JXPJ_ZJMK_SP")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZjmkSp对象", description = "教学评价-专家慕课-视频")
public class ZjmkSp extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 课程ID
	*/
	@ExcelProperty("课程ID")
	@ApiModelProperty(value = "课程ID")
	@TableField("KCID")
	private String kcid;

	/**
	* 视频名称
	*/
	@ExcelProperty("视频名称")
	@ApiModelProperty(value = "视频名称")
	@TableField("SPMC")
	private String spmc;

	/**
	* 主讲人
	*/
	@ExcelProperty("主讲人")
	@ApiModelProperty(value = "主讲人")
	@TableField("ZJR")
	private String zjr;

	/**
	* 主要关注点
	*/
	@ExcelProperty("主要关注点")
	@ApiModelProperty(value = "主要关注点")
	@TableField("ZYGZD")
	private String zygzd;

	/**
	 * 主要关注点名称
	 */
	@ExcelProperty("主要关注点名称")
	@ApiModelProperty(value = "主要关注点名称")
	@TableField("ZYGZDMC")
	private String zygzdmc;

	/**
	* 关键词
	*/
	@ExcelProperty("关键词")
	@ApiModelProperty(value = "关键词")
	@TableField("GJC")
	private String gjc;

	/**
	 * 关键词名称
	 */
	@ExcelProperty("关键词名称")
	@ApiModelProperty(value = "关键词名称")
	@TableField("GJCMC")
	private String gjcmc;

	/**
	* 所属专题
	*/
	@ExcelProperty("所属专题")
	@ApiModelProperty(value = "所属专题")
	@TableField("SSZT")
	private String sszt;

	/**
	 * 所属专题名称
	 */
	@ExcelProperty("所属专题名称")
	@ApiModelProperty(value = "所属专题名称")
	@TableField("SSZTMC")
	private String ssztmc;

	/**
	* 视频简介摘要
	*/
	@ExcelProperty("视频简介摘要")
	@ApiModelProperty(value = "视频简介摘要")
	@TableField("SPJJ")
	private String spjj;

	/**
	 * 视频简介富文本
	 */
	@ExcelProperty("视频简介富文本")
	@ApiModelProperty(value = "视频简介富文本")
	@TableField("SPJJTEXT")
	private String spjjtext;

	/**
	* 视频
	*/
	@ExcelProperty("视频")
	@ApiModelProperty(value = "视频")
	@TableField("SP")
	private String sp;

	/**
	 * 视频文件名称
	 */
	@ExcelProperty("视频文件名称")
	@ApiModelProperty(value = "视频文件名称")
	@TableField("SPWJMC")
	private String spwjmc;

	/**
	* 封面
	*/
	@ExcelProperty("封面")
	@ApiModelProperty(value = "封面")
	@TableField("FM")
	private String fm;

	/**
	* 视频时长
	*/
	@ExcelProperty("视频时长")
	@ApiModelProperty(value = "视频时长")
	@TableField("SPSC")
	private String spsc;

	/**
	* 推荐
	*/
	@ExcelProperty("推荐")
	@ApiModelProperty(value = "推荐")
	@TableField("TJ")
	private String tj;

	/**
	* 推荐时间
	*/
	@ExcelProperty("推荐时间")
	@ApiModelProperty(value = "推荐时间")
	@TableField("TJSJ")
	private Date tjsj;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private String px;

	/**
	* 上传人名称
	*/
	@ExcelProperty("上传人名称")
	@ApiModelProperty(value = "上传人名称")
	@TableField("SCRMC")
	private String scrmc;


	/**
	 * 主要关注点ID
	 */
	@ExcelProperty("主要关注点ID")
	@ApiModelProperty(value = "主要关注点ID")
	@TableField("ZYGZDIDS")
	private String zygzdids;

	/**
	 * 主要关注点名称
	 */
	@ExcelProperty("主要关注点名称")
	@ApiModelProperty(value = "主要关注点名称")
	@TableField("ZYGZDMCS")
	private String zygzdmcs;

	/**
	 * 上传日期
	 */
	@ExcelProperty("上传日期")
	@ApiModelProperty(value = "上传日期")
	@TableField("SCRQ")
	private Date scrq;
}
