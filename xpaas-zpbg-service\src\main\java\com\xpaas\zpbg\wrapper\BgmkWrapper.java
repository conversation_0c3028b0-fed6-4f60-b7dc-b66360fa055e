package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.vo.BgmkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-报告模块包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Component
public class BgmkWrapper extends BaseEntityWrapper<Bgmk, BgmkVO>  {


	@Override
	public BgmkVO entityVO(Bgmk bgmk) {
		BgmkVO bgmkVO = Objects.requireNonNull(BeanUtil.copy(bgmk, BgmkVO.class));
		//User cjr = UserCache.getUser(bgmk.getCjr());
		//if (cjr != null){
		//	bgmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmk.getGxr());
		//if (gxr != null){
		//	bgmkVO.setGxrName(gxr.getName());
		//}
		return bgmkVO;
	}

    @Override
    public BgmkVO wrapperVO(BgmkVO bgmkVO) {
		//User cjr = UserCache.getUser(bgmkVO.getCjr());
		//if (cjr != null){
		//	bgmkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgmkVO.getGxr());
		//if (gxr != null){
		//	bgmkVO.setGxrName(gxr.getName());
		//}
        return bgmkVO;
    }

}
