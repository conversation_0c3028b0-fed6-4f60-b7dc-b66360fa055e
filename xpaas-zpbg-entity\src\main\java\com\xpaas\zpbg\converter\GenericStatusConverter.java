package com.xpaas.zpbg.converter;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;


public class GenericStatusConverter implements Converter<Integer> {

    private Map<Integer, String> statusMap = new HashMap<>();

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (statusMap.isEmpty() && contentProperty != null) {
            Field field = contentProperty.getField();
            GenericStatusMapping statusMapping = field.getAnnotation(GenericStatusMapping.class);
            if (statusMapping != null) {
                for (String pair : statusMapping.keyValuePairs()) {
                    String[] keyValue = pair.split(":");
                    statusMap.put(Integer.valueOf(keyValue[0].trim()), keyValue[1].trim());
                }
            }
        }
        String statusString = statusMap.getOrDefault(value, "Unknown");
        return new CellData(statusString);
    }
}