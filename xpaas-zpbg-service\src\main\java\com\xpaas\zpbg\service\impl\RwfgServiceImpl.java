package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseEntity;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.utils.DateUtil;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.system.cache.DeptCache;
import com.xpaas.user.entity.User;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.dto.RwryDTO;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.mapper.RwfgMapper;
import com.xpaas.zpbg.service.IOnlyOfficeService;
import com.xpaas.zpbg.service.IRwfgService;
import com.xpaas.zpbg.vo.RwfgVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教学评价-自评报告-任务分工 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class RwfgServiceImpl extends BaseServiceImpl<RwfgMapper, Rwfg> implements IRwfgService {

	@Resource
	private IUserClient userClient;

	@Autowired
	private RwfgMapper rwfgMapper;

	@Autowired
	@Lazy
	private IOnlyOfficeService iOnlyOfficeService;

	// # 自评报告撰写平台-校外专家-角色ID
	@Value("${xpaas.roles.zpbgzxptXwzjRoleId}")
	private String[] zpbgzxptXwzjRoleId;

	/**
	 * 查询
	 */
	@Override
	public IPage<RwfgVO> selectRwfgPage(IPage<RwfgVO> page, RwfgVO rwfg) {
		return page.setRecords(baseMapper.selectRwfgPage(page, rwfg));
	}

//	@Override
//	public List<RwryDTO> selectMkry(IPage<RwfgVO> page,Bggl bggl){
//		return rwfgMapper.selectMkry(page,bggl);
//	}

	/**
	 * 查询任务人员信息
	 */
	@Override
	public List<RwryDTO> selectMkry(Bggl bggl){
		return rwfgMapper.selectMkry(bggl);
	}

	/**
	 * 根据条件查询任务人员信息
	 */
	@Override
	public List<RwryDTO> selectMkryByWrapper(@Param(Constants.WRAPPER) Wrapper<RwryDTO> rwryWrapper){
		return rwfgMapper.selectMkryByWrapper(rwryWrapper);
	}

	/**
	 * 先删除再保存任务人员信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteAndSave(List<Rwfg> rwfgList){
		if(rwfgList.size()>0){
			Long bgid = rwfgList.get(0).getBgid();

			// 修改前的任务分工
			List<RwfgVO> rwfgVOList = rwfgMapper.selectRwfgInfo(bgid);
			// 获取去除的人员
			List<RwfgVO> ryList = new ArrayList<>();
			for(RwfgVO rwfgVO : rwfgVOList){
				Rwfg r = rwfgList.stream().filter(rwfg -> rwfg.getBgmkid().equals(rwfgVO.getBgmkid()) &&
						rwfg.getFglx() == rwfgVO.getFglx() && rwfg.getRyid().equals(rwfgVO.getRyid())).findFirst().orElse(null);
				if( r == null ){
					ryList.add(rwfgVO);
				}
			}

			// 踢人参数
			List<Map<String, Object>> infolist = new ArrayList<>();
			if(ryList.size() > 0){
				// 按模块分类
				Map<Long, List<RwfgVO>> collect = ryList.stream().collect(Collectors.groupingBy(RwfgVO::getBgmkid));
				for (Long l : collect.keySet()) {
					// users数据取得
					List<Long> userLong = collect.get(l).stream().map(RwfgVO::getRyid).collect(Collectors.toList());
					// users类型转换
					List<String> userLists = userLong.stream().map(Object::toString).collect(Collectors.toList());
					// users去重
					List<String> userList = userLists.stream().distinct().collect(Collectors.toList());

					Map<String,Object> map = new HashMap<>();
					map.put("bgmkid",String.valueOf(l));
					map.put("users",userList);
					infolist.add(map);
				}
			}

			if(infolist.size() > 0){
				iOnlyOfficeService.dropUsers(infolist);
			}

			// 删除原有人员信息
			rwfgMapper.deletePhysical(bgid);
		}

		for(Rwfg rwfg : rwfgList){
			User user = userClient.userInfoById(rwfg.getRyid()).getData();
			rwfg.setId(null);
			if(user != null){
				rwfg.setRyid(user.getId());
				rwfg.setRyxm(user.getName());
				rwfg.setDwid(Long.parseLong(user.getDeptId()));
				rwfg.setDwmc(DeptCache.getDeptName(Long.parseLong(user.getDeptId())));
			}
		}
		return this.saveBatch(rwfgList);
	}

	private void resolveEntity(BaseEntity entity){
		LoginUser user = AuthUtil.getUser();
		Date now = DateUtil.now();
		if (user != null) {
			entity.setCjr(user.getUserId());
			entity.setCjbm((Long) Func.toLongList(user.getDeptId()).iterator().next());
			entity.setCjdw(user.getUnitId());
			entity.setGxr(user.getUserId());
		}

		if (entity.getZt() == null) {
			entity.setZt(1);
		}

		entity.setCjrq(now);
	}

	/**
	 * 演示报告导入撰写人
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean zxrImportSave(Rwfg rwfg){
		List<Rwfg> rwfgList = new ArrayList<>();

		// 报告模块
		List<RwfgVO> bgmkList = rwfgMapper.selectBgmkInfo(rwfg.getBgid());

		// 当前的任务分工
		List<RwfgVO> rwfgVOList = rwfgMapper.selectRwfgInfo(rwfg.getBgid());

		// 导入人员
		List<Long> userIdList = new ArrayList<>();
		if (zpbgzxptXwzjRoleId != null && zpbgzxptXwzjRoleId.length > 0) {
			for (int i = 0; i < zpbgzxptXwzjRoleId.length; i++) {
				List<User> tempList = userClient.listByRoleId(Long.valueOf(zpbgzxptXwzjRoleId[i])).getData();
				if (tempList != null && tempList.size() > 0) {
					for (User user : tempList) {
						userIdList.add(user.getId());
					}
				}
			}

		}

		// 数据处理
		for(RwfgVO bgmk: bgmkList){
			for(Long userId : userIdList){
				// 去重
				RwfgVO r = rwfgVOList.stream().filter(rwfgVO -> rwfgVO.getBgmkid().equals(bgmk.getBgmkid()) &&
						rwfgVO.getFglx() == 2 && rwfgVO.getRyid().equals(userId)).findFirst().orElse(null);
				if( r == null ){
					// 创建数据
					Rwfg rwfgInfo = new Rwfg();
					rwfgInfo.setId(null);
					rwfgInfo.setBgid(rwfg.getBgid());
					rwfgInfo.setBgmkid(bgmk.getBgmkid());
					rwfgInfo.setFglx(2);
					// 用户信息
					User user = userClient.userInfoById(userId).getData();
					if(user != null){
						rwfgInfo.setRyid(user.getId());
						rwfgInfo.setRyxm(user.getName());
						rwfgInfo.setDwid(Long.parseLong(user.getDeptId()));
						rwfgInfo.setDwmc(DeptCache.getDeptName(Long.parseLong(user.getDeptId())));
					}

					rwfgList.add(rwfgInfo);
				}
			}
		}
		if(rwfgList.size() == 0){
			throw new ServiceException("没有可导入数据！");
		}

		return this.saveBatch(rwfgList);
	}

}
