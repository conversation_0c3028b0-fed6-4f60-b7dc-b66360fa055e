package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.elasticsearch.feign.IZjjyClient;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.entity.Zzclssmk;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.service.IZzclssmkService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;
import com.xpaas.zpbg.wrapper.ZzclWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教学评价-自评报告-佐证材料 控制器
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zzcl")
@Api(value = "教学评价-自评报告-佐证材料", tags = "教学评价-自评报告-佐证材料接口")
public class ZzclController extends BaseController {

    private ZzclWrapper zzclWrapper;
    private IZzclService zzclService;
    private IZjjyClient zjjyClient;
    private IZzclssmkService iZzclssmkService;
    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入zzcl")
    @ApiLog("佐证材料-详情")
    public R<ZzclVO> detail(Zzcl zzcl) {
        return R.data(zzclService.getZzclDetail(zzcl));
    }

    /**
     * 查询的所有数据
     * 1658296607174098945
     * 1658296607174098945
     */
    @GetMapping("/allList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "所有", notes = "传入zzcl")
    @ApiLog("佐证材料-查询的所有数据")
    public R<IPage<ZzclVO>> allList(ZzclglVO zzcl, Query query) {
        zzcl.setScbj(0);
        LoginUser user = AuthUtil.getUser();
        long userid = user.getUserId();
        ZzclglVO zzclpar = new ZzclglVO();
        zzclpar.setSsmkid(zzcl.getSsmkid());
        zzclpar.setClmc(zzcl.getClmc());
        zzclpar.setPc(zzcl.getPc());
        IPage<ZzclVO> pages = zzclService.getZzclList(zzclpar,Condition.getPage(query));
        if( pages.getRecords().size()>0){
            for (ZzclVO zzclvo : pages.getRecords()) {
                Zzclgl zzclgl = new Zzclgl();
                long cjr = zzclvo.getCjr();
                if(userid!=cjr){
                    zzclvo.setIsploadinperson(true);
                }
                zzclgl.setBbid(zzcl.getBbid());
                zzclgl.setBgid(zzcl.getBgid());
                zzclgl.setBgmkid(zzcl.getBgmkid());
                zzclgl.setZzclid(zzclvo.getId());
                List<ZzclglVO> clLsit=zzclService.getQuote(zzclgl);
                if(clLsit.size()>0){
                    zzclvo.setIsquote(true);
                }
            }
        }
        return R.data(pages);
    }

    /**
     * 分页 教学评价-自评报告-佐证材料 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入zzcl")
    @ApiLog("佐证材料-分页")
    public R<IPage<ZzclVO>> list(Zzcl zzcl, Query query) {
        zzcl.setScbj(0);
        IPage<Zzcl> pages = zzclService.page(Condition.getPage(query), Condition.getQueryWrapper(zzcl));
        return R.data(zzclWrapper.pageVO(pages));
    }

    /**
     * 根据材料名称查询所有的相同名称的材料
     */
    @GetMapping("/listByClmc")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据材料名称查询所有的相同名称的材料", notes = "传入zzcl")
    @ApiLog("根据材料名称查询所有的相同名称的材料")
    public R listByClmc(Zzcl zzcl) {
        return R.data(zzclService.listByClmc(zzcl));
    }

    /**
     * 自定义分页 教学评价-自评报告-佐证材料
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入zzcl")
    @ApiLog("佐证材料-自定义分页")
    public R<IPage<ZzclVO>> page(ZzclVO zzcl, Query query) {
        zzcl.setScbj(0);
        IPage<ZzclVO> pages = zzclService.selectZzclPage(Condition.getPage(query), zzcl);
        return R.data(zzclWrapper.wrapperPageVO(pages));
    }

    /**
     * 新增 教学评价-自评报告-佐证材料
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入zzcl")
    @ApiLog("佐证材料-新增")
    public R save(@Valid @RequestBody ZzclVO zzclVO) {
        //获取被覆盖的材料id判断是否为空,如果不为空则把新数据信息赋值给被覆盖数据之后被覆盖数据执行编辑
        String oldId = zzclVO.getOldId();
        if(Func.isNotEmpty(oldId)){//如果不为空则需要修改原来的材料
            Zzcl byId = zzclService.getById(oldId);
            ZzclVO zzclVO1 = zzclWrapper.entityVO(byId);
            zzclVO1.setSsmk(zzclVO.getSsmk());
            zzclVO1.setWjlj(zzclVO.getWjlj());
            Long ssmk = Long.parseLong(zzclVO.getSsmk());
            List<Zzclssmk> list = iZzclssmkService.list(new QueryWrapper<Zzclssmk>().lambda().eq(Zzclssmk::getScbj, "0").eq(Zzclssmk::getZzclid, oldId));
            List<Long> mkids = new ArrayList<>();
            if(Func.isNotEmpty(list) && list.size()>0){
                mkids = list.stream().map(m -> m.getMkid()).collect(Collectors.toList());
            }
            if(!mkids.contains(ssmk)){
                mkids.add(ssmk);
            }
            Long[] mkidsNew = mkids.toArray(new Long[0]);
            zzclVO1.setMkidList(mkidsNew);
            return update(zzclVO1);
        }

        boolean isSavesuccess = zzclService.save(zzclVO) && zzclService.saveZZCLSSMK(zzclVO);
        if(isSavesuccess){
            new Thread(() -> {
                zzclService.requestZjjyApi(zzclVO.getId(),zzclVO.getWjlj(),zzclVO.getCmm(),zzclVO.getClmc(),1);
            }).start();

        }
        return R.status(isSavesuccess);
    }

    /**
     * 修改 教学评价-自评报告-佐证材料
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入zzcl")
    @ApiLog("佐证材料-修改")
    public R update(@Valid @RequestBody ZzclVO zzclVO) {
        boolean b = zzclService.updateById(zzclVO) && zzclService.saveZZCLSSMK(zzclVO);
        if(b){
            new Thread(() -> {
                zzclService.requestZjjyApi(zzclVO.getId(),zzclVO.getWjlj(),zzclVO.getCmm(),zzclVO.getClmc(),2);
            }).start();
        }
        return R.status(b);
    }

    /**
     * 修改 教学评价-自评报告-佐证材料批次
     */
    @PostMapping("/updatePc")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改批次", notes = "传入zzcl")
    @ApiLog("佐证材料-修改")
    public R updatePc(@Valid @RequestBody ZzclVO zzclVO) {
        ZzclVO vo = new ZzclVO();
        vo.setId(zzclVO.getId());
        vo.setPc(zzclVO.getPc());
        boolean b = zzclService.updateById(vo);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-佐证材料 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入zzcl")
    @ApiLog("佐证材料-新增或修改")
    public R submit(@Valid @RequestBody ZzclVO zzcl) {
        if (zzclService.checkZzcl(zzcl)) {
            return R.fail("该材料名称已存在，请修改");
        }
        return R.status(zzclService.saveOrUpdate(zzcl) && zzclService.saveZZCLSSMK(zzcl));
    }

    /**
     * 删除 教学评价-自评报告-佐证材料
     */
    @PostMapping("/deleteZzcl")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("佐证材料-逻辑删除")
    public R deleteZzcl(@Valid @RequestParam ZzclVO zzcl) {
        String clid = zzcl.getId()+"";

        boolean b = zzclService.deleteLogic(Func.toLongList(clid));
        if(b){
            new Thread(() -> {
                zjjyClient.deleteDocument(clid);
            }).start();
        }
        return R.status(b);
    }
    /**
     * 删除 教学评价-自评报告-佐证材料
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("佐证材料-逻辑删除")
    public R remove(@RequestBody BaseDeleteVO deleteVO) {
        boolean b = zzclService.deleteLogic(Func.toLongList(deleteVO.getIds()));
        if(b){
            new Thread(() -> {
                zjjyClient.deleteDocument(deleteVO.getIds());
            }).start();
        }
        return R.status(b);
    }
    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("佐证材料-高级查询")
    public R<IPage<ZzclVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Zzcl> queryWrapper = Condition.getQueryWrapper(map, Zzcl.class);
        IPage<Zzcl> pages = zzclService.page(Condition.getPage(query), queryWrapper);
        return R.data(zzclWrapper.pageVO(pages));
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("佐证材料-导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<Zzcl> queryWrapper = Condition.getQueryWrapper(map, Zzcl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<Zzcl> list = zzclService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Zzcl.class);
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    @ApiLog("佐证材料-导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Zzcl> list = ExcelUtil.read(file, Zzcl.class);
        //TODO 此处需要根据具体业务添加代码
        zzclService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("佐证材料-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Zzcl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Zzcl> list = zzclService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        //TODO 此处需要根据具体业务添加代码
        ExcelUtil.export(response, "Zzcl导入模板", "Zzcl导入模板", columnFiledNames, list, Zzcl.class);
    }
}
