package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zzclssmk;
import com.xpaas.zpbg.vo.ZzclssmkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料所属模块 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Repository
public interface ZzclssmkMapper extends BaseMapper<Zzclssmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zzclssmk
	 * @return
	 */
	List<ZzclssmkVO> selectZzclssmkPage(IPage page, ZzclssmkVO zzclssmk);

	/**
	 * 获取佐证材料所属模块列表
	 * @param zzclId
	 * @return
	 */
	List<ZzclssmkVO> selectZzclssmkList(Long zzclId);

}
