package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Zjjdcl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-专家解读材料视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZjjdclVO对象", description = "教学评价-自评报告-专家解读材料")
public class ZjjdclVO extends Zjjdcl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;


}
