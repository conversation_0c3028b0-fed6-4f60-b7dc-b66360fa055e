-- 报告管理增加任务类型字段
ALTER TABLE t_dt_jxpj_zpbg_bggl ADD RWLX tinyint(4) NULL COMMENT '任务类型:字典编码rwlx';
update t_dt_jxpj_zpbg_bggl set RWLX=BGLX;
update t_dt_jxpj_zpbg_bggl set RWLX=1 where RWLX=2;


-- 任务类型字典
INSERT INTO xpaas_dict_biz (ID,ZHID,PARENT_ID,CODE,DICT_KEY,DICT_VALUE,SORT,REMARK,IS_SEALED,SCBJ,CJDW,DICT_TYPE,ANCESTORS,CJRQ,CJR,CJBM,GXR,GXRQ,ZT,MENU_ID) VALUES
	 (1902630361608851457,'000000',0,'rwlx',NULL,'任务类型',NULL,'',0,0,NULL,1,NULL,'2025-03-20 15:53:16',NULL,NULL,NULL,NULL,NULL,''),
	 (1902631361933578242,'000000',1902630361608851457,'rwlx','1','综合评价报告',1,NULL,0,0,NULL,0,'null,1902630361608851457',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
	 (1902631535212859394,'000000',1902630361608851457,'rwlx','3','专业评价报告',3,NULL,0,0,NULL,0,'null,1902630361608851457',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
	 (1902631546239684610,'000000',1902630361608851457,'rwlx','4','课程评价报告',4,NULL,0,0,NULL,0,'null,1902630361608851457',NULL,NULL,NULL,NULL,NULL,NULL,NULL);



-- 报告管理增加归档字段
ALTER TABLE t_dt_jxpj_zpbg_bggl ADD SFGD tinyint(1) DEFAULT 0 NULL COMMENT '是否归档:1-是;0-否;';



-- 是否归档菜单按钮
INSERT INTO xpaas_menu (ID,PARENT_ID,CODE,NAME,ALIAS,NO_MENU,`PATH`,SOURCE,SORT,CATEGORY,`ACTION`,IS_OPEN,REMARK,SCBJ,IS_THIRD,TARGET,VISIBLE,IS_CACHE,IS_FRAME,ZT,YYLY,YYBH,LB,SFWZXT,EXPLAINS,TEMPLATE,CJDW,IS_SHOW,ANCESTORS,PARAM_TYPE,PARAM_VALUE) VALUES
	 (1904048359091572737,1801147713704230923,'bggl_archive','归档',NULL,0,'','',11,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1801147713704230923',NULL,'[]'),
	 (1904048485851828226,1801147713704230923,'bggl_cancelArchive','取消归档',NULL,0,'','',12,2,0,1,'',0,'0',NULL,'0',0,NULL,NULL,'','',2,0,'','',NULL,NULL,'0,1798219302920359937,1801147713704230923',NULL,'[]');



-- 材料库增加年度和任务类型字段
ALTER TABLE t_dt_jxpj_zpbg_zzcl ADD ND varchar(10) NULL COMMENT '年度';
ALTER TABLE t_dt_jxpj_zpbg_zzcl ADD RWLX tinyint(4) NULL COMMENT '任务类型:字典编码rwlx';
ALTER TABLE t_dt_jxpj_zpbg_zccl ADD ND varchar(10) NULL COMMENT '年度';
ALTER TABLE t_dt_jxpj_zpbg_zccl ADD RWLX tinyint(4) NULL COMMENT '任务类型:字典编码rwlx';



-- 未引用老数据设置2025年任务类型综合
update t_dt_jxpj_zpbg_zzcl set ND='2024',RWLX=1 where id in (select id from (select t1.id from t_dt_jxpj_zpbg_zzcl t1 left join t_dt_jxpj_zpbg_zzclgl t2 on t1.ID=t2.ZZCLID where t2.ID is null) a);
update t_dt_jxpj_zpbg_zccl set ND='2024',RWLX=1 where id in (select id from (select t1.id from t_dt_jxpj_zpbg_zccl t1 left join t_dt_jxpj_zpbg_zcclgl t2 on t1.ID=t2.ZCCLID where t2.ID is null) a);



-- 优化资料库查询
CREATE INDEX t_dt_jxpj_zpbg_bbpz_COMMENT_ID_IDX USING BTREE ON t_dt_jxpj_zpbg_bbpz (COMMENT_ID);



-- 优化模块管理查询
CREATE INDEX t_dt_jxpj_zpbg_jdmkgl_BGMKID_IDX USING BTREE ON t_dt_jxpj_zpbg_jdmkgl (BGMKID);



-- 删除批次管理菜单
update xpaas_menu set SCBJ=0 where id=1873896682127142913;