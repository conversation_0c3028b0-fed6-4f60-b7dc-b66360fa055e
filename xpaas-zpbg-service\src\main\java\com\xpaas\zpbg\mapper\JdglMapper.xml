<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.JdglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="jdglResultMap" type="com.xpaas.zpbg.entity.Jdgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="JDMC" property="jdmc"/>
        <result column="KSSJ" property="kssj"/>
        <result column="ZXJSSJ" property="zxjssj"/>
        <result column="SYJSSJ" property="syjssj"/>
        <result column="PX" property="px"/>
        <result column="ZXJD" property="zxjd"/>
        <result column="SYJD" property="syjd"/>
        <result column="JDZT" property="jdzt"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="jdglResultMapVO" type="com.xpaas.zpbg.vo.JdglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="JDMC" property="jdmc"/>
        <result column="KSSJ" property="kssj"/>
        <result column="ZXJSSJ" property="zxjssj"/>
        <result column="SYJSSJ" property="syjssj"/>
        <result column="PX" property="px"/>
        <result column="ZXJD" property="zxjd"/>
        <result column="SYJD" property="syjd"/>
        <result column="JDZT" property="jdzt"/>
    </resultMap>

    <!-- 进度查询 -->
    <select id="selectJdglPage" resultType="com.xpaas.zpbg.vo.JdglVO">
        SELECT
        jdgl.*,
        count( DISTINCT jdmkgl.ID ) AS mkcount,
        count( DISTINCT CASE WHEN bgmkztjl.CZLX = 20 THEN bgmkztjl.BGMKID ELSE NULL END ) AS tjcount,
        count( DISTINCT CASE WHEN bgmkztjl.CZLX = 40 THEN bgmkztjl.BGMKID ELSE NULL END ) AS sycount
        FROM
        T_DT_JXPJ_ZPBG_JDGL jdgl
        LEFT JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdmkgl.JDGLID = jdgl.ID
        AND jdmkgl.SCBJ = 0
        LEFT JOIN t_dt_jxpj_zpbg_bgmkztjl bgmkztjl ON bgmkztjl.BGMKID = jdmkgl.BGMKID
        AND bgmkztjl.CJRQ &gt;= jdgl.KSSJ
        AND bgmkztjl.CJRQ &lt; DATE_ADD( jdgl.SYJSSJ, INTERVAL 1 DAY )
        AND bgmkztjl.SCBJ = 0
        WHERE
        jdgl.SCBJ = 0
        AND jdgl.bgid = #{jdgl.bgid}
        GROUP BY
        jdgl.ID
        ORDER BY
        jdgl.px,
        jdgl.ID
    </select>

    <!-- 报告信息取得 -->
    <select id="getHeaderInfo" resultType="com.xpaas.zpbg.vo.JdglVO">
        SELECT * FROM t_dt_jxpj_zpbg_jdgl WHERE SCBJ = 0 AND BGID = #{jdgl.bgid} AND KSSJ &lt;= CURDATE() AND CURDATE() &lt;= SYJSSJ
    </select>

    <!-- 报告模块取得 -->
    <select id="getBgmkInfo" resultType="com.xpaas.zpbg.vo.JdglVO">
        select ID,(CASE WHEN CMM is NOT NULL and CMM != '' THEN CMM ELSE MKMC end) AS MKMC,BGMKZT FROM T_DT_JXPJ_ZPBG_BGMK where scbj = 0 and BGID = #{bgid} ORDER BY PX,MKID DESC
    </select>

    <!-- 进度模块关联取得 -->
    <select id="getJdmkglInfo" resultType="com.xpaas.zpbg.vo.JdglVO">
        select BGMKID FROM T_DT_JXPJ_ZPBG_JDMKGL where scbj = 0 and JDGLID = #{id}
    </select>

    <!-- 进度名称存在查询 -->
    <select id="checkExistByInfo" resultType="int">
        select
        count(1)
        from
        T_DT_JXPJ_ZPBG_JDGL
        where
        JDMC = #{jdmc}
        and scbj = 0
        and bgid = #{bgid}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

<!-- 查询报告当前进度信息 -->
<!--    <select id="selectJdglByBggl" parameterType="list" resultMap="jdglResultMap">-->
<!--        SELECT-->
<!--            J.ID, J.BGID, J.JDMC, J.KSSJ, J.ZXJSSJ, J.SYJSSJ, J.PX-->
<!--        FROM-->
<!--            T_DT_JXPJ_ZPBG_JDGL j-->
<!--        INNER JOIN (-->
<!--            SELECT-->
<!--                j2.BGID,-->
<!--                MAX( j2.KSSJ ) AS NAX_KSSJ-->
<!--            FROM-->
<!--                T_DT_JXPJ_ZPBG_JDGL j2-->
<!--            WHERE-->
<!--                j2.SCBJ = 0-->
<!--              and j2.KSSJ >=  CURDATE()-->
<!--              AND j2.BGID in-->
<!--            <foreach collection="bgidList" item="bgid" open=" (" separator=", " close=") ">-->
<!--                #{bgid}-->
<!--            </foreach>-->
<!--            GROUP BY-->
<!--                BGID-->
<!--        ) t ON j.BGID = t.BGID-->
<!--           AND j.KSSJ = t.NAX_KSSJ-->
<!--    </select>-->
</mapper>
