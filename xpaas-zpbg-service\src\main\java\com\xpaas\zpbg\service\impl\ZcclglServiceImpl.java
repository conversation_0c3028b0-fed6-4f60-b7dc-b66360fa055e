package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.mapper.ZcclglMapper;
import com.xpaas.zpbg.service.IZcclglService;
import com.xpaas.zpbg.utils.ZpbgUtils;
import com.xpaas.zpbg.vo.ZcclglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-备查材料关联 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class ZcclglServiceImpl extends BaseServiceImpl<ZcclglMapper, Zcclgl> implements IZcclglService {

	@Override
	public IPage<ZcclglVO> selectZcclglPage(IPage<ZcclglVO> page, ZcclglVO zcclgl) {
		return page.setRecords(baseMapper.selectZcclglPage(page, zcclgl));
	}
	@Override
	public String sameGl(ZcclglVO zcclglVO) {
		List<String> msg = new ArrayList();

		if(zcclglVO.getSelectList() != null && zcclglVO.getSelectList().size() > 0) {
			for(Zcclgl item : zcclglVO.getSelectList()) {
				List<ZcclglVO> glList = baseMapper.sameGl(item);

				if(glList != null && glList.size() > 0) {
					List<String> glwz = new ArrayList();

					for(ZcclglVO vo : glList) {
						glwz.add(ZpbgUtils.convertGlwz(vo.getGlwz()));
					}
					boolean constainsFuillemets =item.getClmc().contains("《")||item.getClmc().contains("》");
					if(constainsFuillemets){
						msg.add("备查材料" + item.getClmc() + "：已在报告中" + Joiner.on("，").join(glwz) + "引用,确定要引用吗？");
					}else{
						msg.add("备查材料《" + item.getClmc() + "》：已在报告中" + Joiner.on("，").join(glwz) + "引用,确定要引用吗？");
					}
				}
			}
		}

		if(msg.size() > 0) {
			return Joiner.on("<br>").join(msg);
		} else {
			return "";
		}
	}
	@Override
	public int getMaxPx(){
		return baseMapper.getMaxPx();
	}
	@Override
	public int checkGlkey(ZcclglVO zcclglVO) {
		return baseMapper.checkGlkey(zcclglVO);
	}
	@Override
	public List<ZcclglVO> getzcclList(String bbid, List<String> glkeyList) {
		if(glkeyList.isEmpty()){
			return new ArrayList<>();
		}
        return baseMapper.getzcclList(bbid,glkeyList);
	}
	@Override
	public List<ZcclglVO>getZclglList(Zcclgl zcclgl){
		return baseMapper.getZclglList(zcclgl);
	}

	@Override
	public boolean updataPx(String id,String cjsj){
		return  baseMapper.updataPx(id,cjsj);
	}
}
