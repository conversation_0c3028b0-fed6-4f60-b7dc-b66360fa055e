<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BbwjnrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bbwjnrResultMap" type="com.xpaas.zpbg.entity.Bbwjnr">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BBID" property="bbid"/>
        <result column="WJNR" property="wjnr"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bbwjnrResultMapVO" type="com.xpaas.zpbg.vo.BbwjnrVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BBID" property="bbid"/>
        <result column="WJNR" property="wjnr"/>
    </resultMap>

    <select id="selectBbwjnrPage" resultMap="bbwjnrResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BBWJNR where scbj = 0
    </select>

    <select id="getByBbid" resultType="com.xpaas.zpbg.entity.Bbwjnr">
        select * from T_DT_JXPJ_ZPBG_BBWJNR where scbj = 0 and BBID = #{bbid} limit 0, 1
    </select>
</mapper>
