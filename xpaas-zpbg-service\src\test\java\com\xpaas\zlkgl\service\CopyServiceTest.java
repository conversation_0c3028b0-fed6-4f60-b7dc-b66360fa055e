package com.xpaas.zlkgl.service;

import com.xpaas.zlkgl.dto.CopyRequest;
import com.xpaas.zlkgl.dto.CopyResult;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 复制服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CopyServiceTest {

    @Autowired
    private ICopyService copyService;

    @Autowired
    private IZlglService zlglService;

    @Autowired
    private IFlglWjjService flglWjjService;

    @Autowired
    private IWxzlWjjService wxzlWjjService;

    @Test
    public void testCopyFile() {
        // 1. 创建测试文件
        Zlgl testFile = new Zlgl();
        testFile.setWjjId("test-folder-1");
        testFile.setZlMc("测试文件.pdf");
        testFile.setZlCmm("测试文件.pdf");
        testFile.setZlDz("/path/to/test.pdf");
        testFile.setPjLx("1");
        zlglService.save(testFile);

        // 2. 复制文件
        CopyResult result = copyService.copyFile(testFile.getId(), "test-folder-2", null);

        // 3. 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getNewId());
        assertEquals("测试文件.pdf副本", result.getNewName());

        // 4. 验证新文件存在
        Zlgl copiedFile = zlglService.getById(result.getNewId());
        assertNotNull(copiedFile);
        assertEquals("test-folder-2", copiedFile.getWjjId());
        assertEquals("测试文件.pdf副本", copiedFile.getZlMc());
    }

    @Test
    public void testCopyFlglFolder() {
        // 1. 创建测试文件夹
        FlglWjj testFolder = new FlglWjj();
        testFolder.setFjWjjId("parent-folder");
        testFolder.setWjjMc("测试文件夹");
        testFolder.setWjjLx("1"); // 普通文件夹
        testFolder.setPjLx("1");
        flglWjjService.save(testFolder);

        // 2. 在文件夹中创建测试文件
        Zlgl testFile = new Zlgl();
        testFile.setWjjId(testFolder.getId());
        testFile.setZlMc("文件夹内文件.doc");
        testFile.setZlCmm("文件夹内文件.doc");
        testFile.setPjLx("1");
        zlglService.save(testFile);

        // 3. 复制文件夹
        CopyResult result = copyService.copyFlglFolder(testFolder.getId(), "target-parent", null);

        // 4. 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getNewId());
        assertEquals("测试文件夹副本", result.getNewName());
        assertEquals(1, result.getCopiedFileCount());
        assertEquals(1, result.getCopiedFolderCount());

        // 5. 验证新文件夹存在
        FlglWjj copiedFolder = flglWjjService.getById(result.getNewId());
        assertNotNull(copiedFolder);
        assertEquals("target-parent", copiedFolder.getFjWjjId());
        assertEquals("测试文件夹副本", copiedFolder.getWjjMc());
    }

    @Test
    public void testCopyWxzlFolder() {
        // 1. 创建测试外校文件夹
        WxzlWjj testFolder = new WxzlWjj();
        testFolder.setFjWjjId("parent-folder");
        testFolder.setWjjMc("外校文件夹");
        testFolder.setWjjLx("1"); // 普通文件夹
        testFolder.setPjLx("1");
        wxzlWjjService.save(testFolder);

        // 2. 复制文件夹
        CopyResult result = copyService.copyWxzlFolder(testFolder.getId(), "target-parent", null);

        // 3. 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getNewId());
        assertEquals("外校文件夹副本", result.getNewName());

        // 4. 验证新文件夹存在
        WxzlWjj copiedFolder = wxzlWjjService.getById(result.getNewId());
        assertNotNull(copiedFolder);
        assertEquals("target-parent", copiedFolder.getFjWjjId());
        assertEquals("外校文件夹副本", copiedFolder.getWjjMc());
    }

    @Test
    public void testCopyExternalLinkFolder() {
        // 1. 创建外链文件夹
        FlglWjj externalFolder = new FlglWjj();
        externalFolder.setFjWjjId("parent-folder");
        externalFolder.setWjjMc("外链文件夹");
        externalFolder.setWjjLx("0"); // 外链文件夹
        externalFolder.setWbljDz("https://example.com");
        externalFolder.setPjLx("1");
        flglWjjService.save(externalFolder);

        // 2. 复制外链文件夹
        CopyResult result = copyService.copyFlglFolder(externalFolder.getId(), "target-parent", null);

        // 3. 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getNewId());
        assertEquals("外链文件夹副本", result.getNewName());
        assertEquals(0, result.getCopiedFileCount()); // 外链文件夹不复制子项
        assertEquals(1, result.getCopiedFolderCount());

        // 4. 验证新文件夹存在且保持外链属性
        FlglWjj copiedFolder = flglWjjService.getById(result.getNewId());
        assertNotNull(copiedFolder);
        assertEquals("0", copiedFolder.getWjjLx());
        assertEquals("https://example.com", copiedFolder.getWbljDz());
    }

    @Test
    public void testGenerateUniqueName() {
        // 1. 创建同名文件
        Zlgl existingFile = new Zlgl();
        existingFile.setWjjId("test-folder");
        existingFile.setZlMc("重复文件.txt");
        existingFile.setZlCmm("重复文件.txt");
        existingFile.setPjLx("1");
        zlglService.save(existingFile);

        // 2. 创建同名副本
        Zlgl existingCopy = new Zlgl();
        existingCopy.setWjjId("test-folder");
        existingCopy.setZlMc("重复文件.txt副本");
        existingCopy.setZlCmm("重复文件.txt副本");
        existingCopy.setPjLx("1");
        zlglService.save(existingCopy);

        // 3. 生成唯一名称
        String uniqueName = copyService.generateUniqueName("重复文件.txt", "test-folder", CopyRequest.CopyType.FILE);

        // 4. 验证结果
        assertEquals("重复文件.txt副本(1)", uniqueName);
    }

    @Test
    public void testCopyWithCustomName() {
        // 1. 创建测试文件
        Zlgl testFile = new Zlgl();
        testFile.setWjjId("test-folder");
        testFile.setZlMc("原始文件.pdf");
        testFile.setZlCmm("原始文件.pdf");
        testFile.setPjLx("1");
        zlglService.save(testFile);

        // 2. 使用自定义名称复制
        CopyResult result = copyService.copyFile(testFile.getId(), "target-folder", "自定义名称.pdf");

        // 3. 验证结果
        assertTrue(result.isSuccess());
        assertEquals("自定义名称.pdf", result.getNewName());

        // 4. 验证新文件
        Zlgl copiedFile = zlglService.getById(result.getNewId());
        assertEquals("自定义名称.pdf", copiedFile.getZlMc());
    }
}
