package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bbwjnr;
import com.xpaas.zpbg.vo.BbwjnrVO;

/**
 * 教学评价-自评报告-版本文件内容 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IBbwjnrService extends BaseService<Bbwjnr> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bbwjnr
	 * @return
	 */
	IPage<BbwjnrVO> selectBbwjnrPage(IPage<BbwjnrVO> page, BbwjnrVO bbwjnr);

	/**
	 * 保存文件内容
	 *
	 * @param bbid
	 * @param wjlj
	 * @return
	 */
	void saveWjnr(Long bbid, String wjlj);
}
