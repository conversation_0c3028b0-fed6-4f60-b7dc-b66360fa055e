package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-关键词检测情况实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_GJCJCQK")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Gjcjcqk对象", description = "教学评价-自评报告-关键词检测情况")
public class Gjcjcqk extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 检测记录ID
	*/
	@ExcelProperty("检测记录ID")
	@ApiModelProperty(value = "检测记录ID")
	@TableField("JCJLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long jcjlid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 关键词ID
	*/
	@ExcelProperty("关键词ID")
	@ApiModelProperty(value = "关键词ID")
	@TableField("GJCID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long gjcid;

	/**
	* 关键词名称
	*/
	@ExcelProperty("关键词名称")
	@ApiModelProperty(value = "关键词名称")
	@TableField("GJCMC")
	private String gjcmc;

	/**
	* 频次
	*/
	@ExcelProperty("频次")
	@ApiModelProperty(value = "频次")
	@TableField("PC")
	private Integer pc;



}
