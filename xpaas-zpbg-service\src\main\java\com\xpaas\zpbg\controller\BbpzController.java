package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Bbpz;
import com.xpaas.zpbg.service.IBbpzService;
import com.xpaas.zpbg.vo.BbpzGlCopyVO;
import com.xpaas.zpbg.vo.BbpzVO;
import com.xpaas.zpbg.wrapper.BbpzWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-版本批注 控制器
 * controller 入口
 * <AUTHOR>
 * @since 2024-09-02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bbpz")
@Api(value = "教学评价-自评报告-版本批注", tags = "教学评价-自评报告-版本批注接口")
public class BbpzController extends BaseController {
	private BbpzWrapper bbpzWrapper;
	private IBbpzService bbpzService;

	/**
	 * 获取详情数据
	 * @param bbpz bbpz实体
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入bbpz")
	@ApiLog("版本批注-详情")
	public R<BbpzVO> detail(Bbpz bbpz) {
		Bbpz detail = bbpzService.getOne(Condition.getQueryWrapper(bbpz));
		return R.data(bbpzWrapper.entityVO(detail));
	}

	/**
	 * 根据主键集合查询 教学评价-自评报告-版本批注 数据
	 * @param ids 主键集合
	 * <AUTHOR>  作者
	 * @since 2024-09-02 日期
	 */
	@GetMapping("/listByIds")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据IDs查询", notes = "传入ids")
	@ApiLog("版本批注-根据IDs查询")
	public R<List<BbpzVO>> listByIds(String ids) {
		List<Bbpz> listByIds = bbpzService.listByIds(Func.toLongList(ids));
		return R.data(bbpzWrapper.listVO(listByIds));
	}

	/**
	 * 根据条件查询 教学评价-自评报告-版本批注 数据
	 * @param bbpz bbpz实体
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入bbpz")
	@ApiLog("版本批注-列表")
	public R<List<BbpzVO>> list(Bbpz bbpz) {
		List<Bbpz> lists = bbpzService.list(Condition.getQueryWrapper(bbpz));
		return R.data(bbpzWrapper.listVO(lists));
	}

    /**
     * 分页查询 教学评价-自评报告-版本批注数据
	 * @param bbpz bbpz实体
	 * @param query 查询条件
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bbpz")
	@ApiLog("版本批注-分页")
    public R<IPage<BbpzVO>> page(BbpzVO bbpz, Query query) {
        IPage<Bbpz> pages = bbpzService.page(Condition.getPage(query), Condition.getQueryWrapper(bbpz));
        return R.data(bbpzWrapper.pageVO(pages));
    }

	/**
	 * 高级查询，界面字段的高级搜索
	 * (开发过程中根据需求调整精确查询或模糊查询)
	 * @param map 高级查询字段,请参考高级查询逻辑
	 * @param query 查询条件
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("版本批注-高级查询")
	public R<IPage<BbpzVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Bbpz> queryWrapper = Condition.getQueryWrapper(map, Bbpz.class);
		IPage<Bbpz> pages = bbpzService.page(Condition.getPage(query), queryWrapper);
		return R.data(bbpzWrapper.pageVO(pages));
	}



	/**
	 * 新增 教学评价-自评报告-版本批注
	 * @param bbpzVO bbpzVO实体
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入bbpz")
	@ApiLog("版本批注-新增")
	public R save(@Valid @RequestBody BbpzVO bbpzVO) {
		boolean b = bbpzService.save(bbpzVO);

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-版本批注
	 * 根据主键ID修改数据
	 * @param bbpzVO bbpzVO实体
	 * <AUTHOR>
	 * @since 2024-09-02
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入bbpz")
	@ApiLog("版本批注-修改")
	public R update(@Valid @RequestBody BbpzVO bbpzVO) {
		boolean b = bbpzService.updateById(bbpzVO);
		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-版本批注 (优先使用save或update接口)
	 * id存在的情况下进行更新操作，id不存在进行插入操作
	 * @param bbpz bbpz实体
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入bbpz")
	@ApiLog("版本批注-新增或修改")
	public R submit(@Valid @RequestBody Bbpz bbpz) {
		return R.status(bbpzService.saveOrUpdate(bbpz));
	}

	
	/**
	 * 删除 教学评价-自评报告-版本批注
	 * 根据主键ID集合逻辑删除数据
	 * @param ids 主键集合
	 * <AUTHOR> 作者
	 * @since 2024-09-02 集合
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("版本批注-逻辑删除")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		boolean b = bbpzService.deleteLogic(Func.toLongList(ids));
		return R.status(b);
	}

	
	/**
	 * 导出Excel
	 * @param response 返回响应
	 * @param fileName 文件名
	 * @param sheetName sheet页名称
	 * @param columnNames 要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段
	 * @param ids 要导出的id,多个id用逗号连接.如果为空,将导出全部数据
	 * @param ascs 正排序字段,多个字段用逗号连接
	 * @param descs 倒排序字段,多个字段用逗号连接
	 * @param map 高级查询字段,请参考高级查询逻辑
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("版本批注-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Bbpz> queryWrapper = Condition.getQueryWrapper(map, Bbpz.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Bbpz> list = bbpzService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Bbpz.class);
	}


	/**
	 * 导入Excel
	 * @param file 文件名
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("版本批注-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Bbpz> list = ExcelUtil.read(file, Bbpz.class);
		//TODO 此处需要根据具体业务添加代码
		bbpzService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 * @param response 返回的响应数据
	 * @param columnNames 导入模板的字段
	 * <AUTHOR> 作者
	 * @since 2024-09-02 创建日期
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("版本批注-下载导入模板")
	public void template(HttpServletResponse response,
                         @ApiParam(value = "要导出的字段,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames) {
		QueryWrapper<Bbpz> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Bbpz> list = bbpzService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		ExcelUtil.export(response, "Bbpz导入模板", "Bbpz导入模板",columnFiledNames, list, Bbpz.class);
	}

	/**
	 * 复制批注关联引用
	 *
	 * @param params 传入复制版本、批注ID
	 * <AUTHOR> 作者
	 * @since 2024-09-02 日期
	 */
	@PostMapping("/bbpzGlCopy")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "复制批注关联引用", notes = "传入复制版本、批注ID")
	@ApiLog("版本批注-复制批注关联引用")
	public R bbpzGlCopy(@RequestBody(required = false) List<BbpzGlCopyVO> params) {
		return R.status(bbpzService.bbpzGlCopy(params));
	}

}
