package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Tree;
import com.xpaas.zpbg.vo.TreeVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自评报告-文章管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Repository
public interface TreeMapper extends BaseMapper<Tree> {

	/**
	 * 取得一级模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getMkTreeOne(TreeVO treeVO);

	/**
	 * 取得二级模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getMkTreeTwo(TreeVO treeVO);

	/**
	 * 取得三级模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getMkTreeThree(TreeVO treeVO);

	/**
	 * 取得一级报告模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getBgTreeOne(TreeVO treeVO);

	/**
	 * 取得二级报告模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getBgTreeTwo(TreeVO treeVO);

	/**
	 * 取得三级报告模块树
	 *
	 * @param treeVO
	 * @return
	 */
	List<TreeVO> getBgTreeThree(TreeVO treeVO);
}
