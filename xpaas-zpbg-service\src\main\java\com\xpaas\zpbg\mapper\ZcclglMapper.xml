<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZcclglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zcclglResultMap" type="com.xpaas.zpbg.entity.Zcclgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="ZCCLID" property="zcclid"/>
        <result column="CLMC" property="clmc"/>
        <result column="BCSM" property="bcsm"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="GLWZ" property="glwz"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zcclglResultMapVO" type="com.xpaas.zpbg.vo.ZcclglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="ZCCLID" property="zcclid"/>
        <result column="CLMC" property="clmc"/>
        <result column="BCSM" property="bcsm"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="GLWZ" property="glwz"/>
        <result column="XH" property="xh"/>
    </resultMap>

    <select id="selectZcclglPage" resultMap="zcclglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZCCLGL ZCCLGL INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on ZCCLGL.BBID = bbpz.BBID and ZCCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0 where ZCCLGL.scbj = 0
    </select>

    <select id="checkGlkey" resultType="int">
        SELECT
        ( SELECT count( 1 ) FROM T_DT_JXPJ_ZPBG_ZCCLGL ZCCLGL
          INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on ZCCLGL.BBID = bbpz.BBID and ZCCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
          WHERE ZCCLGL.SCBJ = 0 and ZCCLGL.BBID = #{bbid} and ZCCLGL.BGID = #{bgid} and ZCCLGL.BGMKID = #{bgmkid}
        <if test="glkeyList != null and glkeyList.size() > 0">
            <foreach collection="glkeyList" item="item" open=" AND GLKEY IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="getzcclList" resultMap="zcclglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZCCLGL ZCCLGL INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on ZCCLGL.BBID = bbpz.BBID and ZCCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0

         where ZCCLGL.scbj = 0 AND ZCCLGL.bbid = #{bbid}
        <foreach collection="glkeyList" item="item" open=" AND ZCCLGL.GLKEY IN (" separator="," close=")">
            #{item}
        </foreach>
        order by ZCCLGL.cjrq
    </select>
    <select id="sameGl" resultType="com.xpaas.zpbg.vo.ZcclglVO">
        SELECT
            *
        FROM
            T_DT_JXPJ_ZPBG_ZCCLGL zcgl
               INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on zcgl.BBID = bbpz.BBID and zcgl.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE
            zcgl.BGID = #{bgid}
            AND zcgl.BGMKID = #{bgmkid}
            AND zcgl.BBID = #{bbid}
            AND zcgl.ZCCLID = #{zcclid}
            AND zcgl.GLKEY != #{glkey}
            AND zcgl.SCBJ = 0
    </select>
    <select id="getZclglList" resultMap="zcclglResultMapVO">
        select t2.*
        from
            (
                select
                    t1.*
                     ,(select @rownum:=@rownum+1) xh
                from
                (
                    SELECT
                        zcgl.*,
                        bbpz.px as bbpzpx,
                        (select @rownum:=0) r
                    from
                        T_DT_JXPJ_ZPBG_ZCCLGL zcgl
                    INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on zcgl.BBID = bbpz.BBID and zcgl.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
                    WHERE
                        zcgl.BBID =#{bbid}
                    AND zcgl.BGMKID =#{bgmkid}
                    AND zcgl.BGID =#{bgid}
                    AND zcgl.SCBJ = 0
                )  t1
        ) t2
        <where>
            <if test="glkey != null and glkey!=''">
                AND t2.GLKEY = #{glkey}
            </if>
        </where>
        ORDER BY t2.bbpzpx,t2.px
    </select>

    <select id="getMaxPx" resultType="int">
        select IFNULL(Max(ZCCLGL.px), 0) AS MaxPx from T_DT_JXPJ_ZPBG_ZCCLGL ZCCLGL
    </select>
    <update id="updataPx">
        update T_DT_JXPJ_ZPBG_ZCCLGL set px = #{newpx} where id = #{zcid} and SCBJ = 0
    </update>

    <select id="getQuote" resultType="com.xpaas.zpbg.vo.ZcclglVO">
        SELECT
            *
        FROM
            T_DT_JXPJ_ZPBG_ZCCLGL zcgl
        WHERE
            zcgl.BGID = #{bgid}
            AND zcgl.BGMKID = #{bgmkid}
            AND zcgl.BBID = #{bbid}
            AND zcgl.ZCCLID = #{zcclid}
            AND zcgl.SCBJ = 0
    </select>

    <delete id="deleteBatchByIds" parameterType="list">
        DELETE FROM  T_DT_JXPJ_ZPBG_ZCCLGL zcgl  WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
