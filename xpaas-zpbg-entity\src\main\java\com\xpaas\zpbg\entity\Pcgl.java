package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-批次管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_PCGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Pcgl对象", description = "教学评价-自评报告-批次管理")
public class Pcgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 批次名称
	*/
	@ExcelProperty("批次名称")
	@ApiModelProperty(value = "批次名称")
	@TableField("PCMC")
	private String pcmc;

	/**
	* 展示状态
	*/
	@ExcelProperty("展示状态")
	@ApiModelProperty(value = "展示状态")
	@TableField("ZSZT")
	private Integer zszt;

}
