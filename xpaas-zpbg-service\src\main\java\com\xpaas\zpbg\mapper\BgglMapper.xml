<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BgglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bgglResultMap" type="com.xpaas.zpbg.entity.Bggl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGMC" property="bgmc"/>
        <result column="ND" property="nd"/>
        <result column="MKLX" property="mklx"/>
        <result column="PX" property="px"/>
        <result column="BGZT" property="bgzt"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BZ" property="bz"/>
        <result column="YSBG" property="ysbg"/>
        <result column="PCID" property="pcid"/>
        <result column="PCMC" property="pcmc"/>
        <result column="BGLX" property="bglx"/>
        <result column="BGGN" property="bggn"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bgglResultMapVO" type="com.xpaas.zpbg.vo.BgglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGMC" property="bgmc"/>
        <result column="ND" property="nd"/>
        <result column="MKLX" property="mklx"/>
        <result column="PX" property="px"/>
        <result column="BGZT" property="bgzt"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="BZ" property="bz"/>
        <result column="YSBG" property="ysbg"/>
        <result column="PCID" property="pcid"/>
        <result column="PCMC" property="pcmc"/>
        <result column="BGLX" property="bglx"/>
        <result column="BGGN" property="bggn"/>
    </resultMap>


    <!-- 通用查询映射结果 -->
    <resultMap id="bgjdResultMap" type="com.xpaas.zpbg.vo.BgjdVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGMC" property="bgmc"/>
        <result column="ND" property="nd"/>
        <result column="JDMC" property="jdmc"/>
        <result column="ZXJSSJ" property="zxjssj"/>
        <result column="SYJSSJ" property="syjssj"/>
        <result column="PCID" property="pcid"/>
        <result column="PCMC" property="pcmc"/>
    </resultMap>

    <!-- 自定义分页结果 -->
    <select id="selectBgglPage" resultMap="bgglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BGGL bggl where scbj = 0 order by bggl.PX,bggl.id desc
    </select>

    <!-- 查询报告进度信息 -->
    <select id="selectBgjd" resultType="com.xpaas.zpbg.vo.BgjdVO">
        select bggl.ID,bggl.BGMC,bggl.nd,bggl.JDGLID,jdgl.JDMC,jdgl.KSSJ,jdgl.ZXJSSJ,jdgl.SYJSSJ,temp.rwfgsl,mksl.glmksl,bggl.cjrq,bggl.mklx,bggl.ysbg
        ,bggl.PCID,bggl.SFGD
        from  T_DT_JXPJ_ZPBG_BGGL bggl
        left join T_DT_JXPJ_ZPBG_JDGL jdgl
        on bggl.ID = jdgl.BGID and bggl.JDGLID = jdgl.id
        left join (select count(1) as rwfgsl,bgid from t_dt_jxpj_zpbg_rwfg where scbj = 0 group by bgid) temp
        on temp.bgid = bggl.id
        LEFT JOIN (
        SELECT
        jdgl.bgid AS bgid,
        sum( mkgl.mksl ) AS glmksl
        FROM
        T_DT_JXPJ_ZPBG_JDGL jdgl
        LEFT JOIN ( SELECT count( 1 ) AS mksl, jdglid FROM t_dt_jxpj_zpbg_jdmkgl WHERE scbj = 0 GROUP BY jdglid ) mkgl ON mkgl.jdglid = jdgl.id
        GROUP BY
        jdgl.bgid
        ) mksl ON mksl.bgid = bggl.id
        WHERE bggl.scbj = 0
        <if test="bggl.id != null and bggl.id != ''">and bggl.id = #{bggl.id}</if>
        <if test="bggl.bgmc != null and bggl.bgmc != ''">and bggl.bgmc like concat('%', #{bggl.bgmc}, '%')</if>
        order by bggl.PX,bggl.id desc
    </select>

    <!-- 查询指定报告信息 -->
    <select id="selectBgData" parameterType="String" resultMap="bgglResultMapVO">
         select * from T_DT_JXPJ_ZPBG_BGGL bggl where scbj = 0 and ID = #{bgid}
    </select>

    <!-- 报告查询 -->
    <select id="searchBggl" resultType="com.xpaas.zpbg.vo.BgglVO">
        SELECT
        	bggl.*
        FROM
        	t_dt_jxpj_zpbg_bggl bggl
        	INNER JOIN t_dt_jxpj_zpbg_jdgl jdgl ON bggl.id = jdgl.bgid
        	AND jdgl.SCBJ = 0
        	INNER JOIN t_dt_jxpj_zpbg_jdmkgl jdmkgl ON jdgl.id = jdmkgl.jdglid
        	AND jdmkgl.SCBJ = 0
        WHERE
        	bggl.SCBJ = 0
        GROUP BY
        	bggl.id
        ORDER BY
        	bggl.px,
        	bggl.id desc
    </select>
</mapper>
