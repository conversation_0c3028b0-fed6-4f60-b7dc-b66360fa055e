package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-模块管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_MKGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Mkgl对象", description = "教学评价-自评报告-模块管理")
public class Mkgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 模块名称
	*/
	@ExcelProperty("模块名称")
	@ApiModelProperty(value = "模块名称")
	@TableField("MKMC")
	private String mkmc;

	/**
	* 模块类型
	*/
	@ExcelProperty("模块类型")
	@ApiModelProperty(value = "模块类型")
	@TableField("MKLX")
	private Integer mklx;

	/**
	* 重命名
	*/
	@ExcelProperty("重命名")
	@ApiModelProperty(value = "重命名")
	@TableField("CMM")
	private String cmm;

	/**
	* 是否为封面
	*/
	@ExcelProperty("是否为封面")
	@ApiModelProperty(value = "是否为封面")
	@TableField("SFWFM")
	private Integer sfwfm;

	/**
	* 指标来源
	*/
	@ExcelProperty("指标来源")
	@ApiModelProperty(value = "指标来源")
	@TableField("ZBLY")
	private Integer zbly;

	/**
	* 一级指标ID
	*/
	@ExcelProperty("一级指标ID")
	@ApiModelProperty(value = "一级指标ID")
	@TableField("YJZBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long yjzbid;

	/**
	* 一级指标
	*/
	@ExcelProperty("一级指标")
	@ApiModelProperty(value = "一级指标")
	@TableField("YJZB")
	private String yjzb;

	/**
	* 二级指标ID
	*/
	@ExcelProperty("二级指标ID")
	@ApiModelProperty(value = "二级指标ID")
	@TableField("EJZBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long ejzbid;

	/**
	* 二级指标
	*/
	@ExcelProperty("二级指标")
	@ApiModelProperty(value = "二级指标")
	@TableField("EJZB")
	private String ejzb;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 数据来源
	*/
	@ExcelProperty("数据来源")
	@ApiModelProperty(value = "数据来源")
	@TableField("SJLY")
	private Integer sjly;

	/**
	 * 标准体系ID
	 */
	@ExcelProperty("标准体系ID")
	@ApiModelProperty(value = "标准体系ID")
	@TableField("BZTXID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bztxid;

	/**
	 * 模版文件KEY
	 */
	@ExcelProperty("模版文件KEY")
	@ApiModelProperty(value = "模版文件KEY")
	@TableField("MBWJKEY")
	private String mbwjkey;

	/**
	* 模版文件路径
	*/
	@ExcelProperty("模版文件路径")
	@ApiModelProperty(value = "模版文件路径")
	@TableField("MBWJLJ")
	private String mbwjlj;



}
