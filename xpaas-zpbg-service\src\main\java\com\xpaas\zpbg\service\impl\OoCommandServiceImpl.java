package com.xpaas.zpbg.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xpaas.system.feign.IParamClient;
import com.xpaas.zpbg.onlyOffice.managers.jwt.JwtManager;
import com.xpaas.zpbg.service.IOoCommandService;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * onlyoffice命令服务
 *
 * <AUTHOR>
 * @date 2024/07/11 10:45
 **/
@Slf4j
@Service
public class OoCommandServiceImpl implements IOoCommandService {

    private String docserviceUrlCommand="coauthoring/CommandService.ashx";

    private String documentJwtHeader = "Authorization";

    @Autowired
    private JwtManager jwtManager;

    @Autowired
    private ObjectMapper objectMapper;

    private final JSONParser parser = new JSONParser();

    @Autowired
    IParamClient paramClient;

    public String commandRequest(final Map<String, Object> params,
                               final Map<String,String> userdata) throws IOException, ParseException {  // create a command request
        log.info("调用onlyoffice："+params);
        String documentCommandUrl = paramClient.getByParamKey("env.onlyOfficeServerRoot").getData().getParamValue() + docserviceUrlCommand;

        URL url = new URL(documentCommandUrl);
        java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

        if (userdata != null) {
            params.put("userdata", userdata);
        }

        String headerToken;
        // check if a secret key to generate token exists or not
        if (jwtManager.tokenEnabled() && jwtManager.tokenUseForRequest()) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("payload", params);
            headerToken = jwtManager.createToken(payloadMap);  // encode a payload object into a header token

            // add a header Authorization with a header token and Authorization prefix in it
            connection.setRequestProperty(documentJwtHeader.equals("")
                    ? "Authorization" : documentJwtHeader, "Bearer " + headerToken);

            String token = jwtManager.createToken(params);  // encode a payload object into a body token
            params.put("token", token);
        }

        String bodyString = objectMapper.writeValueAsString(params);

        byte[] bodyByte = bodyString.getBytes(StandardCharsets.UTF_8);

        connection.setRequestMethod("POST");  // set the request method
        connection
                .setRequestProperty("Content-Type", "application/json; charset=UTF-8");  // set the Content-Type header
        connection.setDoOutput(true);  // set the doOutput field to true
        connection.connect();

        try (OutputStream os = connection.getOutputStream()) {
            os.write(bodyByte);  // write bytes to the output stream
        }

        InputStream stream = connection.getInputStream();  // get input stream

        if (stream == null) {
            throw new RuntimeException("Could not get an answer");
        }

        String jsonString = convertStreamToString(stream);  // convert stream to json string
        connection.disconnect();

        JSONObject response = convertStringToJSON(jsonString);  // convert json string to json object
        // todo: Add errors ENUM
        String responseCode = response.get("error").toString();
        log.info("调用onlyoffice服务返回值：" + response.toJSONString());
        switch (responseCode) {
            case "0":
            case "4":
                break;
            default:
                throw new RuntimeException("调用onlyOffice服务异常" + responseCode);
        }
        return responseCode;
    }

    private String convertStreamToString(final InputStream stream) throws IOException {
        InputStreamReader inputStreamReader = new InputStreamReader(stream);  // create an object to get incoming stream
        StringBuilder stringBuilder = new StringBuilder();  // create a string builder object

        // create an object to read incoming streams
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String line = bufferedReader.readLine();  // get incoming streams by lines

        while (line != null) {
            stringBuilder.append(line);  // concatenate strings using the string builder
            line = bufferedReader.readLine();
        }

        return stringBuilder.toString();
    }

    private JSONObject convertStringToJSON(final String jsonString) throws ParseException {
        Object obj = parser.parse(jsonString);  // parse json string
        return (JSONObject) obj;
    }
}
