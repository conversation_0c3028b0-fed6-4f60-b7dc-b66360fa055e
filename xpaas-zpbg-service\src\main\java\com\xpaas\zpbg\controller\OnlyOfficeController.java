package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.dto.HeaderValueDTO;
import com.xpaas.zpbg.service.IOnlyOfficeService;
import com.xpaas.zpbg.service.IOoCommandService;
import com.xpaas.zpbg.vo.OnlyOfflceTreeVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.parser.ParseException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 * onLineOffice 控制器
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/onlyOffice")
public class OnlyOfficeController extends BaseController {

    private IOnlyOfficeService iOnlyOfficeService;

    private IOoCommandService ooCommandService;

    /**
     * 获取头数据
     */
    @GetMapping("/header")
    @ApiOperationSupport(order = 2)
    @ApiLog("撰写平台-详情")
    public HeaderValueDTO getHeaderValue(String bbglId,String bgid,String bgmkid) {
        HeaderValueDTO result = iOnlyOfficeService.getHeaderValue(bbglId,bgid,bgmkid);
        return result;
    }

    /**
     * 获取模块树
     */
    @GetMapping("/getMkTree")
    @ApiOperationSupport(order = 3)
    @ApiLog("模块树")
    public R<List<OnlyOfflceTreeVO>> getMkTree(String bbglId, String bgId, String bgmkId, String role) {
        HeaderValueDTO dto = new HeaderValueDTO();
        if (!StringUtil.isEmpty(bbglId)) {
            dto = iOnlyOfficeService.getJdglIdValue(bbglId);
            if (!StringUtil.isEmpty(bgmkId)) {
                dto.setBgmkid(bgmkId);
            }
        } else {
//            bbglId = iOnlyOfficeService.getMainBbIdByBgId(bgId);
//            dto = iOnlyOfficeService.getJdglIdValue(bbglId);
//            dto.setBgmkid("");
//
//            if (!StringUtil.isEmpty(bgmkId)) {
//                dto.setBgmkid(bgmkId);
//            }
            dto.setBgid(bgId);
            dto.setBgmkid("");
        }

        List<OnlyOfflceTreeVO> result = null;
        if ("1".equals(dto.getBblx()) || !StringUtil.isEmpty(bgId)) {
            result = iOnlyOfficeService.getMkTreeValue(dto, role);
        } else {
            // 查看历史版本
            result = iOnlyOfficeService.getBbAsMkTree(bbglId);
        }

        return R.data(result);
    }


    /**
     * onlyoffice回调
     *
     * @param param   自定义参数
     *                mode mbgl模版管理 zx撰写
     *                mkid 模块id
     * @param request onlyoffice回调参数
     */
    @PostMapping("callBack")
    @ApiOperationSupport(order = 4)
    @ApiLog("onlyoffice-回调")
    public Map<String, Object> callBack(@RequestParam Map<String, String> param, HttpServletRequest request) {
        Map<String, Object> result = iOnlyOfficeService.callBack(param, request);
        return result;
    }

    /**
     * 触发保存回调
     *
     * @param params
     * @return
     */
    @ApiOperationSupport(order = 5)
    @ApiLog("onlyoffice-触发onlyoffice强制保存")
    @PostMapping("triggerForceSave")
    public R<String> triggerForceSave(@RequestBody Map<String, Object> params) {
        try {
            params.put("c", "forcesave");
            ooCommandService.commandRequest(params, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("操作失败");
        }
        return R.data(0, "", "");
    }

    /**
     * 保存文档(暂存、提交、专家审核提交)
     *
     * @param params
     * @return
     */
    @ApiOperationSupport(order = 5)
    @ApiLog("onlyoffice-保存文档")
    @PostMapping("saveDoc")
    public String saveDoc(@RequestBody Map<String, String> params) {

        Map<String,Object> commandParams = new HashMap<>();
        commandParams.put("c","forcesave");
        commandParams.put("key",params.get("key"));

        try {
            String res = ooCommandService.commandRequest(commandParams, params);
            if("4".equals(res)){
                iOnlyOfficeService.updateNewBb(params);
                if("tj".equals(params.get("operate"))){
                    iOnlyOfficeService.addTjCache(params.get("key"));
                    // 踢掉提交者之外的人
                    Map<String, Object> dropParam = new HashMap<>();
                    dropParam.put("c", "info");
                    dropParam.put("key", params.get("key"));
                    Map<String, String> userdata = new HashMap<>();
                    userdata.put("operate", "drop");
                    userdata.put("tjUser", String.valueOf(AuthUtil.getUser().getUserId()));
                    try{
                        ooCommandService.commandRequest(dropParam, userdata);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }

            }
            return res;
        } catch (ParseException | IOException e) {
            log.error(e.getMessage());
            return "";
        }
    }

    /**
     * 获取被踢下线信息
     * @param params
     * @return
     */
    @GetMapping("getDropMsg")
    @ApiOperationSupport(order = 8)
    @ApiLog("onlyoffice-获取被踢下线提示信息")
    public R<String> getDropMsg(@RequestParam Map<String,Object> params){
        String msg = iOnlyOfficeService.getDropMsg(params);
        return R.data(0,"",msg);
    }

    /**
     * 获取批注信息
     * @return
     */
    @GetMapping("getCommentInfo")
    @ApiOperationSupport(order = 9)
    @ApiLog("onlyoffice-获取批注信息")
    public Map<String,String> getCommentInfo(@RequestParam String bbid, String commentSep){
        Map<String,String> result = new HashMap<>();
        if(!StringUtil.isEmpty(commentSep)){
            List<String> list = Arrays.asList(commentSep.split(","));
            for (String s : list) {
                result.put(s,iOnlyOfficeService.getCommentText(bbid, Collections.singletonList(s)));
            }
        }
        return result;
    }

    /**
     * 获取批注信息(卓马用)
     * @return
     */
    @GetMapping("getCommentInfoZm")
    @ApiOperationSupport(order = 12)
    @ApiLog("onlyoffice-获取批注信息（专家进院用）")
    public String getCommentInfoZm(@RequestParam String bgmkId, String commentId){
        if(!StringUtil.isEmpty(commentId)){
            List<String> list = Arrays.asList(commentId.split(","));
            return iOnlyOfficeService.getCommentTextZm(bgmkId, list);
        } else {
            return "";
        }
    }

    /**
     * 插入批注
     * @return
     */
    @PostMapping("addCommentOrder")
    @ApiOperationSupport(order = 10)
    @ApiLog("onlyoffice-插入批注")
    public String addCommentOrder(@RequestBody Map<String, Object> params){
        return iOnlyOfficeService.addCommentOrder(params);
    }

    /**
     * 删除批注
     * @return
     */
    @PostMapping("delComment")
    @ApiOperationSupport(order = 11)
    @ApiLog("onlyoffice-删除批注")
    public String delComment(@RequestBody Map<String, String> params){
        return iOnlyOfficeService.delComment(params);
    }
}
