package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Bgmkztjl;
import com.xpaas.zpbg.service.IBgmkztjlService;
import com.xpaas.zpbg.vo.BgmkztjlVO;
import com.xpaas.zpbg.wrapper.BgmkztjlWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-报告模块状态记录 控制器
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bgmkztjl")
@Api(value = "教学评价-自评报告-报告模块状态记录", tags = "教学评价-自评报告-报告模块状态记录接口")
public class BgmkztjlController extends BaseController {

	private BgmkztjlWrapper bgmkztjlWrapper;
	private IBgmkztjlService bgmkztjlService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入bgmkztjl")
	@ApiLog("详情获取")
	public R<BgmkztjlVO> detail(Bgmkztjl bgmkztjl) {
		Bgmkztjl detail = bgmkztjlService.getOne(Condition.getQueryWrapper(bgmkztjl));
		return R.data(bgmkztjlWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-报告模块状态记录 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入bgmkztjl")
	@ApiLog("分页列表获取")
	public R<IPage<BgmkztjlVO>> list(Bgmkztjl bgmkztjl, Query query) {
		IPage<Bgmkztjl> pages = bgmkztjlService.page(Condition.getPage(query), Condition.getQueryWrapper(bgmkztjl));
		return R.data(bgmkztjlWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-报告模块状态记录
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入bgmkztjl")
	@ApiLog("分页列表获取")
    public R<IPage<BgmkztjlVO>> page(BgmkztjlVO bgmkztjl, Query query) {
        IPage<BgmkztjlVO> pages = bgmkztjlService.selectBgmkztjlPage(Condition.getPage(query), bgmkztjl);
        return R.data(bgmkztjlWrapper.wrapperPageVO(pages));
    }



	/**
	 * 新增 教学评价-自评报告-报告模块状态记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入bgmkztjl")
	@ApiLog("新增")
	public R save(@Valid @RequestBody BgmkztjlVO bgmkztjlVO) {
		boolean b = bgmkztjlService.save(bgmkztjlVO);

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-报告模块状态记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入bgmkztjl")
	@ApiLog("修改")
	public R update(@Valid @RequestBody BgmkztjlVO bgmkztjlVO) {
		boolean b = bgmkztjlService.updateById(bgmkztjlVO);
		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-报告模块状态记录 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入bgmkztjl")
	@ApiLog("新增或修改")
	public R submit(@Valid @RequestBody Bgmkztjl bgmkztjl) {
		return R.status(bgmkztjlService.saveOrUpdate(bgmkztjl));
	}

	
	/**
	 * 删除 教学评价-自评报告-报告模块状态记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("逻辑删除")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		boolean b = bgmkztjlService.deleteLogic(Func.toLongList(ids));
		return R.status(b);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("高级查询")
	public R<IPage<BgmkztjlVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Bgmkztjl> queryWrapper = Condition.getQueryWrapper(map, Bgmkztjl.class);
		IPage<Bgmkztjl> pages = bgmkztjlService.page(Condition.getPage(query), queryWrapper);
		return R.data(bgmkztjlWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Bgmkztjl> queryWrapper = Condition.getQueryWrapper(map, Bgmkztjl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Bgmkztjl> list = bgmkztjlService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Bgmkztjl.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Bgmkztjl> list = ExcelUtil.read(file, Bgmkztjl.class);
		//TODO 此处需要根据具体业务添加代码
		bgmkztjlService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Bgmkztjl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Bgmkztjl> list = bgmkztjlService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Bgmkztjl导入模板", "Bgmkztjl导入模板",columnFiledNames, list, Bgmkztjl.class);
	}
}
