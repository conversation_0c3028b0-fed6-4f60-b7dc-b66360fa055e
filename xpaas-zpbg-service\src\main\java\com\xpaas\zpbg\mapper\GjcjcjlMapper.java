package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Gjcjcjl;
import com.xpaas.zpbg.vo.GjcglVO;
import com.xpaas.zpbg.vo.GjcjcjlVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-关键词检测记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface GjcjcjlMapper extends BaseMapper<Gjcjcjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcjcjl
	 * @return
	 */
	List<GjcjcjlVO> selectGjcjcjlPage(IPage page, GjcjcjlVO gjcjcjl);

	/**
	 * 取得关键词列表
	 *
	 * @return
	 */
	List<GjcglVO> getGjcList(GjcjcjlVO gjcjcjlVO);

	/**
	 * 取得文章内容
	 *
	 * @param gjcjcjlVO
	 * @return
	 */
	List<GjcjcjlVO> getWznr(GjcjcjlVO gjcjcjlVO);

	/**
	 * 报告列表
	 *
	 * @return
	 */
	List<GjcjcjlVO> getBgList();
}
