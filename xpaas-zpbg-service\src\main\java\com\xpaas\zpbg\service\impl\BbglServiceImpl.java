package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.mapper.BbglMapper;
import com.xpaas.zpbg.service.IBbglService;
import com.xpaas.zpbg.vo.BbglVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-版本管理 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Slf4j
@Service
public class BbglServiceImpl extends BaseServiceImpl<BbglMapper, Bbgl> implements IBbglService {

	/**
	 * 自定义分页
	 */
	@Override
	public IPage<BbglVO> selectBbglPage(IPage<BbglVO> page, BbglVO bbgl) {

		return page.setRecords(baseMapper.selectBbglPage(page, bbgl));
	}

	/**
	 * 自定义查询
	 */
	@Override
	public IPage<BbglVO> listByParamsPage(IPage<BbglVO> page, List<Bgmk> bgmkList, BbglVO bbgl) {
		List<Long> listId = new ArrayList<>();
		List<BbglVO> bbglVOS = new ArrayList<>();
		for(Bgmk bgmk : bgmkList){
			listId.add(bgmk.getId());
		}
		if(listId.size()>0){
			bbglVOS = baseMapper.listByParams(page, listId, bbgl);
		}
		return page.setRecords(bbglVOS);
	}

	/**
	 * 历史版本
	 */
	@Override
	public IPage<BbglVO> selectLsbbPage(IPage<BbglVO> page, BbglVO bbgl) {
		return page.setRecords(baseMapper.selectLsbbPage(page, bbgl));
	}

	/**
	 * 历史版本
	 */
	@Override
	public IPage<BbglVO> selectLsbbjpPage(IPage<BbglVO> page, BbglVO bbgl) {
		return page.setRecords(baseMapper.selectLsbbjpPage(page, bbgl));
	}
}
