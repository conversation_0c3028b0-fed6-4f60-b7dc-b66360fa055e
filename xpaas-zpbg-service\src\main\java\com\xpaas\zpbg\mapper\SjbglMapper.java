package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Sjbgl;
import com.xpaas.zpbg.vo.SjbglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-数据表关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Repository
public interface SjbglMapper extends BaseMapper<Sjbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjbgl
	 * @return
	 */
	@SqlParser(filter=true)
	List<SjbglVO> selectSjbglPage(IPage page, SjbglVO sjbgl);

	/**
	 * 不同的内容引用了相同的数据表
	 *
	 * @param sjbgl
	 * @return
	 */
	List<SjbglVO> sameGl(Sjbgl sjbgl);

	/**
	 * 判断是否已关联
	 *
	 * @param sjbgl
	 * @return
	 */
	List<SjbglVO> getGlList(Sjbgl sjbgl);
}
