package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 教学评价-自评报告-进度管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_JDGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Jdgl对象", description = "教学评价-自评报告-进度管理")
public class Jdgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 进度名称
	*/
	@ExcelProperty("进度名称")
	@ApiModelProperty(value = "进度名称")
	@TableField("JDMC")
	private String jdmc;

	/**
	* 开始时间
	*/
	@ExcelProperty("开始时间")
	@ApiModelProperty(value = "开始时间")
	@TableField("KSSJ")

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date kssj;

	/**
	* 撰写结束时间
	*/
	@ExcelProperty("撰写结束时间")
	@ApiModelProperty(value = "撰写结束时间")
	@TableField("ZXJSSJ")

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date zxjssj;

	/**
	* 审阅结束时间
	*/
	@ExcelProperty("审阅结束时间")
	@ApiModelProperty(value = "审阅结束时间")
	@TableField("SYJSSJ")

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date syjssj;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 撰写进度
	*/
	@ExcelProperty("撰写进度")
	@ApiModelProperty(value = "撰写进度")
	@TableField("ZXJD")
	private String zxjd;

	/**
	* 审阅进度
	*/
	@ExcelProperty("审阅进度")
	@ApiModelProperty(value = "审阅进度")
	@TableField("SYJD")
	private String syjd;

	/**
	* 进度状态
	*/
	@ExcelProperty("进度状态")
	@ApiModelProperty(value = "进度状态")
	@TableField("JDZT")
	private Integer jdzt;



}
