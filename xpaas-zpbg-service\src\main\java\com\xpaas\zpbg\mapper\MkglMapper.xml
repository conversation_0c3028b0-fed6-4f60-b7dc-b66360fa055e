<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.MkglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="mkglResultMap" type="com.xpaas.zpbg.entity.Mkgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="MKMC" property="mkmc"/>
        <result column="MKLX" property="mklx"/>
        <result column="CMM" property="cmm"/>
        <result column="SFWFM" property="sfwfm"/>
        <result column="ZBLY" property="zbly"/>
        <result column="YJZBID" property="yjzbid"/>
        <result column="YJZB" property="yjzb"/>
        <result column="EJZBID" property="ejzbid"/>
        <result column="EJZB" property="ejzb"/>
        <result column="PX" property="px"/>
        <result column="BZTXID" property="bztxid"/>
        <result column="SJLY" property="sjly"/>
        <result column="MBWJKEY" property="mbwjkey"/>
        <result column="MBWJLJ" property="mbwjlj"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="mkglResultMapVO" type="com.xpaas.zpbg.vo.MkglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="MKMC" property="mkmc"/>
        <result column="MKLX" property="mklx"/>
        <result column="CMM" property="cmm"/>
        <result column="SFWFM" property="sfwfm"/>
        <result column="ZBLY" property="zbly"/>
        <result column="YJZBID" property="yjzbid"/>
        <result column="YJZB" property="yjzb"/>
        <result column="EJZBID" property="ejzbid"/>
        <result column="EJZB" property="ejzb"/>
        <result column="PX" property="px"/>
        <result column="BZTXID" property="bztxid"/>
        <result column="SJLY" property="sjly"/>
        <result column="MBWJKEY" property="mbwjkey"/>
        <result column="MBWJLJ" property="mbwjlj"/>
        <result column="JDMKGLBS" property="jdmkglbs"/>
    </resultMap>

    <!-- 自定义分页 -->
    <select id="selectMkglPage" resultMap="mkglResultMapVO">
        select
            t1.*,(case when t3.id is null or t4.id is null then false else true end) as jdmkglbs
        from T_DT_JXPJ_ZPBG_MKGL t1
        left join T_DT_JXPJ_ZPBG_BGMK t2
            on t2.mkid = t1.id
            and t2.scbj = 0
        left join T_DT_JXPJ_ZPBG_JDMKGL t3
            on t3.BGMKID = t2.id
            and t3.scbj = 0
        left join T_DT_JXPJ_ZPBG_BGGL t4
            on t4.id = t2.bgid
            and t4.scbj =0
        where t1.scbj = 0
        <if test="mkgl.mkmc != null">
            and t1.mkmc like CONCAT('%', #{mkgl.mkmc}, '%')
        </if>
        <if test="mkgl.mklx != null">
            and t1.mklx = #{mkgl.mklx}
        </if>
        <if test="tree.value != null">
            and (t1.yjzbid = #{tree.value}
                 or t1.ejzbid = #{tree.value}
                 or t1.id = #{tree.value})
        </if>
        group by t1.id
        order by t1.px,t1.cjrq desc
    </select>

    <!-- 标准体系字典取得 -->
    <select id="bztxOptions" resultType="com.xpaas.zpbg.dto.TreeData">
        SELECT
        CAST( zygzdgl.ID AS CHAR ) AS id,
        CASE
        when zygzdgl.cj = 3 then CONCAT(zygzdgl.ZBMC,'/',ly)
        ELSE zygzdgl.ZBMC
        END AS label,
        zygzdgl.cj,
        zygzdgl.zbly,
        zygzdgl.sfglhdjl,
        CAST( zygzdgl.sjzb AS CHAR ) as parentId
        ,zygzdgl.zygzdfzr
        ,zygzdgl.zygzdfzr_str
        FROM
        t_dt_jxpj_sz_zygzdgl zygzdgl
        WHERE
        zygzdgl.SCBJ = 0
        AND ( zygzdgl.cj = 1
            OR zygzdgl.cj = 2
            OR (zygzdgl.cj = 3 )
        )
          AND zygzdgl.zbly = #{mklxid}
        ORDER BY
        zygzdgl.px asc
    </select>

    <!-- 通用查询映射结果 -->
    <select id="getBgmc" resultType="String">
        select
            bggl.bgmc
        from T_DT_JXPJ_ZPBG_BGGL bggl
        inner join T_DT_JXPJ_ZPBG_BGMK bgmk
            on bggl.id = bgmk.bgid
            and bgmk.mkid = #{mkid}
            and bgmk.scbj = 0
        inner join T_DT_JXPJ_ZPBG_RWFG rwfg
            on rwfg.bgmkid = bgmk.id
            and rwfg.scbj = 0
            and rwfg.ryid = #{ryid}
        where bggl.scbj = 0
        limit 1
    </select>

    <!-- 模块名称取得 -->
    <select id="getSaveMkgl" resultMap="mkglResultMapVO">
        SELECT
            zy3.ID AS id,
            zy3.CJR AS cjr,
            zy3.CJBM AS cjbm,
            zy3.CJDW AS cjdw,
            zy3.CJRQ AS cjrq,
            zy3.GXR AS gxr,
            zy3.GXRQ AS gxrq,
            zy3.SCBJ AS scbj,
            zy3.ZT AS zt,
            zy3.ZBMC AS mkmc,
            zy3.ZBLY AS mklx,
            0 AS sfwfm,
            zy3.ZBLY AS zbly,
            zy1.ID AS yjzbid,
            zy1.ZBMC AS yjzb,
            zy2.ID AS ejzbid,
            zy2.ZBMC AS ejzb,
            (zy1.PX * 100 + zy2.PX * 10 + zy3.PX) AS px,
            1 AS sjly,
            zy3.ID AS bztxid
        FROM
            t_dt_jxpj_sz_zygzdgl zy3
            INNER JOIN t_dt_jxpj_sz_zygzdgl zy2 ON zy2.SCBJ = 0
            AND zy2.ID = zy3.SJZB
            INNER JOIN t_dt_jxpj_sz_zygzdgl zy1 ON zy1.SCBJ = 0
            AND zy1.ID = zy2.SJZB
        WHERE
            zy3.SCBJ = 0
            AND zy3.CJ = 3
            AND zy3.ID = #{zygzdid}
            AND NOT EXISTS ( SELECT 1 FROM T_DT_JXPJ_ZPBG_MKGL mkgl WHERE mkgl.BZTXID = zy3.id )
    </select>

    <!-- 模块管理与一期标准体系同步查询 -->
    <insert id="saveMkgl">
        INSERT INTO t_dt_jxpj_zpbg_mkgl
        (ID,
        CJR,
        CJBM,
        CJDW,
        CJRQ,
        GXR,
        GXRQ,
        SCBJ,
        ZT,
        MKMC,
        MKLX,
        SFWFM,
        ZBLY,
        YJZBID,
        YJZB,
        EJZBID,
        EJZB,
        PX,
        SJLY,
        BZTXID
        ) VALUES (
        #{id},
        #{cjr},
        #{cjbm},
        #{cjdw},
        #{cjrq},
        #{gxr},
        #{gxrq},
        #{scbj},
        #{zt},
        #{mkmc},
        #{mklx},
        #{sfwfm},
        #{zbly},
        #{yjzbid},
        #{yjzb},
        #{ejzbid},
        #{ejzb},
        #{px},
        #{sjly},
        #{bztxid}
        )
    </insert>

    <!-- 模块管理与一期标准体系同步新增 -->
    <update id="updateMkgl">
        UPDATE T_DT_JXPJ_ZPBG_MKGL mkgl
        <set>
            <if test="cj == 1">
                mkgl.yjzb = #{zbmc},
            </if>
            <if test="cj == 2">
                mkgl.ejzb = #{zbmc},
            </if>
            <if test="cj == 3">
                mkgl.mkmc = #{zbmc},
                mkgl.zbly = #{zbly},
            </if>
            mkgl.scbj = 0,
            mkgl.gxr = #{gxr},
            mkgl.gxrq = #{gxrq}
        </set>
        where mkgl.scbj = 0
            and mkgl.sjly = 1
            and ((mkgl.yjzbid = #{id} and #{cj} = 1)
              or (mkgl.ejzbid = #{id} and #{cj} = 2)
              or (mkgl.bztxid = #{id} and #{cj} = 3))
    </update>

    <!-- 模块管理与一期标准体系同步修改 -->
    <select id="getSaveMkglZd" resultMap="mkglResultMap">
        SELECT
            zy3.ZBMC AS MKMC,
            zy3.ZBLY AS MKLX,
            zy3.ZBLY AS ZBLY,
            zy1.ID AS YJZBID,
            zy1.ZBMC AS YJZB,
            zy2.ID AS EJZBID,
            zy2.ZBMC AS EJZB,
            (zy1.PX * 100 + zy2.PX * 10 + zy3.PX) AS px,
            1 AS SJLY,
            zy3.ID AS BZTXID,
            0 AS SFWFM
        FROM
            t_dt_jxpj_sz_zygzdgl zy3
            INNER JOIN t_dt_jxpj_sz_zygzdgl zy2 ON zy2.SCBJ = 0
            AND zy2.ID = zy3.SJZB
            INNER JOIN t_dt_jxpj_sz_zygzdgl zy1 ON zy1.SCBJ = 0
            AND zy1.ID = zy2.SJZB
        WHERE
            zy3.SCBJ = 0
            AND zy3.CJ = 3
            AND NOT EXISTS ( SELECT 1 FROM T_DT_JXPJ_ZPBG_MKGL mkgl WHERE mkgl.BZTXID = zy3.id )
        ORDER BY
            zy1.PX,
            zy1.ID,
            zy2.PX,
            zy2.ID,
            zy3.PX,
            zy3.ID
    </select>

    <!-- 模块管理主动同步-模块名称更新 -->
    <update id="mkglUpdateMkmc">
        UPDATE T_DT_JXPJ_ZPBG_MKGL mkgl
        INNER JOIN t_dt_jxpj_sz_zygzdgl zygzdgl
            ON zygzdgl.id = mkgl.BZTXID
            AND zygzdgl.scbj = 0
            AND zygzdgl.cj = 3
        SET mkgl.mkmc = zygzdgl.zbmc,
            mkgl.zbly = zygzdgl.zbly
        WHERE mkgl.mkmc != zygzdgl.zbmc
            AND mkgl.scbj = 0
            AND mkgl.sjly = 1
    </update>

    <!-- 模块管理主动同步-一级指标更新 -->
    <update id="mkglUpdateYjzb">
        UPDATE T_DT_JXPJ_ZPBG_MKGL mkgl
        INNER JOIN t_dt_jxpj_sz_zygzdgl zygzdgl
            ON zygzdgl.id = mkgl.yjzbid
            AND zygzdgl.scbj = 0
            AND zygzdgl.cj = 1
        SET mkgl.yjzb = zygzdgl.zbmc
        WHERE mkgl.yjzb != zygzdgl.zbmc
            AND mkgl.scbj = 0
            AND mkgl.sjly = 1
    </update>

    <!-- 模块管理主动同步-二级指标更新 -->
    <update id="mkglUpdateEjzb">
        UPDATE T_DT_JXPJ_ZPBG_MKGL mkgl
        INNER JOIN t_dt_jxpj_sz_zygzdgl zygzdgl
            ON zygzdgl.id = mkgl.ejzbid
            AND zygzdgl.scbj = 0
            AND zygzdgl.cj = 2
        SET mkgl.ejzb = zygzdgl.zbmc
        WHERE mkgl.ejzb != zygzdgl.zbmc
            AND mkgl.scbj = 0
            AND mkgl.sjly = 1
    </update>

    <!-- 获取id相同的模块信息 -->
    <select id="getMkglIdDate"  resultMap="mkglResultMap">
        SELECT
           *
        FROM T_DT_JXPJ_ZPBG_MKGL
        WHERE
            SCBJ = 0
        <foreach collection="list" item="item" open=" AND ID IN (" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
