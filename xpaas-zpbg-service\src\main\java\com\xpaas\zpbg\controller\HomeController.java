package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.IHomeService;
import com.xpaas.zpbg.vo.HomeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自评报告-首页 控制器
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/home")
@Api(value = "自评报告-首页", tags = "自评报告-首页接口")
public class HomeController extends BaseController {

	private IHomeService homeService;

	/**
	 * 角色权限获取
	 */
	@GetMapping("/getRoleFlag")
	@ApiOperationSupport(order = 1)
	@ApiLog("角色权限获取")
	public R<Map<String,Object>> getRoleFlag(String bgid, String jdid, String bgmkid) {
		LoginUser user = AuthUtil.getUser();

		Map<String,Object> retMap = new HashMap<>();

		int flag = homeService.getRoleFlag(user.getRoleId(), bgid, jdid, bgmkid);

		int flagZj = homeService.getRoleFlagZj(user.getRoleId());

		Map<String, Object> flagDzAndZxr = homeService.getDzAndZxrRole(bgid,jdid);

		if(flagDzAndZxr != null && flagDzAndZxr.size() > 0){
			if(flagDzAndZxr.get("dz") != null){
				if(Integer.parseInt(String.valueOf(flagDzAndZxr.get("dz"))) > 0){
					retMap.put("flagDz", 1);
				}else{
					retMap.put("flagDz", 0);
				}
			}else{
				retMap.put("flagDz", 0);
			}

			if(flagDzAndZxr.get("zxr") != null){
				if(Integer.parseInt(String.valueOf(flagDzAndZxr.get("zxr"))) > 0){
					retMap.put("flagZxr", 1);
				}else{
					retMap.put("flagZxr", 0);
				}
			}else{
				retMap.put("flagZxr", 0);
			}

		}else{
			retMap.put("flagDz", 0);
			retMap.put("flagZxr", 0);
		}

		retMap.put("flag",flag);
		retMap.put("flagZj",flagZj);
		return R.data(retMap);
	}

	/**
	 * 获取报告下拉列表数据 教评管理员
	 */
	@GetMapping("/getBgJpList")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "取报告列表数据不分页", notes = "")
	@ApiLog("报告下拉列表数据-教评管理员")
	public R<List<HomeVO>> getBgJpList(HomeVO home) {
		List<HomeVO> list = homeService.getBgJpList(home);
		return R.data(list);
	}

	/**
	 * 获取报告下拉列表数据 专家
	 */
	@GetMapping("/getBgZjList")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "取报告列表数据不分页", notes = "")
	@ApiLog("报告下拉列表数据-专家")
	public R<List<HomeVO>> getBgZjList(HomeVO home) {
		List<HomeVO> list = homeService.getBgZjList(home);
		return R.data(list);
	}

	/**
	 * 获取报告下拉列表数据 点长撰写人
	 */
	@GetMapping("/getBgDzAndZxrList")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "取报告列表数据不分页", notes = "")
	@ApiLog("报告下拉列表数据-点长和撰写人")
	public R<List<HomeVO>> getBgDzAndZxrList(HomeVO home) {
		List<HomeVO> list = homeService.getBgDzAndZxrList(home);
		return R.data(list);
	}

	/**
	 * 获取报告列表 工作台使用 专家、点长、撰写人
	 */
	@GetMapping("/getBgZjAndDzAndZxrList")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "取报告列表数据不分页", notes = "")
	@ApiLog("报告下拉列表数据-专家和点长和撰写人")
	public R<List<HomeVO>> getBgZjAndDzAndZxrList(HomeVO home) {
		List<HomeVO> list = homeService.getBgZjAndDzAndZxrList(home);
		return R.data(list);
	}

	/**
	 * 获取点长或教评管理员模块列表 以及撰写人任务分工列表 教评管理员统计页用
	 */
	@GetMapping("/getSymkInfo")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "取报告列表的模块数据不分页", notes = "")
	@ApiLog("获取点长或教评管理员模块列表")
	public R<HomeVO> getSymkInfo(HomeVO home) {
		HomeVO homeVO = homeService.getSymkInfo(home);
		return R.data(homeVO);
	}

	/**
	 * 获取进度下拉列表数据
	 */
	@GetMapping("/getJdList")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "取进度列表数据不分页", notes = "")
	@ApiLog("进度下拉列表数据")
	public R<List<HomeVO>> getJdList(HomeVO home) {
		List<HomeVO> list = homeService.getJdList(home);
		return R.data(list);
	}

	/**
	 * 获取进度头部显示数据
	 */
	@PostMapping("/getJdInfo")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "取进度头部显示数据不分页", notes = "")
	@ApiLog("进度进度头部显示数据")
	public R<Map<String, Object>> getJdInfo(HomeVO home) throws ParseException {
		Map<String, Object> map = homeService.getJdInfo(home);
		return R.data(map);
	}

	/**
	 * 各单位进度统计
	 */
	@GetMapping("/getGdwjdtjList")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "各单位进度统计", notes = "")
	@ApiLog("各单位进度统计")
	public R<List<Map<String, Object>>> getGdwjdtjList(HomeVO homeVO){
		List<Map<String, Object>> list = homeService.getGdwjdtjList(homeVO);
		return R.data(list);
	}

	/**
	 * 专家审阅情况统计
	 */
	@GetMapping("/getZjsyqkList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "专家审阅情况统计", notes = "")
	@ApiLog("专家审阅情况统计")
	public R<List<Map<String, Object>>> getZjsyqkList(HomeVO homeVO){
		List<Map<String, Object>> list = homeService.getZjsyqkList(homeVO);
		return R.data(list);
	}

	/**
	 * 各模块审阅频次统计
	 */
	@GetMapping("/getGmksypcList")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "各模块审阅频次统计", notes = "")
	@ApiLog("各模块审阅频次统计")
	public R<List<Map<String, Object>>> getGmksypcList(HomeVO homeVO){
		List<Map<String, Object>> list = homeService.getGmksypcList(homeVO);
		return R.data(list);
	}

	/**
	 * 各一级指标情况统计
	 */
	@GetMapping("/getGyjzbqktjList")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "各一级指标情况统计", notes = "")
	@ApiLog("各一级指标情况统计")
	public R<List<Map<String, Object>>> getGyjzbqktjList(HomeVO homeVO){
		List<Map<String, Object>> list = homeService.getGyjzbqktjList(homeVO);
		return R.data(list);
	}

	/**
	 * 总览信息统计
	 */
	@GetMapping("/getZlxxtjInfo")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "总览信息统计", notes = "")
	@ApiLog("总览信息统计")
	public R<Map<String, Object>> getZlxxtjInfo(HomeVO homeVO){
		Map<String, Object> map = homeService.getZlxxtjInfo(homeVO);
		return R.data(map);
	}

	/**
	 * 获取当前系统时间
	 */
	@GetMapping("/getNowTime")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "取当前系统时间", notes = "")
	@ApiLog("当前系统时间")
	public R<HomeVO> getNowTime() {
		HomeVO home = homeService.getNowTime();
		return R.data(home);
	}

	/**
	 * 获取报告进度对应的模块列表数据 专家
	 */
	@GetMapping("/getBgmkZjList")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "取报告进度对应的模块列表数据不分页", notes = "")
	@ApiLog("报告进度对应的模块列表数据-专家")
	public R<List<HomeVO>> getBgmkZjList(HomeVO homeVO) {
		List<HomeVO> list = homeService.getBgmkZjList(homeVO);
		return R.data(list);
	}

	/**
	 * 获取报告进度对应的模块列表数据 撰写人
	 */
	@GetMapping("/getBgmkDzAndZxrList")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "取报告进度对应的模块列表数据不分页", notes = "")
	@ApiLog("报告进度对应的模块列表数据-点长和撰写人")
	public R<List<HomeVO>> getBgmkDzAndZxrList(HomeVO homeVO) {
		List<HomeVO> list = homeService.getBgmkDzAndZxrList(homeVO);
		return R.data(list);
	}

	/**
	 * 工作台报告数量
	 */
	@GetMapping("/gztbgsl")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "工作台报告数量", notes = "")
	@ApiLog("工作台报告数量")
	public R gztbgsl(HomeVO homeVO) {
		return R.data(homeService.gztbgsl(homeVO));
	}


}
