/*
 Navicat Premium Data Transfer

 Source Server         : 【西岐】教学评价
 Source Server Type    : MySQL
 Source Server Version : 50736
 Source Host           : *************:3306
 Source Schema         : xpaas_jxpj_dev

 Target Server Type    : MySQL
 Target Server Version : 50736
 File Encoding         : 65001

 Date: 02/09/2024 16:33:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_dt_jxpj_zpbg_bbpz
-- ----------------------------
DROP TABLE IF EXISTS `t_dt_jxpj_zpbg_bbpz`;
CREATE TABLE `t_dt_jxpj_zpbg_bbpz`  (
  `ID` bigint(20) NOT NULL COMMENT '主键ID',
  `ZHID` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `CJR` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CJBM` bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
  `CJDW` bigint(20) NULL DEFAULT NULL COMMENT '创建单位',
  `CJRQ` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `GXR` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `GXRQ` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `SCBJ` tinyint(4) NULL DEFAULT NULL COMMENT '删除标记',
  `ZT` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `BBID` bigint(20) NULL DEFAULT NULL COMMENT '版本ID',
  `COMMENT_ID` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批注ID',
  `PX` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `index_bbid`(`BBID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学评价-自评报告-版本批注' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
