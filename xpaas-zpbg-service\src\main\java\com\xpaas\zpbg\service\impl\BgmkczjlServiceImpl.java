package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Bgmkczjl;
import com.xpaas.zpbg.mapper.BgmkczjlMapper;
import com.xpaas.zpbg.service.IBgmkczjlService;
import com.xpaas.zpbg.vo.BgmkczjlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 教学评价-自评报告-报告模块操作记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@Service
public class BgmkczjlServiceImpl extends BaseServiceImpl<BgmkczjlMapper, Bgmkczjl> implements IBgmkczjlService {

	@Override
	public IPage<BgmkczjlVO> selectBgmkczjlPage(IPage<BgmkczjlVO> page, BgmkczjlVO bgmkczjl) {
		return page.setRecords(baseMapper.selectBgmkczjlPage(page, bgmkczjl));
	}

}
