package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Gjcjcjl;
import com.xpaas.zpbg.service.IGjcjcjlService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.GjcjcjlVO;
import com.xpaas.zpbg.wrapper.GjcjcjlWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-关键词检测记录 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/gjcjcjl")
@Api(value = "教学评价-自评报告-关键词检测记录", tags = "教学评价-自评报告-关键词检测记录接口")
public class GjcjcjlController extends BaseController {

	private GjcjcjlWrapper gjcjcjlWrapper;
	private IGjcjcjlService gjcjcjlService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-详情")
	public R<GjcjcjlVO> detail(Gjcjcjl gjcjcjl) {
		Gjcjcjl detail = gjcjcjlService.getOne(Condition.getQueryWrapper(gjcjcjl));
		return R.data(gjcjcjlWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-关键词检测记录 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-列表")
	public R<IPage<GjcjcjlVO>> list(Gjcjcjl gjcjcjl, Query query) {
		IPage<Gjcjcjl> pages = gjcjcjlService.page(Condition.getPage(query), Condition.getQueryWrapper(gjcjcjl));
		return R.data(gjcjcjlWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-关键词检测记录
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-自定义分页")
    public R<IPage<GjcjcjlVO>> page(GjcjcjlVO gjcjcjl, Query query) {
        IPage<GjcjcjlVO> pages = gjcjcjlService.selectGjcjcjlPage(Condition.getPage(query), gjcjcjl);
        return R.data(gjcjcjlWrapper.wrapperPageVO(pages));
    }

	/**
	 * 新增 教学评价-自评报告-关键词检测记录
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-新增")
	public R save(@Valid @RequestBody GjcjcjlVO gjcjcjlVO) {
		boolean b = gjcjcjlService.save(gjcjcjlVO);

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-关键词检测记录
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-修改")
	public R update(@Valid @RequestBody GjcjcjlVO gjcjcjlVO) {
		boolean b = gjcjcjlService.updateById(gjcjcjlVO);
		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-关键词检测记录 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-新增或修改")
	public R submit(@Valid @RequestBody Gjcjcjl gjcjcjl) {
		return R.status(gjcjcjlService.saveOrUpdate(gjcjcjl));
	}

	
	/**
	 * 删除 教学评价-自评报告-关键词检测记录
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("关键词检测记录-删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = gjcjcjlService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}

	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("关键词检测记录-高级查询")
	public R<IPage<GjcjcjlVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Gjcjcjl> queryWrapper = Condition.getQueryWrapper(map, Gjcjcjl.class);
		IPage<Gjcjcjl> pages = gjcjcjlService.page(Condition.getPage(query), queryWrapper);
		return R.data(gjcjcjlWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("关键词检测记录-导出")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Gjcjcjl> queryWrapper = Condition.getQueryWrapper(map, Gjcjcjl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Gjcjcjl> list = gjcjcjlService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Gjcjcjl.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("关键词检测记录-导入")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Gjcjcjl> list = ExcelUtil.read(file, Gjcjcjl.class);
		//TODO 此处需要根据具体业务添加代码
		gjcjcjlService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("关键词检测记录-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Gjcjcjl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Gjcjcjl> list = gjcjcjlService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Gjcjcjl导入模板", "Gjcjcjl导入模板",columnFiledNames, list, Gjcjcjl.class);
	}

	/**
	 * 关键词检测 教学评价-自评报告-关键词检测记录
	 */
	@PostMapping("/detect")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "关键词检测", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-关键词检测")
	public R detect(@Valid @RequestBody GjcjcjlVO gjcjcjlVO) {
		boolean b = gjcjcjlService.detect(gjcjcjlVO);

		return R.data(String.valueOf(gjcjcjlVO.getId()));
	}

	/**
	 * 报告列表
	 */
	@GetMapping("/getBgList")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入gjcjcjl")
	@ApiLog("关键词检测记录-报告列表")
	public List<GjcjcjlVO> getBgList() {
		return gjcjcjlService.getBgList();
	}
}
