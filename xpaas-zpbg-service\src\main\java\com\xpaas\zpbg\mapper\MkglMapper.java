package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.dto.TreeData;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zygzdgl;
import com.xpaas.zpbg.vo.MkglVO;
import com.xpaas.zpbg.vo.TreeVO;
import feign.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-模块管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Repository
public interface MkglMapper extends BaseMapper<Mkgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mkgl
	 * @return
	 */
	List<MkglVO> selectMkglPage(IPage page, MkglVO mkgl, TreeVO tree);

	/**
	 * 标准体系字典取得
	 *
	 * @param mklxid
	 * @return
	 */
	List<TreeData> bztxOptions(String mklxid);

	/**
	 * 模块名称取得
	 *
	 * @param mkid
	 * @param ryid
	 * @return
	 */
	String getBgmc(String mkid, String ryid);

	/**
	 * 模块管理与一期标准体系同步查询
	 *
	 * @param zygzdid
	 * @return
	 */
	MkglVO getSaveMkgl(Long zygzdid);

	/**
	 * 模块管理与一期标准体系同步新增
	 *
	 * @param mkglVO
	 * @return
	 */
	boolean saveMkgl(MkglVO mkglVO);

	/**
	 * 模块管理与一期标准体系同步修改
	 *
	 * @param zygzdgl
	 * @return
	 */
	boolean updateMkgl(Zygzdgl zygzdgl);

	/**
	 * 模块管理主动同步查询
	 *
	 * @return
	 */
	List<Mkgl> getSaveMkglZd();

	/**
	 * 模块管理主动同步-模块名称更新
	 *
	 * @return
	 */
	boolean mkglUpdateMkmc();

	/**
	 * 模块管理主动同步-一级指标更新
	 *
	 * @return
	 */
	boolean mkglUpdateYjzb();

	/**
	 * 模块管理主动同步-二级指标更新
	 *
	 * @return
	 */
	boolean mkglUpdateEjzb();

	/**
	 * 获取id相同的模块信息
	 *
	 * @return
	 */
	List<Mkgl> getMkglIdDate( @Param("list") List<String> list);

}
