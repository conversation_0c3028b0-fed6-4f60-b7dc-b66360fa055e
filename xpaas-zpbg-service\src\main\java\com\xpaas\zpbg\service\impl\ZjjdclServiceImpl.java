package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zjjdcl;
import com.xpaas.zpbg.mapper.ZjjdclMapper;
import com.xpaas.zpbg.service.IZjjdclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教学评价-自评报告-专家解读材料 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@Service
public class ZjjdclServiceImpl extends BaseServiceImpl<ZjjdclMapper, Zjjdcl> implements IZjjdclService {

	@Autowired
	private ZjjdclMapper zjjdclMapper;

	@Override
	public List<Zjjdcl> list1(Zjjdcl zjjdcl) {
		return zjjdclMapper.list1(zjjdcl);
	}

}
