package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.vo.BbglVO;

import java.util.List;

/**
 * 教学评价-自评报告-版本管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
public interface IBbglService extends BaseService<Bbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	IPage<BbglVO> selectBbglPage(IPage<BbglVO> page, BbglVO bbgl);


	/**
	 * 自定义查询
	 *
	 * @param page
	 * @param bgmkList
	 * @param bbgl
	 * @return
	 */
	IPage<BbglVO> listByParamsPage(IPage<BbglVO> page, List<Bgmk> bgmkList, BbglVO bbgl);

	/**
	 * 历史版本
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	IPage<BbglVO> selectLsbbPage(IPage<BbglVO> page, BbglVO bbgl);


	/**
	 * 历史版本
	 *
	 * @param page
	 * @param bbgl
	 * @return
	 */
	IPage<BbglVO> selectLsbbjpPage(IPage<BbglVO> page, BbglVO bbgl);


}
