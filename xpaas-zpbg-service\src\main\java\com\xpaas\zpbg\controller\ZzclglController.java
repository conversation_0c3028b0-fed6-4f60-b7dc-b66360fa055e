package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.service.IZzclglService;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;
import com.xpaas.zpbg.wrapper.ZzclglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-佐证材料关联 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zzclgl")
@Api(value = "教学评价-自评报告-佐证材料关联", tags = "教学评价-自评报告-佐证材料关联接口")
public class ZzclglController extends BaseController {

    private ZzclglWrapper zzclglWrapper;
    private IZzclglService zzclglService;
    private IZzclService zzclService;
    @Autowired
    private ResourceLoader resourceLoader;


    /**f
     * 获取模块id
     */
    @GetMapping("/getMKId")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取模块id", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-获取模块id")
    public R<ZzclglVO> detail(String bgmkid) {
        Zzclgl detail = zzclglService.getMKId(Long.valueOf(bgmkid));
        return R.data(zzclglWrapper.entityVO(detail));
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-详情")
    public R<ZzclglVO> detail(Zzclgl zzclgl) {
        Zzclgl detail = zzclglService.getOne(Condition.getQueryWrapper(zzclgl));
        return R.data(zzclglWrapper.entityVO(detail));
    }

    /**
     * 查询的所有数据
     */
    @GetMapping("/allList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "所有", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-查询的所有数据")
    public R  allList(Zzclgl zzclgl, Query query) {
        List<ZzclglVO> list = zzclglService.getZzlglList(zzclgl);
        return R.data(list);
    }

    /**
     * 分页 教学评价-自评报告-佐证材料关联 (优先使用search接口)
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-分页")
    public R<IPage<ZzclglVO>> list(Zzclgl zzclgl, Query query) {
        IPage<Zzclgl> pages = zzclglService.page(Condition.getPage(query), Condition.getQueryWrapper(zzclgl));
        return R.data(zzclglWrapper.pageVO(pages));
    }

    /**
     * 自定义分页 教学评价-自评报告-佐证材料关联
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-自定义分页")
    public R<IPage<ZzclglVO>> page(ZzclglVO zzclgl, Query query) {
        IPage<ZzclglVO> pages = zzclglService.selectZzclglPage(Condition.getPage(query), zzclgl);
        return R.data(zzclglWrapper.wrapperPageVO(pages));
    }


    /**
     * 新增 教学评价-自评报告-佐证材料关联
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-新增")
    @Transactional
    public R save(@Valid @RequestBody List<ZzclglVO> zzclglVOs) {
        ZzclglVO  zzclgl = new ZzclglVO();
        Query query = new Query();
        int px = 1;
        query.setSize(-1);
        query.setCurrent(1);
        int  maxPx = zzclglService.getMaxPx();
        px = maxPx+1;
        for (ZzclglVO zzclglVO : zzclglVOs) {
            zzclglVO.setScbj(0);
            List<Zzclgl> list = zzclglService.list(Condition.getQueryWrapper(zzclglVO));
            if (list.size() > 0) {

                return R.data("此关联已添加！");
            }

            if(zzclglVO.getZzclid()==null){
                ZzclVO zzcl = new ZzclVO();
                zzcl.setClmc(zzclglVO.getClmc());
                zzcl.setCmm(zzclglVO.getCmm());
                zzcl.setWjh(zzclglVO.getWjh());
                zzcl.setWjlj(zzclglVO.getWjlj());
                List<ZzclVO> zcllList = zzclService.getZzclId(zzcl);
                if(zcllList.size()>0){
                    zzclglVO.setZzclid(zcllList.get(0).getId());
                }
            }
            zzclglVO.setPx(px);
            boolean b = zzclglService.save(zzclglVO);
            px++;
        }
        return R.status(true);
    }

    /**
     * 新增多条记录 教学评价-自评报告-不同的内容引用了相同的佐证材料
     */
    @PostMapping("/sameGl")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "不同的内容引用了相同的佐证材料", notes = "传入zzclgl")
    @ApiLog("数据表关联-不同的内容引用了相同的佐证材料")
    public R sameGl(@Valid @RequestBody ZzclglVO zzclglVO) {
        String res = checkGl(zzclglVO.getSelectList());
        if(!"".equals(res)){
            return R.data("0");
        }
        String msg = zzclglService.sameGl(zzclglVO);
        return R.data(msg);
    }

    public String checkGl(List<Zzclgl> zzclglVOs) {
        ZzclglVO  zzclgl = new ZzclglVO();
        Query query = new Query();
        query.setSize(-1);
        query.setCurrent(1);
        //IPage<ZzclglVO> pages = zzclglService.selectZzclglPage(Condition.getPage(query), zzclgl);
        //List<ZzclglVO>  listData = pages.getRecords();
        for (Zzclgl zzclglVO : zzclglVOs) {
            zzclglVO.setPx(null);
            // 没有关联KEY时，不用校验
            if(StringUtil.isEmpty(zzclglVO.getGlkey())) {
                return "";
            }
            zzclglVO.setScbj(0);
            List<Zzclgl> list = zzclglService.list(Condition.getQueryWrapper(zzclglVO));
            if (list.size() > 0) {
                return "此关联已添加！";
            }
        }
        return "";
    }

    /**
     * 修改 教学评价-自评报告-佐证材料关联
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-修改")
    public R update(@Valid @RequestBody ZzclglVO zzclglVO) {
        boolean b = zzclglService.updateById(zzclglVO);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-自评报告-佐证材料关联 (优先使用save或update接口)
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入zzclgl")
    @ApiLog("佐证材料关联-新增或修改")
    public R submit(@Valid @RequestBody Zzclgl zzclgl) {
        return R.status(zzclglService.saveOrUpdate(zzclgl));
    }

    /**
     * 删除 教学评价-自评报告-佐证材料关联
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @ApiLog("佐证材料关联-逻辑删除")
    public R remove(@Valid @RequestBody List<ZzclglVO> ids) {
        List<Long> longList = new ArrayList<>();
        if(ids.size()>0){
            for (ZzclglVO item :ids){
                longList.add(item.getId());
            }
        }
        boolean b = zzclglService.deleteLogic(longList);
        return R.status(b);
    }

    /**
     * 高级查询
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    @ApiLog("佐证材料关联-高级查询")
    public R<IPage<ZzclglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<Zzclgl> queryWrapper = Condition.getQueryWrapper(map, Zzclgl.class);
        IPage<Zzclgl> pages = zzclglService.page(Condition.getPage(query), queryWrapper);
        return R.data(zzclglWrapper.pageVO(pages));
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    @ApiLog("佐证材料关联-导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Zzclgl> list = ExcelUtil.read(file, Zzclgl.class);
        //TODO 此处需要根据具体业务添加代码
        zzclglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @ApiLog("佐证材料关联-下载导入模板")
    public void template(HttpServletResponse response) {
        QueryWrapper<Zzclgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Zzclgl> list = zzclglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        //TODO 此处需要根据具体业务添加代码
        ExcelUtil.export(response, "Zzclgl导入模板", "Zzclgl导入模板", columnFiledNames, list, Zzclgl.class);
    }

    @GetMapping("getIpAddress")
    @ApiLog("获取IP地址")
    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getRemoteAddr();
        return ip;
    }

    /**
     * 导出Excel
     */
    @GetMapping("/exportExcel")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ApiLog("佐证材料关联-导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "报告ID") String bgid,
                            @ApiParam(value = "报告模块ID") String bgmkid,
                            @ApiParam(value = "版本ID") String bbid,
                            @ApiParam(value = "材料名称") String clmc,
                            @ApiParam(value = "关联KEY") String glkey) {

        ZzclglVO zzclglVO = new ZzclglVO();
        zzclglVO.setBgid(Long.parseLong(bgid));
        zzclglVO.setBgmkid(Long.parseLong(bgmkid));
        zzclglVO.setBbid(Long.parseLong(bbid));
        zzclglVO.setClmc(clmc);
        zzclglVO.setGlkeyStr(glkey);
        zzclglVO.setScbj(0);

        Query query = new Query();
        query.setCurrent(1);
        query.setSize(-1);

        IPage<Zzclgl> pages = zzclglService.page(Condition.getPage(query), Condition.getQueryWrapper(zzclglVO));

        export(response, pages.getRecords());
    }

    private void export(HttpServletResponse response, List<Zzclgl> dataList) {
        try {
            String path = "classpath:doc/zzclgl.xlsx";
            org.springframework.core.io.Resource resource = resourceLoader.getResource(path);
            InputStream inStream= resource.getInputStream();
            XSSFWorkbook wb = new XSSFWorkbook(inStream);
            response.reset();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(Charsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=zzclgl.xlsx");

            writeData(wb, dataList);
            wb.write(response.getOutputStream());
            wb.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void writeData(XSSFWorkbook wb, List<Zzclgl> list) {
        XSSFSheet sheet = wb.getSheetAt(0);
        int rowIndex = 1;
        Row row = null;

        if(list != null) {
            for(int i = 0; i < list.size(); i++) {
                Zzclgl vo = list.get(i);

                int colIndex = 0;
                // 插入行
                row = sheet.createRow(rowIndex);

                // 序号
                setCellValue(wb, row, (i + 1), colIndex);
                colIndex++;

                // 佐证材料名称
                setCellValue(wb, row, vo.getClmc(), colIndex);
                colIndex++;

                // 文件号
                setCellValue(wb, row, vo.getWjh(), colIndex);
                colIndex++;

                rowIndex++;
            }
        }
    }

    private void setCellValue(XSSFWorkbook wb, Row row, Object str, int col) {
        Font titleFont = wb.createFont();
        titleFont.setFontName("微软雅黑");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setColor(IndexedColors.BLACK.index);
        XSSFCellStyle titleStyle = wb.createCellStyle();
        titleStyle.setWrapText(true);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFont(titleFont);

        byte[] rgb = new byte[]{(byte) 0, (byte) 0, (byte) 0};
        setBorder(titleStyle, BorderStyle.THIN, new XSSFColor(rgb ,null));
        Cell cell = row.createCell(col);
        if(str == null) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(String.valueOf(str));
        }

        cell.setCellStyle(titleStyle);
    }

    private void setBorder(XSSFCellStyle style, BorderStyle border, XSSFColor color) {
        style.setBorderTop(border);
        style.setBorderLeft(border);
        style.setBorderRight(border);
        style.setBorderBottom(border);
        style.setBorderColor(XSSFCellBorder.BorderSide.TOP, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.LEFT, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.RIGHT, color);
        style.setBorderColor(XSSFCellBorder.BorderSide.BOTTOM, color);
    }


    /**
     * 校验关键词是否关联
     */
    @GetMapping("/checkGlkey")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "zzclglVO")
    @ApiLog("佐证材料关联-校验关键词是否关联")
    public R checkGlkey(ZzclglVO zzclglVO) {
        if(zzclglVO.getGlkeyStr()!=null){
            zzclglVO.setGlkeyList(Arrays.asList(zzclglVO.getGlkeyStr().split(",")));
        }
        int flag = zzclglService.checkGlkey(zzclglVO);
        return R.data(flag);
    }

    /**
     * 更新顺序
     */
    @GetMapping("/updataPx")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "所有", notes = "传入zclgl")
    @ApiLog("佐证材料关联-更新排序顺序")
    public R updataPx(String id,String newpx){
        boolean flg = zzclglService.updataPx(id,newpx);
        return R.status(flg);
    }
}
