package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Xxfb;
import org.springframework.stereotype.Repository;

/**
 * 自评报告-消息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Repository
public interface XxfbMapper extends BaseMapper<Xxfb> {

//
//	/**
//	 * 报告期限预警 模块列表
//	 *
//	 * @return
//	 */
//	List<Map<String, Object>> getBgWarnList();
//
//	/**
//	 * 报告开始预警 模块列表
//	 *
//	 * @return
//	 */
//	List<Map<String, Object>> getBgStartWarnList();
//
//	/**
//	 * 报告延期预警 模块列表
//	 *
//	 * @return
//	 */
//	List<Map<String, Object>> getBgDelayWarnList();
//
//	/**
//	 * 任务分工列表
//	 * @param bgId 报告ID
//	 * @param bgmkId 报告模块ID
//	 * @return
//	 */
//	List<Map<String, Object>> getRwfgList(Long bgId, Long bgmkId);
//
//	int updatBbglById(Long bgId, Long bgmkId, Long jdglId);
//
//	int updatBgmkById(Long bgmkId);

}
