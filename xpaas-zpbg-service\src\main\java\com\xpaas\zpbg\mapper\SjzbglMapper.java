package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Sjzbgl;
import com.xpaas.zpbg.vo.SjclVO;
import com.xpaas.zpbg.vo.SjzbglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-数据指标关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Repository
public interface SjzbglMapper extends BaseMapper<Sjzbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjzbgl
	 * @return
	 */
	@SqlParser(filter=true)
	List<SjzbglVO> selectSjzbglPage(IPage page, SjzbglVO sjzbgl);

	/**
	 * 取得关键词
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> searchGjc(SjzbglVO sjzbglVO);

	/**
	 * 不同的内容引用了相同的数据表
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> sameGl(SjzbglVO sjzbglVO);

	/**
	 * 校验关键词是否关联
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<String> checkGlkey(SjzbglVO sjzbglVO);

	/**
	 * 取得关联信息
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> getGlAllList(SjzbglVO sjzbglVO);

	/**
	 * 查询数据材料列表
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjclVO> searchSjlc(SjzbglVO sjzbglVO);

	/**
	 * 判断是否已关联
	 *
	 * @param sjzbglVO
	 * @return
	 */
	List<SjzbglVO> getGlList(SjzbglVO sjzbglVO);
}
