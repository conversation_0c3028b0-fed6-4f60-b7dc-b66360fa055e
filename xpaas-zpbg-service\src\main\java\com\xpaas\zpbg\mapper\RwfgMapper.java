package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xpaas.zpbg.dto.RwryDTO;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.vo.RwfgVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-任务分工 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Repository
public interface RwfgMapper extends BaseMapper<Rwfg> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rwfg
	 * @return
	 */
	List<RwfgVO> selectRwfgPage(IPage page, RwfgVO rwfg);

	/**
	 * 查询任务人员信息
	 *
	 * @param bggl
	 * @return
	 */
	List<RwryDTO> selectMkry(@Param("bggl") Bggl bggl);

	/**
	 * 删除任务人员信息
	 *
	 * @param
	 * @return
	 */
	int deletePhysical(@Param("bgid")Long bgid);

	/**
	 *
	 * 根据条件查询任务人员信息
	 *
	 * @param rwryWrapper
	 * @return
	 */
	List<RwryDTO> selectMkryByWrapper(@Param(Constants.WRAPPER) Wrapper<RwryDTO> rwryWrapper);

	/**
	 * 任务人员信息
	 *
	 * @param bgid
	 * @return
	 */
	List<RwfgVO> selectRwfgInfo(@Param("bgid")Long bgid);

	/**
	 * 报告模块信息
	 *
	 * @param bgid
	 * @return
	 */
	List<RwfgVO> selectBgmkInfo(@Param("bgid")Long bgid);

}
