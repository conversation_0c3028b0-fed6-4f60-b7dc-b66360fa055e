package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Gjcglmk;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.mapper.GjcglmkMapper;
import com.xpaas.zpbg.service.IGjcglmkService;
import com.xpaas.zpbg.service.IMkglService;
import com.xpaas.zpbg.vo.GjcglmkVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

/**
 * 教学评价-自评报告-关键词关联模块 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Slf4j
@Service
public class GjcglmkServiceImpl extends BaseServiceImpl<GjcglmkMapper, Gjcglmk> implements IGjcglmkService {

	@Autowired
	private IMkglService mkglService;

	@Override
	public IPage<GjcglmkVO> selectGjcglmkPage(IPage<GjcglmkVO> page, GjcglmkVO gjcglmk) {
		return page.setRecords(baseMapper.selectGjcglmkPage(page, gjcglmk));
	}

	@Override
	public List<GjcglmkVO> selectGjcglmkList(GjcglmkVO gjcglmk) {
		return baseMapper.selectGjcglmkList(gjcglmk);
	}

	@Override
	public boolean gjcglmkSave(GjcglmkVO gjcglmk, Integer glfw, String mode) {
		if ("update".equals(mode)) {
			List<GjcglmkVO> lastMkidList= baseMapper.selectGjcglmkList(gjcglmk);
			findNonIntersectingElements(lastMkidList, gjcglmk.getMkidList(), gjcglmk.getGjcid(), glfw);
		}

		// 关键词关联模块删除
		baseMapper.gjcglmkDelete(gjcglmk);

		// 关键词关联模块保存
		for(Long mkid : gjcglmk.getMkidList()){
			Gjcglmk item = new Gjcglmk();
			item.setGjcid(gjcglmk.getGjcid());
			item.setMkid(mkid);
			super.save(item);
		}
		return true;
	}

	public void findNonIntersectingElements(List<GjcglmkVO> lastMkidList, Long[] mkidList, Long gjcid, Integer glfw) {
		HashSet<Long> setOld = new HashSet<>();
		for (GjcglmkVO gjcglmkVO : lastMkidList) {
			setOld.add(gjcglmkVO.getMkid());
		}
		HashSet<Long> setNew = new HashSet<>();
		for (Long numNew : mkidList) {
			setNew.add(numNew);
		}
		// 部分模块
		if(glfw == 2){

			// 更新前关键词关联范围是所有模块时
			if (lastMkidList.size() == 0) {
				List<Mkgl> mkglList= mkglService.list();
				// 遍历mkglList，
				for (Mkgl mkgl : mkglList) {
					// 检查每个元素是否在mkidList中
					if (!setNew.contains(mkgl.getId())) {
						// 逻辑删除关键词智能检测表中该关键词数据
						baseMapper.removeByGjcidMkid(gjcid, mkgl.getId());
					}
				}
			} else {
				// 遍历lastMkidList，
				for (GjcglmkVO gjcglmkVO : lastMkidList) {
					// 检查每个元素是否在mkidList中
					if (!setNew.contains(gjcglmkVO.getMkid())) {
						// 逻辑删除关键词智能检测表中该关键词数据
						baseMapper.removeByGjcidMkid(gjcid, gjcglmkVO.getMkid());
					}
				}
				// 遍历mkidList，
				for (Long numNew : mkidList) {
					if (!setOld.contains(numNew)) {
						// 复原删除关键词智能检测表中该关键词数据
						baseMapper.restoreByGjcidMkid(gjcid, numNew);
					}
				}
			}
		// 所有模块
		} else if (glfw == 1) {
			List<Mkgl> mkglList= mkglService.list();
			// 遍历mkglList，
			for (Mkgl mkgl : mkglList) {
				// 检查每个元素是否在mkidList中
				if (!setOld.contains(mkgl.getId())) {
					// 逻辑删除关键词智能检测表中该关键词数据
					baseMapper.restoreByGjcidMkid(gjcid, mkgl.getId());
				}
			}

		}
	}

}
