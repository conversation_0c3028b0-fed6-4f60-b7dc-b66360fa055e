package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.elasticsearch.dto.SearchDTO;
import com.xpaas.elasticsearch.feign.IZjjyClient;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zccl;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.entity.Zcclssmk;
import com.xpaas.zpbg.mapper.MkglMapper;
import com.xpaas.zpbg.mapper.ZcclMapper;
import com.xpaas.zpbg.mapper.ZcclglMapper;
import com.xpaas.zpbg.mapper.ZcclssmkMapper;
import com.xpaas.zpbg.service.IZcclService;
import com.xpaas.zpbg.service.IZcclssmkService;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZcclglVO;
import com.xpaas.zpbg.vo.ZcclssmkVO;
import com.xpaas.zpbg.wrapper.ZcclWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 教学评价-自评报告-备查材料 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class ZcclServiceImpl extends BaseServiceImpl<ZcclMapper, Zccl> implements IZcclService {
    @Autowired
    IZcclssmkService iZcclssmkService;

    @Autowired
    public IOssClient ossClient;

    @Autowired
    IZzclService zzclService;
    @Autowired
    ZcclssmkMapper zcclssmkMapper;
    @Autowired
    ZcclglMapper zcclglMapper;

    @Autowired
    private ZcclWrapper zcclWrapper;

    @Autowired
    MkglMapper mkglMapper;

    @Autowired
    IZjjyClient zjjyClient;

    @Override
    public IPage<ZcclVO> selectZcclPage(IPage<ZcclVO> page, ZcclVO zccl) {
        LoginUser user = AuthUtil.getUser();
        String ssmkids = zccl.getSsmkids();
        List<String> ssmkidslist = new ArrayList<>();
        if (ssmkids != null) {
            String[] ssmkidsArr = ssmkids.split(",");
            ssmkidslist = Arrays.asList(ssmkidsArr);
            zccl.setSsmkid(null);
        }
        return page.setRecords(baseMapper.selectZcclPage(page, zccl, user.getUserId(),ssmkidslist));
    }

    @Override
    public Boolean checkZccl(ZcclVO zccl) {
        return baseMapper.checkZccl(zccl).size() > 0;
    }

    @Override
    public List<ZcclVO> getZcclId(ZcclVO zccl) {
        return baseMapper.getZcclId(zccl);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveZZCLSSMK(ZcclVO zcclVO) {
        baseMapper.deleteByZcclId(zcclVO.getId());
        for (Long mkid : zcclVO.getMkidList()) {
            Zcclssmk zcclssmk = new Zcclssmk();
            zcclssmk.setZcclid(zcclVO.getId());
            zcclssmk.setMkid(mkid);
            iZcclssmkService.save(zcclssmk);
        }
        return true;
    }

    @Override
    public ZcclVO getZcclDetail(Zccl zccl) {
        Zccl detail = getOne(Condition.getQueryWrapper(zccl));
        ZcclVO zcclVO = zcclWrapper.entityVO(detail);
        List<ZcclssmkVO> zcclssmkVOList = zcclssmkMapper.selectZcclssmkList(zcclVO.getId());
        String ssmkIds = "";
        for (int i = 0; i < zcclssmkVOList.size(); i++) {
            ssmkIds = ssmkIds + zcclssmkVOList.get(i).getMkid() + ",";
        }
        ssmkIds = ssmkIds.substring(0, ssmkIds.length() - 1);
        zcclVO.setSsmk(ssmkIds);
        return zcclVO;
    }


    @Override
    public IPage<ZcclVO> getZzclList(ZcclglVO zcclVO, IPage<ZcclVO> page) {
        return page.setRecords(baseMapper.getZzclList(zcclVO,page));
    }
    @Override
    public void requestZcZjjyApi(Long clid,String wjlj,String name,String wjmc,int updataOrSave){
        String pdfData = "";
        List<String> yjzbidList = new ArrayList<>();
        List<String> ejzbidList  = new ArrayList<>();
        List<String> zygzjList  = new ArrayList<>();
        List<String> yjzbname = new ArrayList<>();
        List<String> ejzbname  = new ArrayList<>();
       SearchDTO searchDTO = new SearchDTO();
        //材料id
        searchDTO.setId(clid.toString());
        // 文件路径
        searchDTO.setUrl(wjlj);
        // 4备查材料
        searchDTO.setLx(4);
        //标题
        searchDTO.setTitle(name);
        if(!"".equals(wjlj)){
            pdfData = zjjyESApi(wjlj);
            // 内容
            searchDTO.setIntro(pdfData);
        }
        List<ZcclssmkVO> zcclssmkVOList = zcclssmkMapper.selectZcclssmkList(clid);
        if(zcclssmkVOList.size()>0){
            List<String> idlist = new ArrayList();
            for (ZcclssmkVO zcclssmkVO : zcclssmkVOList) {
                idlist.add(zcclssmkVO.getMkid().toString());
            }
            List<Mkgl> mkglList =  mkglMapper.getMkglIdDate(idlist);
            if(mkglList.size()>0){
                for (Mkgl mkgl : mkglList) {
                    yjzbidList.add(mkgl.getYjzbid()==null?"":mkgl.getYjzbid().toString());
                    yjzbname.add(mkgl.getYjzb()==null?"":mkgl.getYjzb());
                    ejzbidList.add(mkgl.getEjzbid()==null?"":mkgl.getEjzbid().toString());
                    ejzbname.add(mkgl.getEjzb()==null?"":mkgl.getEjzb());
                    zygzjList.add(mkgl.getBztxid()==null?"":mkgl.getBztxid().toString());
                }
                List<String> zygzdNamelist = baseMapper.getZygzdName(zygzjList);
                searchDTO.setYjzbid( String.join(",", yjzbidList));
                searchDTO.setEjzbid( String.join(",", ejzbidList));
                searchDTO.setZygzdid(String.join(",", zygzjList));
                searchDTO.setYjzbmc(String.join(",", yjzbname));
                searchDTO.setEjzbmc(String.join(",", ejzbname));
                searchDTO.setZygzdmc(String.join(",", zygzdNamelist));
                searchDTO.setWjmc(wjmc);
            }
            List<SearchDTO> list = new ArrayList<>();
            list.add(searchDTO);
            if(updataOrSave == 1){
                zjjyClient.createDocuments(list);
            }else if(updataOrSave == 2){
                zjjyClient.createDocument(searchDTO);
            }

        }

    }
    public String zjjyESApi(String wjlj) {
        //读取pdf文件
        try {
            String pdfBody = "";
            R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj, "minio11");
            if (inputR.isSuccess()) {
                log.info("取得文件流成功");
                byte[] bytes = inputR.getData();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                PDDocument document = PDDocument.load(inputStream);
                // 获取文档的目录
                PDDocumentCatalog catalog = document.getDocumentCatalog();
                // 获取文档的所有页面
                PDPageTree pages = catalog.getPages();
                for (int i = 1; i <= pages.getCount(); i++) {
                    PDFTextStripper stripper = new PDFTextStripper();
                    stripper.setStartPage(i);
                    stripper.setEndPage(i);
                    String content = stripper.getText(document);
                    content = content.replaceAll(" ", "").replaceAll(" +", "").replaceAll("[\\t\\n\\r]", "");
                    pdfBody += content;
                }
            }
            return pdfBody;
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }
    @Override
    public List<ZcclglVO> getQuote(Zcclgl zzclgl){
        return zcclglMapper.getQuote(zzclgl);
    }
    /**
     * 根据老的批次名称和新的批次名称更改备查材料的批次信息
     * @param oldPcid
     * @param newPcid
     */
    @Override
    public void updateZcclByPcid(Long oldPcid, Long newPcid) {
        //查询备查材料是否有老批次相关的并且和新的批次没有关系的
        List<Zccl> list = this.list(
                new QueryWrapper<Zccl>().lambda().eq(Zccl::getScbj, "0")
                        .apply(" FIND_IN_SET({0},PC) ", oldPcid)
                        .apply(" NOT FIND_IN_SET({0},PC) ", newPcid)
        );
        if(Func.isNotEmpty(list) && list.size()>0){//如果有则更改这种材料的批次信息
            baseMapper.updateZcclByPcid(oldPcid,newPcid);
        }
    }

    /**
     * 根据材料名称查询所有的相同名称的材料
     */
    @Override
    public List<ZcclVO> listByClmc(Zccl zccl){
        return baseMapper.listByClmc(zccl);
    }

}
