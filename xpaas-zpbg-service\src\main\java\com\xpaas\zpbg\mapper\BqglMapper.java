package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bqgl;
import com.xpaas.zpbg.vo.BqglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-标签管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface BqglMapper extends BaseMapper<Bqgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bqgl
	 * @return
	 */
	List<BqglVO> selectBqglPage(IPage page, BqglVO bqgl);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bqgl
	 * @return
	 */
	List<BqglVO> selectZjBqgl(IPage page, BqglVO bqgl);

}
