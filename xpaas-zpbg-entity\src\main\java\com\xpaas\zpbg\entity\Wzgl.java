package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-文章管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_WZGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Wzgl对象", description = "教学评价-自评报告-文章管理")
public class Wzgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 文章标题
	*/
	@ExcelProperty("文章标题")
	@ApiModelProperty(value = "文章标题")
	@TableField("WZBT")
	private String wzbt;

	/**
	* 文章摘要
	*/
	@ExcelProperty("文章摘要")
	@ApiModelProperty(value = "文章摘要")
	@TableField("WZZY")
	private String wzzy;

	/**
	* 文章内容
	*/
	@ExcelProperty("文章内容")
	@ApiModelProperty(value = "文章内容")
	@TableField("WZNR")
	private String wznr;

	/**
	* 发布人ID
	*/
	@ExcelProperty("发布人ID")
	@ApiModelProperty(value = "发布人ID")
	@TableField("FBR")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long fbr;

	/**
	* 发布人姓名
	*/
	@ExcelProperty("发布人姓名")
	@ApiModelProperty(value = "发布人姓名")
	@TableField("FBRXM")
	private String fbrxm;

	/**
	* 封面路径
	*/
	@ExcelProperty("封面路径")
	@ApiModelProperty(value = "封面路径")
	@TableField("FMLJ")
	private String fmlj;

	/**
	* PDF文件名称
	*/
	@ExcelProperty("PDF文件名称")
	@ApiModelProperty(value = "PDF文件名称")
	@TableField("PDFMC")
	private String pdfmc;

	/**
	* PDF文件路径
	*/
	@ExcelProperty("PDF文件路径")
	@ApiModelProperty(value = "PDF文件路径")
	@TableField("PDFLJ")
	private String pdflj;

	/**
	* 附件名称
	*/
	@ExcelProperty("附件名称")
	@ApiModelProperty(value = "附件名称")
	@TableField("FJMC")
	private String fjmc;

	/**
	* 附件路径
	*/
	@ExcelProperty("附件路径")
	@ApiModelProperty(value = "附件路径")
	@TableField("FJLJ")
	private String fjlj;

	/**
	* 压缩包路径
	*/
	@ExcelProperty("压缩包路径")
	@ApiModelProperty(value = "压缩包路径")
	@TableField("YSBLJ")
	private String ysblj;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	 * 展示位置
	 */
	@ExcelProperty("展示位置")
	@ApiModelProperty(value = "展示位置")
	@TableField("ZSWZ")
	private Integer zswz;

}
