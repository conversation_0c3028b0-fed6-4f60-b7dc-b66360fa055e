package com.xpaas.zpbg.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import com.xpaas.zpbg.dto.SelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 教学评价-自评报告-任务人员视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Rwry对象", description = "教学评价-自评报告-任务人员")
public class RwryVO extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "报告id")
	@ExcelProperty("报告id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	@ApiModelProperty(value = "报告名称")
	@ExcelProperty("报告名称")
	private String bgmc;

	@ApiModelProperty(value = "报告模块id")
	@ExcelProperty("报告模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	@ApiModelProperty(value = "报告年度")
	@ExcelProperty("报告年度")
	private String nd;

	@ApiModelProperty(value = "进度开始时间")
	@ExcelProperty("进度开始时间")
	private Date kssj;

	@ApiModelProperty(value = "模块id")
	@ExcelProperty("模块id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;

	@ApiModelProperty(value = "模块名称")
	@ExcelProperty("模块名称")
	private String mkmc;

	@ApiModelProperty(value = "主要关注点负责人")
	@ExcelProperty("主要关注点负责人")
	private String fzr;

	@ApiModelProperty(value = "主要关注点负责人列表")
	@ExcelProperty("主要关注点负责人列表")
	private List<SelDTO> fzrData;

	@ApiModelProperty(value = "撰写单位/撰写人")
	@ExcelProperty("撰写单位/撰写人")
	private String zxr;

	@ApiModelProperty(value = "撰写单位/撰写人列表")
	@ExcelProperty("撰写单位/撰写人列表")
	private List<SelDTO> zxrData;

	@ApiModelProperty(value = "审阅专家")
	@ExcelProperty("审阅专家")
	private String zj;

	@ApiModelProperty(value = "审阅专家")
	@ExcelProperty("审阅专家列表")
	private List<SelDTO> zjData;

	@ApiModelProperty(value = "排序")
	@ExcelProperty("排序")
	private Integer px;


}
