package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-报告模块操作记录实体类
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BGMKCZJL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bgmkczjl对象", description = "教学评价-自评报告-报告模块操作记录")
public class Bgmkczjl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 操作行为
	*/
	@ExcelProperty("操作行为")
	@ApiModelProperty(value = "操作行为")
	@TableField("CZXW")
	private Integer czxw;

	/**
	* 操作角色
	*/
	@ExcelProperty("操作角色")
	@ApiModelProperty(value = "操作角色")
	@TableField("CZJS")
	private Integer czjs;

	/**
	* 人员ID
	*/
	@ExcelProperty("人员ID")
	@ApiModelProperty(value = "人员ID")
	@TableField("RYID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long ryid;

	/**
	* 人员姓名
	*/
	@ExcelProperty("人员姓名")
	@ApiModelProperty(value = "人员姓名")
	@TableField("RYXM")
	private String ryxm;



}
