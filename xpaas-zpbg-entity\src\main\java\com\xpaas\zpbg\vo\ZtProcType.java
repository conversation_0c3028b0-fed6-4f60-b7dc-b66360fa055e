package com.xpaas.zpbg.vo;

import com.xpaas.core.tool.utils.StringUtil;

public enum ZtProcType {
    ZXKS10(10), // 10:撰写开始
    DZTJ20(20), // 20:点长提交
    SYKS30(30), // 30:审阅开始
    SYTJ40(40), // 40:审阅提交
    SYWC50(50), // 50:审阅完成
    DZDG70(70), // 70:点长定稿
    ZXZC91(91), // 91:撰写暂存
    ZXZC92(92), // 92:撰写提交
    ZXZC93(93), // 93:撰写编辑
    DZCH94(94), // 94:点长撤回
    QXDG95(95); // 95:取消定稿


    private final int value;

    ZtProcType(int value) {
        this.value = value;
    }

    public static ZtProcType fromValue(int value) {
        for (ZtProcType procType : ZtProcType.values()) {
            if (procType.getValue() == value) {
                return procType;
            }
        }
        throw new IllegalArgumentException("非法枚举值：" + value);
    }

    public static ZtProcType fromStr(String str) {
        if (StringUtil.isBlank(str)) {
            throw new IllegalArgumentException("非法枚举值：空");
        }
        int value = Integer.parseInt(str);
        return fromValue(value);
    }

    public int getValue() {
        return this.value;
    }
}
