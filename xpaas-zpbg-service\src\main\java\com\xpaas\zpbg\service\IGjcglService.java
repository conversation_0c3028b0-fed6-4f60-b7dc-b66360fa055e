package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Gjcgl;
import com.xpaas.zpbg.vo.GjcglVO;

import java.util.List;

/**
 * 教学评价-自评报告-关键词管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IGjcglService extends BaseService<Gjcgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcgl
	 * @return
	 */
	IPage<GjcglVO> selectGjcglPage(IPage<GjcglVO> page, GjcglVO gjcgl);

	/**
	 * 模块列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectMkglList(GjcglVO gjcgl);

	/**
	 * 报告列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectBgglList(GjcglVO gjcgl);

	/**
	 * 进度列表查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectJdglList(GjcglVO gjcgl);

	/**
	 * 最新进度查询
	 *
	 * @param gjcgl
	 * @return
	 */
	List<GjcglVO> selectZxjd(GjcglVO gjcgl);

}
