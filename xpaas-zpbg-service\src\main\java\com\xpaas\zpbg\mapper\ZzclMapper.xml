<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZzclMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zzclResultMap" type="com.xpaas.zpbg.entity.Zzcl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="CMM" property="cmm"/>
        <result column="WJH" property="wjh"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="YYZT" property="yyzt"/>
        <result column="YYCS" property="yycs"/>
        <result column="YYZTSTR" property="yyztstr"/>
        <result column="EDITOK" property="editOk"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="PC" property="pc"/>
        <result column="PCMC" property="pcmc"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zzclResultMapVO" type="com.xpaas.zpbg.vo.ZzclVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="CMM" property="cmm"/>
        <result column="WJH" property="wjh"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="YYZT" property="yyzt"/>
        <result column="YYCS" property="yycs"/>
        <result column="YYZTSTR" property="yyztstr"/>
        <result column="EDITOK" property="editOk"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="MKID" property="mkid"/>
        <result column="PC" property="pc"/>
        <result column="PCMC" property="pcmc"/>
    </resultMap>

    <select id="selectZzclPage" resultType="com.xpaas.zpbg.vo.ZzclVO">
        SELECT
        c.*,
        jm.mkmc,
        IFNULL(jy.yycs, 0) yycs,
        IF(jy.yycs IS NULL, 0, 1) yyzt
        ,glbg.bgmc
        FROM
        t_dt_jxpj_zpbg_zzcl c
        LEFT JOIN (
        -- 模块名分组
        SELECT
        c.id,
        GROUP_CONCAT(DISTINCT m.mkmc) mkmc
        FROM
        t_dt_jxpj_zpbg_zzcl c
        INNER JOIN t_dt_jxpj_zpbg_zzclssmk cm ON cm.zzclid = c.id
        INNER JOIN t_dt_jxpj_zpbg_mkgl m ON m.id = cm.mkid
        WHERE
        c.scbj = 0
        AND cm.scbj = 0
        AND m.scbj = 0
        GROUP BY
        c.id
        ) jm ON jm.id = c.id
        LEFT JOIN (
        -- 引用次数
        SELECT
        c.id,
        count(1) yycs
        FROM
        t_dt_jxpj_zpbg_zzcl c
        INNER JOIN t_dt_jxpj_zpbg_zzclgl cg ON cg.zzclid = c.id
        INNER JOIN t_dt_jxpj_zpbg_bbgl b ON b.id = cg.bbid
        INNER JOIN t_dt_jxpj_zpbg_bbpz p ON p.comment_id = cg.glkey AND p.bbid = cg.bbid
        INNER JOIN t_dt_jxpj_zpbg_bggl g ON g.id = b.bgid
        WHERE
        c.scbj = 0
        AND cg.scbj = 0
        AND b.scbj = 0
        AND p.scbj = 0
        AND g.scbj = 0
        AND b.bblx = 1
        GROUP BY c.id
        ) jy ON jy.id = c.id
        -- 模块所属
        <if test="(zzcl.ssmkid != null and zzcl.ssmkid != '') or (zzcl.mklx != null and zzcl.mklx != 0) or (ssmkidslist != null and ssmkidslist.size() > 0)">
            INNER JOIN (
            SELECT cm.zzclid id
            FROM
            t_dt_jxpj_zpbg_zzclssmk cm
            INNER JOIN t_dt_jxpj_zpbg_mkgl m ON m.id = cm.mkid
            WHERE
            cm.scbj = 0
            AND m.scbj = 0
            <if test="zzcl.ssmkid != null and zzcl.ssmkid != ''">
                AND m.id = #{zzcl.ssmkid}
            </if>
            <if test="ssmkidslist != null and ssmkidslist.size() > 0">
                <foreach collection="ssmkidslist" item="item" open=" AND m.id IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zzcl.mklx != null and zzcl.mklx != 0">
                AND m.mklx = #{zzcl.mklx}
            </if>
            GROUP BY cm.zzclid
            ) jg ON jg.id = c.id
        </if>
        -- 关联报告
        left join (
            select c.ID,GROUP_CONCAT(distinct bg.ID) bgid,GROUP_CONCAT(distinct bg.BGMC) bgmc,GROUP_CONCAT(distinct bg.ND) nd,GROUP_CONCAT(distinct bg.RWLX) rwlx
            from t_dt_jxpj_zpbg_zzcl c
            inner join t_dt_jxpj_zpbg_zzclgl gl on c.ID=gl.ZZCLID
            inner join t_dt_jxpj_zpbg_bggl bg on gl.BGID=bg.ID
            where c.SCBJ=0 and gl.SCBJ=0 and bg.SCBJ=0
            group by c.ID
        ) glbg on c.ID=glbg.ID
        WHERE
        c.scbj = 0
        <if test="zzcl.clmc != null and zzcl.clmc != ''">
            AND c.clmc LIKE CONCAT('%', #{zzcl.clmc}, '%')
        </if>
        <if test="zzcl.cmm != null and zzcl.cmm != ''">
            AND c.cmm LIKE CONCAT('%', #{zzcl.cmm}, '%')
        </if>
        <if test="zzcl.yyzt != null and zzcl.yyzt == 1">
            AND jy.id IS NOT NULL
        </if>
        <if test="zzcl.yyzt != null and zzcl.yyzt == 0">
            AND jy.id IS NULL
        </if>
        <if test="zzcl.bgId!=null and zzcl.bgId!=''">
            and find_in_set(#{zzcl.bgId},glbg.bgid)
        </if>
        <if test="zzcl.nd!=null and zzcl.nd!=''">
            and (find_in_set(#{zzcl.nd},glbg.nd) or c.nd=#{zzcl.nd})
        </if>
        <if test="zzcl.rwlx!=null and zzcl.rwlx!=''">
            and (find_in_set(#{zzcl.rwlx},glbg.rwlx) or c.rwlx=#{zzcl.rwlx})
        </if>
        ORDER BY c.px ASC, c.id ASC
    </select>

    <select id="selectZzclPage_BACKUP" resultMap="zzclResultMapVO">
        select *,
        ( SELECT GROUP_CONCAT(T_DT_JXPJ_ZPBG_MKGL.MKMC) FROM T_DT_JXPJ_ZPBG_MKGL INNER JOIN t_dt_jxpj_zpbg_zzclssmk
        ON t_dt_jxpj_zpbg_zzclssmk.MKID = T_DT_JXPJ_ZPBG_MKGL.id WHERE t_dt_jxpj_zpbg_zzclssmk.ZZCLID =
        T_DT_JXPJ_ZPBG_ZZCL.id ) AS MKMC,
        CASE
        ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZZCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZZCLGL WHERE T_DT_JXPJ_ZPBG_ZZCLGL.zzclid =
        T_DT_JXPJ_ZPBG_ZZCL.id and T_DT_JXPJ_ZPBG_ZZCLGL.scbj = 0 )
        WHEN 0 THEN
        0 ELSE 1
        END AS yyzt ,
        ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZZCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZZCLGL INNER JOIN T_DT_JXPJ_ZPBG_BBGL ON
        T_DT_JXPJ_ZPBG_ZZCLGL.bbid = T_DT_JXPJ_ZPBG_BBGL.id AND T_DT_JXPJ_ZPBG_BBGL.bblx = 1 WHERE
        T_DT_JXPJ_ZPBG_ZZCLGL.zzclid =
        T_DT_JXPJ_ZPBG_ZZCL.id and T_DT_JXPJ_ZPBG_ZZCLGL.scbj = 0 ) AS yycs,
        ( CASE cjr WHEN #{userId} THEN 0 ELSE 1 END
        + ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZZCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZZCLGL WHERE T_DT_JXPJ_ZPBG_ZZCLGL.zzclid =
        T_DT_JXPJ_ZPBG_ZZCL.id )
        ) AS editOk
        from T_DT_JXPJ_ZPBG_ZZCL where scbj = 0

        <if test="zzcl.clmc != null and zzcl.clmc != ''">
            and clmc like concat('%', #{zzcl.clmc}, '%')
        </if>
        <if test="zzcl.cmm != null and zzcl.cmm != ''">
            and cmm like concat('%', #{zzcl.cmm}, '%')
        </if>
        <if test="zzcl.yyzt != null and zzcl.yyzt == 1">
            and ID in (select CASE
            WHEN zzclid IS NULL THEN 0
            ELSE zzclid
            END AS zzclid from T_DT_JXPJ_ZPBG_ZZCLGL where scbj = 0 )
        </if>
        <if test="zzcl.yyzt != null and zzcl.yyzt == 0">
            and ID NOT IN (select CASE
            WHEN zzclid IS NULL THEN 0
            ELSE zzclid
            END AS zzclid from T_DT_JXPJ_ZPBG_ZZCLGL where scbj = 0)
        </if>
        <if test="zzcl.ssmkid != null and zzcl.ssmkid != ''">
            and id in (select zzclid from t_dt_jxpj_zpbg_zzclssmk where scbj = 0 and mkid = #{zzcl.ssmkid} )
        </if>
        <if test="zzcl.mklx != null and zzcl.mklx != 0">
            and id in (select zzclid from t_dt_jxpj_zpbg_zzclssmk where mkid in (select id from T_DT_JXPJ_ZPBG_MKGL
            where scbj = 0 and mklx = #{zzcl.mklx} ) )
        </if>

        order by px asc
    </select>

    <select id="checkZzcl" resultMap="zzclResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZZCL where scbj = 0
        <if test="clmc != null and clmc != ''">
            and clmc = #{clmc}
        </if>
        <if test="cmm != null and cmm != ''">
            and cmm = #{cmm}
        </if>
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <delete id="deleteByZzclId" parameterType="Long">
        delete
        from t_dt_jxpj_zpbg_zzclssmk
        where ZZCLID = #{zzclId}
    </delete>

    <select id="getZzclList" resultMap="zzclResultMapVO">
        SELECT DISTINCT
        zzcl.*
        FROM
        T_DT_JXPJ_ZPBG_ZZCL zzcl
        INNER JOIN t_dt_jxpj_zpbg_zzclssmk zzclssmk ON zzcl.id = zzclssmk.ZZCLID
        where zzcl.scbj = 0 and zzclssmk.scbj = 0
        <if test="zzcl.clmc != null and zzcl.clmc != ''">
            and (zzcl.clmc like concat('%', #{zzcl.clmc}, '%') or zzcl.cmm like concat('%', #{zzcl.clmc}, '%'))
        </if>
        <if test="zzcl.ssmkid != null">
            and zzclssmk.MKID IN (SELECT MKID FROM T_DT_JXPJ_ZPBG_BGMK WHERE id = #{zzcl.ssmkid} )
            and zzcl.id not in (select zzclid from T_DT_JXPJ_ZPBG_ZZCLGL where bgmkid = #{zzcl.ssmkid} and bgid =
            #{zzcl.bgid} and bbid = #{zzcl.bbid} and scbj = 0)
        </if>
    </select>
    <select id="getZzclId" resultMap="zzclResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZZCL where scbj = 0
        <if test="clmc != null and clmc != ''">
            and clmc = #{clmc}
        </if>
        <if test="cmm != null and cmm != ''">
            and cmm = #{cmm}
        </if>
        <if test="wjh != null and wjh != ''">
            and wjh = #{wjh}
        </if>
        <if test="wjlj != null and wjlj != ''">
            and wjlj = #{wjlj}
        </if>
    </select>


    <update id="updateZzclByPcid">
        update T_DT_JXPJ_ZPBG_ZZCL set PC = CONCAT(PC,',',#{newPcid}) where FIND_IN_SET(#{oldPcid},PC) AND NOT FIND_IN_SET(#{newPcid},PC)
    </update>

    <!-- 根据材料名称查询所有的相同名称的材料 -->
    <select id="listByClmc" resultType="com.xpaas.zpbg.vo.ZzclVO">
        select t1.ID ID,t1.CLMC,t5.ID BG_ID,t5.BGMC BGMC
        from T_DT_JXPJ_ZPBG_ZZCL t1
        left join T_DT_JXPJ_ZPBG_ZZCLGL t4 on t1.ID=t4.ZZCLID and t4.SCBJ=0
        left join T_DT_JXPJ_ZPBG_BGGL t5 on t4.BGID=t5.ID and t5.SCBJ=0
        where t1.SCBJ=0
        and t1.CLMC=#{cl.clmc}
        and (
            t1.RWLX=#{cl.rwlx} or exists(
                select *
                from T_DT_JXPJ_ZPBG_ZZCLGL t2
                left join T_DT_JXPJ_ZPBG_BGGL t3 on t2.BGID=t3.ID and t3.SCBJ=0
                where t1.ID=t2.ZZCLID and t2.SCBJ=0 and t3.SCBJ=0 and t3.RWLX=#{cl.rwlx}
            )
        )
        group by t1.ID,t5.ID
    </select>

</mapper>
