package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Zygzdgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 教学评价-同步标准体系
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@ApiModel(value = "ZygzdglVO对象", description = "教学评价-同步标准体系")
public class ZygzdglSync {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;
    @ApiModelProperty(value = "操作对象")
    private String meth;
    @ApiModelProperty(value = "操作对象的具体内容")
    private Zygzdgl detail;
    @ApiModelProperty(value = "操作")
    private String action;
    @ApiModelProperty(value = "时间")
    private String time;


}
