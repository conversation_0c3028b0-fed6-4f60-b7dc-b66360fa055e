package com.xpaas.zlkgl.service;

import com.xpaas.zlkgl.dto.CopyRequest;
import com.xpaas.zlkgl.dto.CopyResult;

/**
 * 复制服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ICopyService {

    /**
     * 复制文件或文件夹
     *
     * @param request 复制请求
     * @return 复制结果
     */
    CopyResult copy(CopyRequest request);

    /**
     * 复制文件
     *
     * @param sourceId 源文件ID
     * @param targetParentId 目标父文件夹ID
     * @param newName 新名称（可选）
     * @return 复制结果
     */
    CopyResult copyFile(String sourceId, String targetParentId, String newName);

    /**
     * 复制普通文件夹
     *
     * @param sourceId 源文件夹ID
     * @param targetParentId 目标父文件夹ID
     * @param newName 新名称（可选）
     * @return 复制结果
     */
    CopyResult copyFlglFolder(String sourceId, String targetParentId, String newName);

    /**
     * 复制外校文件夹
     *
     * @param sourceId 源文件夹ID
     * @param targetParentId 目标父文件夹ID
     * @param newName 新名称（可选）
     * @return 复制结果
     */
    CopyResult copyWxzlFolder(String sourceId, String targetParentId, String newName);

    /**
     * 生成唯一名称（处理命名冲突）
     *
     * @param baseName 基础名称
     * @param parentId 父级ID
     * @param copyType 复制类型
     * @return 唯一名称
     */
    String generateUniqueName(String baseName, String parentId, CopyRequest.CopyType copyType);
}
