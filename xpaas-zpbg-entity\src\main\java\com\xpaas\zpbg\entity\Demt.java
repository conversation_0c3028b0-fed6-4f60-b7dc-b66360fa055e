package com.xpaas.zpbg.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教学评价-自评报告配置管理-文档 实体类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "document对象", description = "教学评价-自评报告配置管理-文档 实体类")
public class Demt extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 报告ID
	 */
	@ApiModelProperty(value = "报告ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	 * 版本ID
	 */
	@ApiModelProperty(value = "版本ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "文件路径")
	private String wjlj;

	/**
	 * 一级指标
	 */
	@ApiModelProperty(value = "一级指标")
	private String yjzb;

	/**
	 * 二级指标
	 */
	@ApiModelProperty(value = "二级指标")
	private String ejzb;

	/**
	 * 模块名称
	 */
	@ApiModelProperty(value = "模块名称")
	private String mkmc;

	/**
	 * 自评等级
	 */
	@ApiModelProperty(value = "自评等级")
	private String zpdj;

	/**
	 * 牵头单位
	 */
	@ApiModelProperty(value = "牵头单位")
	private String qtdw;

	/**
	 * 是否为封面
	 */
	@ApiModelProperty(value = "是否为封面")
	private Integer sfwfm;

	/**
	 * 模块类型
	 */
	@ApiModelProperty(value = "模块类型")
	private Integer mklx;

	/**
	 * 模版ID字符串
	 */
	@ApiModelProperty(value = "模版ID字符串")
	private String mbids;

	/**
	 * 模版ID列表
	 */
	@ApiModelProperty(value = "模版ID列表")
	private List<String> mbidList;

	/**
	 * 导出内容
	 */
	@ApiModelProperty(value = "导出内容")
	private String dcnr;

	/**
	 * 任务ID
	 */
	@ApiModelProperty(value = "任务ID")
	private String taskId;

	/**
	 * 模块数量
	 */
	@ApiModelProperty(value = "模块数量")
	private Integer mksl;

	/**
	 * 步骤数量
	 */
	@ApiModelProperty(value = "步骤数量")
	private Integer bzsl;

	/**
	 * 步骤进度
	 */
	@ApiModelProperty(value = "步骤进度")
	private Integer bzjd;
}
