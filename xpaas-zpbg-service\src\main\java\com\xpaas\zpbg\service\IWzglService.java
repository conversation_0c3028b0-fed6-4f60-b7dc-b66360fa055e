package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Wzgl;
import com.xpaas.zpbg.vo.WzglVO;

import java.util.List;

/**
 * 自评报告-文章管理 服务类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
public interface IWzglService extends BaseService<Wzgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param wzgl
	 * @return
	 */
	IPage<WzglVO> selectWzPage(IPage<WzglVO> page, WzglVO wzgl);

	/**
	 * 取得慕课
	 *
	 * @return
	 */
	List<WzglVO> getKcList();

	/**
	 * 取得浏览人数
	 *
	 * @return
	 */
	int getLlrs(WzglVO wzglVO);
}
