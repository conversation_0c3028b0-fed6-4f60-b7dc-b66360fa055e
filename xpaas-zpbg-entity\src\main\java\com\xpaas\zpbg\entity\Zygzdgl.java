package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-设置-主要关注点管理实体类
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@TableName("T_DT_JXPJ_SZ_ZYGZDGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zygzdgl对象", description = "教学评价-设置-主要关注点管理")
public class Zygzdgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 指标名称
	*/
	@ExcelProperty("指标名称")
	@ApiModelProperty(value = "指标名称")
	@TableField("ZBMC")
	private String zbmc;

	/**
	* 上级指标
	*/
	@ExcelProperty("上级指标")
	@ApiModelProperty(value = "上级指标")
	@TableField("SJZB")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long sjzb;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 是否关联互动交流 1是 0否
	*/
	@ExcelProperty("是否关联互动交流 1是 0否")
	@ApiModelProperty(value = "是否关联互动交流 1是 0否")
	@TableField("SFGLHDJL")
	private Integer sfglhdjl;

	/**
	* 来源
	*/
	@ExcelProperty("来源")
	@ApiModelProperty(value = "来源")
	@TableField("LY")
	private String ly;

	/**
	* 层级
	*/
	@ExcelProperty("层级")
	@ApiModelProperty(value = "层级")
	@TableField("CJ")
	private Integer cj;

	/**
	* 主要关注点负责人
	*/
	@ExcelProperty("主要关注点负责人")
	@ApiModelProperty(value = "主要关注点负责人")
	@TableField("ZYGZDFZR")
	private String zygzdfzr;

	/**
	* 主要关注点负责人_名称
	*/
	@ExcelProperty("主要关注点负责人_名称")
	@ApiModelProperty(value = "主要关注点负责人_名称")
	@TableField("ZYGZDFZR_STR")
	private String zygzdfzrStr;

	/**
	* 指标来源
	*/
	@ExcelProperty("指标来源")
	@ApiModelProperty(value = "指标来源")
	@TableField("ZBLY")
	private Integer zbly;



}
