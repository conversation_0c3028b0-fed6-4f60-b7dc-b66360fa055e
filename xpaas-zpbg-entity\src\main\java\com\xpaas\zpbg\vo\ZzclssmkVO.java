package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Zzclssmk;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-佐证材料所属模块视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZzclssmkVO对象", description = "教学评价-自评报告-佐证材料所属模块")
public class ZzclssmkVO extends Zzclssmk {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
