package com.xpaas.zlkgl.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.core.tool.api.R;
import com.xpaas.zlkgl.entity.ZlkJdgl;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-资料库平台-节点管理表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IZlkJdglService extends BaseService<ZlkJdgl> {

	//时间轴查询
	List<Map<String,Object>> listBySjz(Map<String, Object> map);

	//根据ids删除节点
	R deleteByIds(List<Long> ids);

}
