package com.xpaas.zpbg.entity;

import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告配置管理-树 实体类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "tree对象", description = "教学评价-自评报告配置管理-树 实体类")
public class Tree extends TenantEntity {

	private static final long serialVersionUID = 1L;
}
