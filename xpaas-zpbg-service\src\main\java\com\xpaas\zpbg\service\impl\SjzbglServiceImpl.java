package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.entity.Sjzbgl;
import com.xpaas.zpbg.mapper.SjzbglMapper;
import com.xpaas.zpbg.service.ISjzbglService;
import com.xpaas.zpbg.utils.ZpbgUtils;
import com.xpaas.zpbg.vo.SjclVO;
import com.xpaas.zpbg.vo.SjzbglVO;
import com.xpaas.zpbg.wrapper.SjzbglWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教学评价-自评报告-数据指标关联 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class SjzbglServiceImpl extends BaseServiceImpl<SjzbglMapper, Sjzbgl> implements ISjzbglService {

	@Autowired
	private SjzbglWrapper sjzbglWrapper;

	@Override
	public IPage<SjzbglVO> selectSjzbglPage(IPage<SjzbglVO> page, SjzbglVO sjzbgl) {
		return page.setRecords(baseMapper.selectSjzbglPage(page, sjzbgl));
	}

	@Override
	public String sameGl(SjzbglVO sjzbglVO) {
		// 判断重复关联
		checkGl(sjzbglVO);

		String msg = "";
		List<SjzbglVO> glList = baseMapper.sameGl(sjzbglVO);

		if(glList != null && glList.size() > 0) {
			List<String> glwz = new ArrayList();

			for(SjzbglVO vo : glList) {
				glwz.add(ZpbgUtils.convertGlwz(vo.getGlwz()));
			}

			if(sjzbglVO.getSjzblx() == 1) {
				msg =  "数据指标《" + sjzbglVO.getZbmc() + "》已在报告" + Joiner.on("、").join(glwz) + "处引用，确定要引用吗？";
			} else {
				msg =  "指标关键词《" + sjzbglVO.getGjcmc() + "》已在报告" + Joiner.on("、").join(glwz) + "处引用，确定要引用吗？";
			}
		}

		return msg;
	}

	@Override
	public boolean save(SjzbglVO sjzbglVO) {
		// 判断重复关联
		int px = checkGl(sjzbglVO);

		sjzbglVO.setPx(px);
		super.save(sjzbglVO);

		return true;
	}

	private int checkGl(SjzbglVO sjzbglVO) {
		int px = 1;
		
		// 没有关联KEY时，不用校验
		if(StringUtil.isEmpty(sjzbglVO.getGlkey())) {
			return px;
		}

		List<SjzbglVO> glList = baseMapper.getGlList(sjzbglVO);
		if(glList != null) {
			for(SjzbglVO vo : glList) {
				if(sjzbglVO.getSjzblx() == 1) {
					if(sjzbglVO.getZbmc().equals(vo.getZbmc())) {
						throw new ServiceException("此关联已添加！");
					}
				}

				if(sjzbglVO.getSjzblx() == 2) {
					if(sjzbglVO.getGjcid().equals(vo.getGjcid())) {
						throw new ServiceException("此关联已添加！");
					}
				}

				px = vo.getPx() + 1;
			}
		}

		return px;
	}

	@Override
	public List<SjzbglVO> searchGjc(SjzbglVO sjzbglVO) {
		return baseMapper.searchGjc(sjzbglVO);
	}

	@Override
	public List<String> checkGlkey(SjzbglVO sjzbglVO) {
		return baseMapper.checkGlkey(sjzbglVO);
	}

	@Override
	public List<SjzbglVO> getGlAllList(SjzbglVO sjzbglVO) {
		return baseMapper.getGlAllList(sjzbglVO);
	}

	@Override
	public List<SjclVO> searchSjlc(SjzbglVO sjzbglVO) {
		return baseMapper.searchSjlc(sjzbglVO);
	}

	@Override
	public void deleteGl(String ids) {
		if(StringUtil.isNotBlank(ids)) {
			String[] array = ids.split(",");

			for(String id : array) {
				Sjzbgl sjzbgl = this.getById(id);
				this.deleteLogic(Func.toLongList(id));
			}
		} else {
			return;
		}

		// 取得已关联的数据，并刷新序号
//		List<SjzbglVO> glList = baseMapper.getGlList(sjzbglWrapper.entityVO(sjzbgl));
//		if(glList != null) {
//			for(int i = 1; i <= glList.size(); i++) {
//				SjzbglVO vo = glList.get(i - 1);
//				vo.setPx(i);
//
//				this.updateById(vo);
//			}
//		}
	}

}
