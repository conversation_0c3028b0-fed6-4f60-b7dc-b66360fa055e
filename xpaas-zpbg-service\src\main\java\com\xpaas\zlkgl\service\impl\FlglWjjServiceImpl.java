package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.zlkgl.dto.CopyResult;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.FlglWjjMapper;
import com.xpaas.zlkgl.service.ICopyService;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.utils.TreeUtils;
import com.xpaas.zlkgl.vo.FlglWjjVO;
import com.xpaas.zlkgl.wrapper.FlglWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 教学评价-资料库平台-分类管理文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlglWjjServiceImpl extends BaseServiceImpl<FlglWjjMapper, FlglWjj> implements IFlglWjjService {

    private final FlglWjjWrapper flglWjjWrapper;
    private final IZlglService zlglService;
    private final ICopyService copyService;

    @Override
    public IPage<FlglWjjVO> selectFlglWjjPage(IPage<FlglWjjVO> page, FlglWjjVO flglWjj) {
        return page.setRecords(baseMapper.selectFlglWjjPage(page, flglWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<FlglWjjVO> listTree(FlglWjjVO bo) {
        QueryWrapper<FlglWjj> queryWrapper = Condition.getQueryWrapper(bo);
        queryWrapper.lambda().isNotNull(FlglWjj::getWjjMc).ne(FlglWjj::getWjjMc, "");
        List<FlglWjjVO> vos = flglWjjWrapper.listVO(baseMapper.selectList(queryWrapper));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                FlglWjj::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(FlglWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new com.xpaas.core.log.exception.ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 转换为实体类并保存
        return save(flglWjjWrapper.wrapperVO(vo));
    }

    /**
     * 修改
     */
    @Override
    public boolean update(FlglWjjVO vo) {

        // 1. 获取原数据
        FlglWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new com.xpaas.core.log.exception.ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new com.xpaas.core.log.exception.ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlglWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new com.xpaas.core.log.exception.ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 更新数据
        return baseMapper.updateById(flglWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 删除
     */
    @Override
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            FlglWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<FlglWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(FlglWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }

    /**
     * 统计节点下的各个评价类型资源数（文件 + 链接）
     */
    @Override
    public List<Map<String, Object>> countResourcesByPjLx(String pjLx, String jdId) {
        return baseMapper.countResourcesByPjLx(pjLx, jdId);
    }

    @Override
    public CopyResult copyFolder(String sourceId, String targetParentId, String newName) {
        return copyService.copyFlglFolder(sourceId, targetParentId, newName);
    }


}
