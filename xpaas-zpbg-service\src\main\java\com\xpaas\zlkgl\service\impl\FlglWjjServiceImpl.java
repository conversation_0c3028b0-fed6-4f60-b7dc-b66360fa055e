package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.FlglWjjMapper;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.utils.CopyHelper;
import com.xpaas.zlkgl.utils.TreeUtils;
import com.xpaas.zlkgl.vo.FlglWjjVO;
import com.xpaas.zlkgl.wrapper.FlglWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 教学评价-资料库平台-分类管理文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlglWjjServiceImpl extends BaseServiceImpl<FlglWjjMapper, FlglWjj> implements IFlglWjjService {

    private final FlglWjjWrapper flglWjjWrapper;
    private final IZlglService zlglService;
    private final CopyHelper copyHelper;

    @Override
    public IPage<FlglWjjVO> selectFlglWjjPage(IPage<FlglWjjVO> page, FlglWjjVO flglWjj) {
        return page.setRecords(baseMapper.selectFlglWjjPage(page, flglWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<FlglWjjVO> listTree(FlglWjjVO bo) {
        QueryWrapper<FlglWjj> queryWrapper = Condition.getQueryWrapper(bo);
        queryWrapper.lambda().isNotNull(FlglWjj::getWjjMc).ne(FlglWjj::getWjjMc, "");
        List<FlglWjjVO> vos = flglWjjWrapper.listVO(baseMapper.selectList(queryWrapper));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                FlglWjj::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(FlglWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new com.xpaas.core.log.exception.ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 转换为实体类并保存
        return save(flglWjjWrapper.wrapperVO(vo));
    }

    /**
     * 修改
     */
    @Override
    public boolean update(FlglWjjVO vo) {

        // 1. 获取原数据
        FlglWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new com.xpaas.core.log.exception.ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new com.xpaas.core.log.exception.ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlglWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new com.xpaas.core.log.exception.ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 更新数据
        return baseMapper.updateById(flglWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 删除
     */
    @Override
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            FlglWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<FlglWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(FlglWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }

    /**
     * 统计节点下的各个评价类型资源数（文件 + 链接）
     */
    @Override
    public List<Map<String, Object>> countResourcesByPjLx(String pjLx, String jdId) {
        return baseMapper.countResourcesByPjLx(pjLx, jdId);
    }

    /**
     * 复制文件夹（包括子文件夹和文件）
     *
     * @param sourceId 源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @param newName 新名称（可选，为空时自动生成"副本"名称）
     * @return 复制是否成功
     */
    @Override
    public boolean copyFolder(String sourceId, String targetFolderId, String newName) {
        try {
            // 1. 查询源文件夹
            FlglWjj sourceFolder = this.getById(sourceId);
            if (sourceFolder == null) {
                log.error("源文件夹不存在，ID: {}", sourceId);
                return false;
            }

            // 2. 校验自定义名称
            if (newName != null) {
                String validationError = copyHelper.validateName(newName);
                if (validationError != null) {
                    log.error("文件夹名校验失败: {}", validationError);
                    return false;
                }
            }

            // 3. 生成最终名称
            String finalName = newName != null ? newName.trim() :
                copyHelper.generateUniqueName(sourceFolder.getWjjMc(), targetFolderId,
                    name -> isFolderNameExists(name, targetFolderId));

            // 4. 创建新文件夹记录
            FlglWjj newFolder = new FlglWjj();
            copyFolderProperties(sourceFolder, newFolder);
            newFolder.setId(null); // 让数据库自动生成新ID
            newFolder.setFjWjjId(targetFolderId);
            newFolder.setWjjMc(finalName);

            // 5. 保存新文件夹
            if (!this.save(newFolder)) {
                log.error("保存新文件夹失败");
                return false;
            }

            // 6. 如果是普通文件夹(wjjLx=1)，递归复制子项
            if ("1".equals(sourceFolder.getWjjLx())) {
                return copyFolderContents(sourceId, String.valueOf(newFolder.getId()));
            }

            return true;
        } catch (Exception e) {
            log.error("复制文件夹失败，sourceId: {}, targetFolderId: {}, newName: {}", sourceId, targetFolderId, newName, e);
            return false;
        }
    }

    /**
     * 复制文件夹内容（子文件夹和文件）
     *
     * @param sourceFolderId 源文件夹ID
     * @param targetFolderId 目标文件夹ID
     * @return 复制是否成功
     */
    private boolean copyFolderContents(String sourceFolderId, String targetFolderId) {
        try {
            // 1. 复制子文件夹
            List<FlglWjj> subFolders = this.list(
                new LambdaQueryWrapper<FlglWjj>().eq(FlglWjj::getFjWjjId, sourceFolderId)
            );
            for (FlglWjj subFolder : subFolders) {
                if (!copyFolder(String.valueOf(subFolder.getId()), targetFolderId, null)) {
                    log.error("复制子文件夹失败，ID: {}", subFolder.getId());
                    return false;
                }
            }

            // 2. 复制文件
            List<Zlgl> files = zlglService.list(
                new LambdaQueryWrapper<Zlgl>().eq(Zlgl::getWjjId, sourceFolderId)
            );
            for (Zlgl file : files) {
                if (!zlglService.copyFile(String.valueOf(file.getId()), targetFolderId, null)) {
                    log.error("复制文件失败，ID: {}", file.getId());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("复制文件夹内容失败", e);
            return false;
        }
    }



    /**
     * 检查文件夹名是否已存在
     *
     * @param folderName 文件夹名
     * @param parentId 父文件夹ID
     * @return 是否存在
     */
    private boolean isFolderNameExists(String folderName, String parentId) {
        return this.count(new LambdaQueryWrapper<FlglWjj>()
            .eq(FlglWjj::getFjWjjId, parentId)
            .eq(FlglWjj::getWjjMc, folderName)) > 0;
    }

    /**
     * 复制文件夹属性
     *
     * @param source 源文件夹
     * @param target 目标文件夹
     */
    private void copyFolderProperties(FlglWjj source, FlglWjj target) {
        target.setJdId(source.getJdId());
        target.setFlglLb(source.getFlglLb());
        target.setPjLx(source.getPjLx());
        target.setWjjMc(source.getWjjMc());
        target.setWjjLx(source.getWjjLx());
        target.setWbljDz(source.getWbljDz());
        target.setWjjPx(source.getWjjPx());
    }


}
