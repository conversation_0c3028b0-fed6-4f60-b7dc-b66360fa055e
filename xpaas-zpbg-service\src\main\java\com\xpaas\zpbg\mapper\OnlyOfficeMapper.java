package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.xpaas.zpbg.dto.HeaderValueDTO;
import com.xpaas.zpbg.vo.OnlyOfficeTreeList;
import com.xpaas.zpbg.vo.OnlyOfflceTreeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * onlyOffice Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Repository
public interface OnlyOfficeMapper{

	/**
	 * 获取进度管理模块ID
	 * @param bbglId 版本管理ID
	 * @return
	 */
	@SqlParser(filter = true)
	HeaderValueDTO getJdglMkIdValue(String bbglId);

	/**
	 * 获取撰写信息
	 * @param bbglId 版本管理ID
	 * @return
	 */
	@SqlParser(filter = true)
	HeaderValueDTO getHeaderValueOther(String bbglId);

	/**
	 * 取报告名称、模块名称
	 * @param bbglId
	 * @return
	 */
	@SqlParser(filter = true)
	HeaderValueDTO getGlHeader(String bbglId);

	/**
	 * 去撰写人信息
	 * @param reportId 报告id
	 * @param bgmkId 报告模块id
	 * @return
	 */
	@SqlParser(filter = true)
	List<HeaderValueDTO> getHeaderValue(@Param("reportId") String reportId,@Param("bgmkId") String bgmkId);

	/**
	 * 获取任务分工
	 * @param reportId
	 * @param bgmkId
	 * @param userid
	 * @return
	 */
	@SqlParser(filter = true)
	List<HeaderValueDTO>  getUserClzt(@Param("reportId") String reportId,@Param("bgmkId") String bgmkId,@Param("userid") String userid);

	/**
	 * 取专家姓名
	 * @param reportId 报告id
	 * @param bgmkId 报告模块id
	 * @param bbId 版本id
	 * @return
	 */
	@SqlParser(filter = true)
	List<HeaderValueDTO> getHeaderValuezj(@Param("reportId") String reportId,@Param("bgmkId") String bgmkId,@Param("bbId") String bbId);

	/**
	 * 获取模块列表
	 * @param jdglId 进度管理id
	 * @return
	 */
	@SqlParser(filter = true)
	List<OnlyOfflceTreeVO>  getMkTree1(@Param("jdglId") String jdglId);

	/**
	 * 获取二级模块列表
	 * @param jdglid 进度管理id
	 * @param yjzbid 一级指标id
	 * @return
	 */
	@SqlParser(filter = true)
	List<OnlyOfflceTreeVO>  getMkTree2(@Param("jdglId") String jdglid,@Param("yjzbId") String yjzbid);

	/**
	 * 获取三级模块列表
	 * @param jdglId 进度管理id
	 * @param yjzbid 一级指标id
	 * @param ejzbid 二级指标ID
	 * @return
	 */
	@SqlParser(filter = true)
	List<OnlyOfflceTreeVO>  getMkTree3(String jdglId ,@Param("yjzbId") String yjzbid, @Param("ejzbId") String ejzbid);

	/**
	 * 判断当前人员是否在任务分工中
	 * @param bbglId 版本管理ID
	 * @param currentUserId 当前用户ID
	 * @return count
	 */
	int selectCountZxr(@Param("bbglId") String bbglId, @Param("userId") String currentUserId);

	/**
	 * 判断当前专家是否在审阅列表中
	 * @param bbglId 版本管理ID
	 * @param currentUserId 当前用户ID
	 * @return count
	 */
	int selectCountZj(@Param("bbglId") String bbglId, @Param("userId") String currentUserId);

	/**
	 * 查询历史版本
	 * @param bbglId 报告管理ID
	 * @return
	 */
	OnlyOfflceTreeVO selectHistoryVersion(String bbglId);


	/**
	 * 获取模块
	 * @param jdglid 进度管理id
	 * @return
	 */
	List<OnlyOfflceTreeVO> getMkTreeByBgid(String jdglid);

	/**
	 * 获取二级模块
	 * @param bgid 进度管理id
	 * @param yjzbId 一级指标ID
	 * @return
	 */
	List<OnlyOfflceTreeVO> getMkTree2ByBgid(@Param("bgid") String bgid, @Param("yjzbId") String yjzbId);

	/**
	 * 获取三级模块
	 * @param bgid 报告id
	 * @param yjzbid 一级指标id
	 * @param ejzbid 二级指标id
	 * @return
	 */
	@SqlParser(filter = true)
	List<OnlyOfflceTreeVO> getMkTree3ByBgid(@Param("bgid") String bgid ,@Param("yjzbId") String yjzbid, @Param("ejzbId") String ejzbid);

	/**
	 * 获取文件key
	 * @param bgmkid 报告模块ID
	 * @return
	 */
	String getWjkeyByBgmkid(String bgmkid);

	/**
	 * 获取撰写人负责模块
	 * @param jdglid 进度管理ID
	 * @param userId 用户ID
	 * @return
	 */
	List<OnlyOfficeTreeList> getZxrMkList(@Param("jdglid") String jdglid, @Param("userId") String userId);

	/**
	 * 获取专家模块
	 * @param jdglid 进度管理ID
	 * @param userId 用户ID
	 * @return
	 */
	List<OnlyOfficeTreeList> getZjMkList(@Param("jdglid") String jdglid, @Param("userId") String userId);

	/**
	 * 更新新版本文件路径 文件key
	 * @param params
	 * @return
	 */
    boolean updateNewBb(Map<String, String> params);

	/**
	 * 查询主要关注点
	 * @param bgmkId 报告模块id
	 * @return
	 */
	Map<String,String> selectZygzdByBgmkId(String bgmkId);

	/**
	 * 查询点长列表
	 * @param value 报告模块id
	 * @return
	 */
	List<Map<String, String>> getDzList(String value);

	/**
	 * 获取报告的报告模块ID
	 * @param bgid 报告ID
	 * @return bgmkid
	 */
	List<String> getJdmkIds(String bgid);

	/**
	 * 取报告模块状态
	 * @param bgmkid 报告模块id
	 * @return
	 */
	String getBgmkzt(String bgmkid);

	/**
	 * 通过报告模块ID获取主线版本ID
	 * @param bgmkId 报告模块ID
	 * @return 主线版本ID
	 */
	String getMainIdByBgmkId(String bgmkId);
}
