package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Gjcjcqk;
import com.xpaas.zpbg.vo.GjcjcqkVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-关键词检测情况包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class GjcjcqkWrapper extends BaseEntityWrapper<Gjcjcqk, GjcjcqkVO>  {


	@Override
	public GjcjcqkVO entityVO(Gjcjcqk gjcjcqk) {
		GjcjcqkVO gjcjcqkVO = Objects.requireNonNull(BeanUtil.copy(gjcjcqk, GjcjcqkVO.class));
		//User cjr = UserCache.getUser(gjcjcqk.getCjr());
		//if (cjr != null){
		//	gjcjcqkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcjcqk.getGxr());
		//if (gxr != null){
		//	gjcjcqkVO.setGxrName(gxr.getName());
		//}
		return gjcjcqkVO;
	}

    @Override
    public GjcjcqkVO wrapperVO(GjcjcqkVO gjcjcqkVO) {
		//User cjr = UserCache.getUser(gjcjcqkVO.getCjr());
		//if (cjr != null){
		//	gjcjcqkVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcjcqkVO.getGxr());
		//if (gxr != null){
		//	gjcjcqkVO.setGxrName(gxr.getName());
		//}
        return gjcjcqkVO;
    }

}
