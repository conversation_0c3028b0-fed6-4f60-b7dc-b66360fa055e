package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.vo.BgglVO;
import com.xpaas.zpbg.vo.BgjdVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-报告管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Repository
public interface BgglMapper extends BaseMapper<Bggl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bggl
	 * @return
	 */
	List<BgglVO> selectBgglPage(IPage page, BgglVO bggl);

	/**
	 * 查询报告进度信息
	 *
	 * @param bggl
	 * @return
	 */
	List<BgjdVO> selectBgjd(@Param(value="bggl")Bggl bggl);

	/**
	 * 查询指定报告信息
	 *
	 * @param bgid
	 * @return
	 */
	BgglVO selectBgData(@Param(value="bgid") String bgid);

	/**
	 * 报告查询
	 *
	 * @param bgglVO
	 * @return
	 */
	List<BgglVO> searchBggl(BgglVO bgglVO);

}
