package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zccl;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZcclglVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Repository
public interface ZcclMapper extends BaseMapper<Zccl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param zccl
     * @return
     */
    List<ZcclVO> selectZcclPage(IPage page, ZcclVO zccl, Long userId,List<String> ssmkidslist );

    /**
     * 获取数据验证用
     *
     * @param zccl
     * @return
     */
    List<ZcclVO> checkZccl(ZcclVO zccl);

    /**
     * 删除指定材料
     *
     * @param zcclId
     * @return
     */
    int deleteByZcclId(Long zcclId);
    /**
     * 获取材料数据
     *
     * @param zcclVO
     * @param page
     * @return
     */
    List<ZcclVO> getZzclList(ZcclglVO zcclVO,IPage page);
    /**
     * 获取材料ID
     *
     * @param zccl
     * @return
     */
    List<ZcclVO> getZcclId(ZcclVO zccl);
    /**
     * 获取主要关注点
     *
     * @param list
     * @return
     */
    List<String> getZygzdName( List<String> list);

    /**
     * 根据老的批次名称和新的批次名称更改备查材料的批次信息
     * @param oldPcid
     * @param newPcid
     */
    boolean updateZcclByPcid(@Param("oldPcid") Long oldPcid, @Param("newPcid") Long newPcid);

    /**
     * 根据材料名称查询所有的相同名称的材料
     */
    List<ZcclVO> listByClmc(@Param("cl") Zccl zccl);

}
