package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.vo.ZzclglVO;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料关联 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface IZzclglService extends BaseService<Zzclgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zzclgl
	 * @return
	 */
	IPage<ZzclglVO> selectZzclglPage(IPage<ZzclglVO> page, ZzclglVO zzclgl);

	/**
	 * 不同的内容引用了相同的佐证材料
	 *
	 * @param zzclglVO
	 * @return
	 */
	String sameGl(ZzclglVO zzclglVO);
	/**
	 * 获取ID对应数据
	 *
	 * @param bgmkid
	 * @return
	 */
	ZzclglVO getMKId (Long bgmkid);
	/**
	 * 验证关联key
	 *
	 * @param zzclglVO
	 * @return
	 */
	int checkGlkey(ZzclglVO zzclglVO);
	/**
	 *获取佐证材料关联key有值的数据
	 * @param glkeyList
	 * @return
	 */
	List<ZzclglVO> getzzclList(String bbid, List<String> glkeyList);
	/**
	 *获取佐证材料关联key有值的数据
	 * @param zzclgl
	 * @return
	 */
	List<ZzclglVO> getZzlglList(Zzclgl zzclgl);
	/**
	 *获取最大排序
	 * @return
	 */
	int getMaxPx();


	boolean updataPx(String id,String newpx);

}
