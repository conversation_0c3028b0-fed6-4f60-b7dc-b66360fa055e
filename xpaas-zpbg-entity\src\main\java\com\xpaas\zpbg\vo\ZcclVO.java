package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Zccl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-备查材料视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZcclVO对象", description = "教学评价-自评报告-备查材料")
public class ZcclVO extends Zccl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "被覆盖的数据id")
    private String oldId;

    @ApiModelProperty(value = "报告id")
    private Long bgId;

    @ApiModelProperty(value = "报告名称")
    private String bgmc;

}
