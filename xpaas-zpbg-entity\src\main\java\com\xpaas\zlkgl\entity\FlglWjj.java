package com.xpaas.zlkgl.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-资料库平台-分类管理文件夹树表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("T_TD_JXPJ_ZLK_FLGL_WJJ")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FlglWjj对象", description = "教学评价-资料库平台-分类管理文件夹树表")
public class FlglWjj extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父级文件夹id
     */
    @ExcelProperty("父级文件夹id")
    @ApiModelProperty(value = "父级文件夹id")
    private String fjWjjId;

    /**
     * 节点id
     */
    @ExcelProperty("节点id")
    @ApiModelProperty(value = "节点id")
    private String jdId;

    /**
     * 所属分类管理类别
     */
    @ExcelProperty("所属分类管理类别")
    @ApiModelProperty(value = "所属分类管理类别")
    private String flglLb;

    /**
     * 评价类型
     */
    @ExcelProperty("评价类型")
    @ApiModelProperty(value = "评价类型")
    private String pjLx;

    /**
     * 文件夹名称
     */
    @ExcelProperty("文件夹名称")
    @ApiModelProperty(value = "文件夹名称")
    private String wjjMc;

    /**
     * 文件夹类型
     */
    @ExcelProperty("文件夹类型")
    @ApiModelProperty(value = "文件夹类型")
    private String wjjLx;

    /**
     * 外部链接地址
     */
    @ExcelProperty("外部链接地址")
    @ApiModelProperty(value = "外部链接地址")
    private String wbljDz;

    /**
     * 文件夹排序
     */
    @ExcelProperty("文件夹排序")
    @ApiModelProperty(value = "文件夹排序")
    private Integer wjjPx;


}
