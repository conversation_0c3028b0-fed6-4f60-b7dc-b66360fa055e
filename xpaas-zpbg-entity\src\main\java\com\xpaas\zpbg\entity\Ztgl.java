package com.xpaas.zpbg.entity;

import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-进度管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
//@TableName("T_DT_JXPJ_ZPBG_JDGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Ztgl对象", description = "教学评价-自评报告-状态管理")
public class Ztgl extends TenantEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态名称")
    private String ztmc;

}
