package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bbgl;
import com.xpaas.zpbg.vo.BbglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-版本管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Component
public class BbglWrapper extends BaseEntityWrapper<Bbgl, BbglVO>  {


	@Override
	public BbglVO entityVO(Bbgl bbgl) {
		BbglVO bbglVO = Objects.requireNonNull(BeanUtil.copy(bbgl, BbglVO.class));
		//User cjr = UserCache.getUser(bbgl.getCjr());
		//if (cjr != null){
		//	bbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbgl.getGxr());
		//if (gxr != null){
		//	bbglVO.setGxrName(gxr.getName());
		//}
		return bbglVO;
	}

    @Override
    public BbglVO wrapperVO(BbglVO bbglVO) {
		//User cjr = UserCache.getUser(bbglVO.getCjr());
		//if (cjr != null){
		//	bbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bbglVO.getGxr());
		//if (gxr != null){
		//	bbglVO.setGxrName(gxr.getName());
		//}
        return bbglVO;
    }

}
