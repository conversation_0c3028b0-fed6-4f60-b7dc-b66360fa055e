<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BqglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bqglResultMap" type="com.xpaas.zpbg.entity.Bqgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="KJBQ" property="kjbq"/>
        <result column="BQLX" property="bqlx"/>
        <result column="PX" property="px"/>
        <result column="TJR" property="tjr"/>
        <result column="TJRXM" property="tjrxm"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bqglResultMapVO" type="com.xpaas.zpbg.vo.BqglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="KJBQ" property="kjbq"/>
        <result column="BQLX" property="bqlx"/>
        <result column="PX" property="px"/>
        <result column="TJR" property="tjr"/>
        <result column="TJRXM" property="tjrxm"/>
    </resultMap>

    <!-- 标签管理端查询 -->
    <select id="selectBqglPage" resultMap="bqglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BQGL where scbj = 0 and tjr is null
        <if test="bqgl.bqlx != null and bqgl.bqlx!=''">
            AND bqlx = #{bqgl.bqlx}
        </if>
        order by px
    </select>

    <!-- 标签撰写端查询 -->
    <select id="selectZjBqgl" resultMap="bqglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BQGL where scbj = 0
        <if test="bqgl.tjr != null and bqgl.tjr!=''">
            AND tjr = #{bqgl.tjr}
        </if>
        order by tjr,cjrq
    </select>
</mapper>


