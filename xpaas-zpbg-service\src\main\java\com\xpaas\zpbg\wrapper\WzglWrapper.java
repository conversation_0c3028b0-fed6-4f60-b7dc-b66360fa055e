package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Wzgl;
import com.xpaas.zpbg.vo.WzglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 自评报告-文章管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Component
public class WzglWrapper extends BaseEntityWrapper<Wzgl, WzglVO> {

	@Override
	public WzglVO entityVO(Wzgl wzgl) {
		WzglVO wzglVO = Objects.requireNonNull(BeanUtil.copy(wzgl, WzglVO.class));
		//User cjr = UserCache.getUser(ztwz.getCjr());
		//if (cjr != null){
		//	ztwzVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(ztwz.getGxr());
		//if (gxr != null){
		//	ztwzVO.setGxrName(gxr.getName());
		//}
		return wzglVO;
	}

    @Override
    public WzglVO wrapperVO(WzglVO wzglVO) {
		//User cjr = UserCache.getUser(ztwzVO.getCjr());
		//if (cjr != null){
		//	ztwzVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(ztwzVO.getGxr());
		//if (gxr != null){
		//	ztwzVO.setGxrName(gxr.getName());
		//}
        return wzglVO;
    }

}
