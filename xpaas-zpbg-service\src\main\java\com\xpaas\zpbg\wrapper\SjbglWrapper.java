package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Sjbgl;
import com.xpaas.zpbg.vo.SjbglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-数据表关联包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Component
public class SjbglWrapper extends BaseEntityWrapper<Sjbgl, SjbglVO>  {


	@Override
	public SjbglVO entityVO(Sjbgl sjbgl) {
		SjbglVO sjbglVO = Objects.requireNonNull(BeanUtil.copy(sjbgl, SjbglVO.class));
		//User cjr = UserCache.getUser(sjbgl.getCjr());
		//if (cjr != null){
		//	sjbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjbgl.getGxr());
		//if (gxr != null){
		//	sjbglVO.setGxrName(gxr.getName());
		//}
		return sjbglVO;
	}

    @Override
    public SjbglVO wrapperVO(SjbglVO sjbglVO) {
		//User cjr = UserCache.getUser(sjbglVO.getCjr());
		//if (cjr != null){
		//	sjbglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjbglVO.getGxr());
		//if (gxr != null){
		//	sjbglVO.setGxrName(gxr.getName());
		//}
        return sjbglVO;
    }

}
