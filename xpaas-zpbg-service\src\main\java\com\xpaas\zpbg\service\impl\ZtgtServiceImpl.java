package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Joiner;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.mapper.*;
import com.xpaas.zpbg.service.IZtgtService;
import com.xpaas.zpbg.vo.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 教学评价-自评报告-状态甘特 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class ZtgtServiceImpl implements IZtgtService {

    @Autowired
    BgglMapper bgglMapper;
    @Autowired
    BbglMapper bbglMapper;
    @Autowired
    BgmkMapper bgmkMapper;
    @Autowired
    JdglMapper jdglMapper;
    @Autowired
    JdmkglMapper jdmkglMapper;
    @Autowired
    RwfgMapper rwfgMapper;
    @Autowired
    SyzjMapper syzjMapper;
    @Autowired
    BgmkztjlMapper bgmkztjlMapper;

    /**
     * 获取状态甘特
     */
    @Override
    public ZtgtResultVO ztgt(ZtgtParamVO param) {

        // 返回结果定义
        ZtgtResultVO ret = new ZtgtResultVO();

        // 参数获取
        Long bgid = param.getBgid();
        Long jdglid = param.getJdid();
        Date startDate = timeParseYmd(param.getStartDate());
        Date endDate = timeParseYmd(param.getEndDate());

        // 参数校验
        if (bgid == null || bgid < 0 ||
                jdglid == null || jdglid < 0 ||
                startDate == null || endDate == null) {
            throw new ServiceException("参数不正确");
        }

        // 获取数据
        ZtgtContext ctx = new ZtgtContext();
        initContext(ctx, startDate, endDate, bgid, jdglid);

        // 处理时间轴
        procTime(ctx, ret.getTimescale().getSummarySections());

        // 处理数据
        procData(ctx, ret.getNodes());

        // 返回结果
        return ret;
    }

    /**
     * 获取数据
     */
    private void initContext(ZtgtContext ctx,
                             Date startDate, Date endDate,
                             Long bgid, Long jdglid) {

        // 参数存储
        ctx.bgid = bgid;
        ctx.jdglid = jdglid;
        ctx.startDate = startDate;
        ctx.endDate = endDate;

        // 操作者信息
        LoginUser user = AuthUtil.getUser();
        if (user != null) {
            ctx.userId = user.getUserId();
        }

        // 进度信息
        ctx.jdgl = jdglMapper.selectById(ctx.jdglid);
        if (ctx.jdgl == null) {
            return;
        }

        // 进度模块关联列表
        LambdaQueryWrapper<Jdmkgl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Jdmkgl::getJdglid, ctx.jdglid).eq(Jdmkgl::getScbj, 0);
        ctx.jdmkglList = jdmkglMapper.selectList(wrapper);
        if (ctx.jdmkglList == null || ctx.jdmkglList.isEmpty()) {
            return;
        }

        // 报告模块ID列表
        ctx.bgmkidList = new ArrayList<>();
        for (Jdmkgl item : ctx.jdmkglList) {
            ctx.bgmkidList.add(item.getBgmkid());
        }

        // 报告模块列表
        LambdaQueryWrapper<Bgmk> bgmkWrapper = new LambdaQueryWrapper<>();
        bgmkWrapper
                .in(Bgmk::getId, ctx.bgmkidList)
                .eq(Bgmk::getScbj, 0)
                .orderByAsc(Bgmk::getPx)
                .orderByAsc(Bgmk::getId);
        ctx.bgmkList = bgmkMapper.selectList(bgmkWrapper);

        // 任务分工列表
        LambdaQueryWrapper<Rwfg> rwfgWrapper = new LambdaQueryWrapper<>();
        rwfgWrapper
                .in(Rwfg::getBgmkid, ctx.bgmkidList)
                .eq(Rwfg::getScbj, 0)
                .orderByAsc(Rwfg::getId);

        ctx.rwfgList = rwfgMapper.selectList(rwfgWrapper);

        // 操作记录列表
        LambdaQueryWrapper<Bgmkztjl> ztjlWrapper = new LambdaQueryWrapper<>();
        ztjlWrapper
                .in(Bgmkztjl::getBgmkid, ctx.bgmkidList)
                .eq(Bgmkztjl::getScbj, 0)
                .orderByAsc(Bgmkztjl::getBgmkid)
                .orderByAsc(Bgmkztjl::getCjrq)
                .orderByAsc(Bgmkztjl::getId);
        ctx.ztjlList = bgmkztjlMapper.selectList(ztjlWrapper);
        if (ctx.ztjlList != null) {
            for (Bgmkztjl item : ctx.ztjlList) {
                String czlx = item.getCzlx();
            }
        }
    }

    // 处理时间轴
    private void procTime(ZtgtContext ctx, List<ZtgtSummarySection> summarySections) {
        List<Jdgl> jdglList = new ArrayList<>();
        jdglList.add(ctx.jdgl);
        List<Map<String, Object>> dayList = new ArrayList<>();

        Date dayLoop = ctx.startDate;
        while (dayLoop.compareTo(ctx.endDate) <= 0) {
            Map<String, Object> item = new HashMap<>();
            dayList.add(item);

            for (Jdgl jdgl : jdglList) {
                Date jdStart = jdgl.getKssj();
                Date jdEnd = jdgl.getSyjssj();
                if (dayLoop.compareTo(jdStart) >= 0 && dayLoop.compareTo(jdEnd) <= 0) {
                    item.put("jdmc", jdgl.getJdmc());
                }
            }

            item.put("month", timeFormatY(dayLoop));
            item.put("day", timeFormatYmd(dayLoop));

            dayLoop = nextDay(dayLoop);
        }

        String lastJdmc = "不可能";
        String lastMonth = "不可能";
        String lastDay = "不可能";

        ZtgtSummarySection summarySection = null;
        ZtgtSection section = null;

        for (Map<String, Object> map : dayList) {
            String jdmc = MapUtils.getString(map, "jdmc", "");
            String month = MapUtils.getString(map, "month");
            String day = MapUtils.getString(map, "day");

            if (!jdmc.equals(lastJdmc)) {
                summarySection = new ZtgtSummarySection();
                summarySections.add(summarySection);

                summarySection.setCaption(jdmc);

                // 重启
                lastMonth = "不可能";

                // 记忆
                lastJdmc = jdmc;
            }

            if (!month.equals(lastMonth)) {
                // 前节点处理
                if (section != null) {
                    section.setEndTime(timeParseYmd(lastDay));
                }

                // 本节点处理
                section = new ZtgtSection();
                summarySection.getSections().add(section);

                section.setCaption(month);
                section.setStartTime(timeParseYmd(day));

                // 记忆
                lastMonth = month;
            }

            lastDay = day;
        }

        // 前节点处理
        if (section != null) {
            section.setEndTime(timeParseYmd(lastDay));
        }


    }

    // 处理数据
    private void procData(ZtgtContext ctx, List<ZtgtNode> nodes) {
        // 行数校验
        if (ctx.bgmkList == null || ctx.bgmkList.isEmpty()) {
            return;
        }

        // 模块字典初始化
        Map<Long, ZtgtNode> nodeDic = new HashMap<>();
        for (Bgmk item : ctx.bgmkList) {
            ZtgtNode node = new ZtgtNode();
            nodes.add(node);
            nodeDic.put(item.getId(), node);

            node.setId(item.getId());
            node.setName(item.getMkmc());
            if (StringUtils.isNotBlank(item.getCmm())) {
                node.setName(item.getCmm());
            }
            node.setBgmk(item);
        }

        // 模块负责、我的模块，数据处理
        if (ctx.rwfgList != null) {
            List<String> ryxmList = new ArrayList<String>();
            List<String> dwmcList = new ArrayList<String>();
            Long bgmkidBefore = -31415926L; // 前一条数据的报告模块ID

            for (Rwfg rwfg : ctx.rwfgList) {
                // 获取node
                Long bgmkid = rwfg.getBgmkid();
                ZtgtNode node = nodeDic.get(bgmkid);
                if (node == null) {
                    // 不知为何，居然会有报告模块不存在的情况
                    continue;
                }

                // 负责模块
                Long ryid = rwfg.getRyid();
                Integer fglx = rwfg.getFglx();
                if (fglx != null && fglx == 1 && ryid != null && ctx.userId != null && ryid.equals(ctx.userId)) {
                    node.setFzmk(true);
                }

                // 判断点长
                if (fglx != null && fglx == 1) {
                    // 判断是否新的报告模块
                    if (!bgmkid.equals(bgmkidBefore)) {
                        // 开启新列表
                        ryxmList = new ArrayList<String>();
                        dwmcList = new ArrayList<String>();
                        // 前数据报告模块
                        bgmkidBefore = bgmkid;
                    }

                    ryxmList.add(rwfg.getRyxm());
                    if (!dwmcList.contains(rwfg.getDwmc())) {
                        dwmcList.add(rwfg.getDwmc());
                    }

                    node.setZygzdfzr(Joiner.on(",").join(ryxmList));
                    node.setZrdw(Joiner.on(",").join(dwmcList));
                }
            }
        }

        // 按报告模块拆组
        List<List<Bgmkztjl>> ztjlLists = new ArrayList<>();
        List<Bgmkztjl> curZtjlList = null;
        Long curBgmkid = -999L;
        for (Bgmkztjl item : ctx.ztjlList) {
            Long bgmkid = item.getBgmkid();
            if (!curBgmkid.equals(bgmkid)) {
                curBgmkid = bgmkid;
                curZtjlList = new ArrayList<>();
                ztjlLists.add(curZtjlList);
            }
            curZtjlList.add(item);
        }

        // 按报告模块生成bars
        for (List<Bgmkztjl> ztjlList : ztjlLists) {
            Bgmkztjl ztjl = ztjlList.get(0);
            Long bgmkid = ztjl.getBgmkid();
            List<ZtgtBar> bars = nodeDic.get(bgmkid).getBars();
            makeBars(ztjlList, bars, ctx.jdgl, ctx.startDate, ctx.endDate);
        }

    }

    private void makeBars(List<Bgmkztjl> ztjlList, List<ZtgtBar> bars, Jdgl jdgl, Date startDate, Date endDate) {
        // 进度详情
        Date kssj = jdgl.getKssj();
        Date zxjssj = nextDay(jdgl.getZxjssj());
        Date syjssj = nextDay(jdgl.getSyjssj());

        // 过滤掉进度期间外数据，必要时追加头尾
        List<Bgmkztjl> list = new ArrayList<>();
        Bgmkztjl lastZtjl = null;
        for (int i = 0; i < ztjlList.size(); i++) {
            Bgmkztjl ztjl = ztjlList.get(i);
            Date sj = ztjl.getCjrq();
            ZtProcType czlx = ZtProcType.fromStr(ztjl.getCzlx());

            // 判断节点时间
            if (sj.compareTo(kssj) >= 0 && sj.compareTo(syjssj) < 0) {
                // 节点时间在进度内

                // 必要时取前节点
                if (list.isEmpty() && lastZtjl != null) {
                    if (czlx == ZtProcType.DZTJ20 || czlx == ZtProcType.SYTJ40) {
                        // 前节点加入队列
                        list.add(lastZtjl);
                    }
                }

                // 加入队列
                list.add(ztjl);

            } else if (sj.compareTo(syjssj) >= 0) {
                // 节点时间在进度后

                // 必要时取后节点
                if (czlx == ZtProcType.ZXKS10 && i < ztjlList.size() - 1) {
                    list.add(ztjlList.get(i + 1));
                }

                // 时间超了，退出
                break;
            }

            lastZtjl = ztjl;
        }

        Date now = new Date();

        // 按时间线处理
        List<ZtgtBar> barList = new ArrayList<>();
        int zxbs = 0; // 撰写版数
        int sybs = 0; // 审阅次数
        int len = list.size();
        for (int i = 0; i < len; i++) {
            Bgmkztjl ztjl = list.get(i);
            ZtProcType czlx = ZtProcType.fromStr(ztjl.getCzlx());
            switch (czlx) {
                case ZXKS10:// 10:撰写开始
                    zxbs++;
                    Date ds = ztjl.getCjrq();
                    Date de = nextZtjlSj(list, i);

                    if (zxbs < 2 && (de == null || de.compareTo(zxjssj) > 0)) {

                        if (ds.compareTo(zxjssj) > 0) {
                            // 撰写结束时间需要大于当前时间，才显示延期
//                            if (now.compareTo(zxjssj) > 0) {
                            // 创建延期bar
                            barList.add(createBar(2, ds, de, zxbs, kssj, syjssj));
//                            }

                        } else {
                            // 拆分撰写bar\延期bar
                            barList.add(createBar(0, ds, de, zxbs, kssj, zxjssj));

                            // 撰写结束时间需要大于当前时间，才显示延期
//                            if (now.compareTo(zxjssj) > 0) {
                            barList.add(createBar(2, ds, de, zxbs, zxjssj, syjssj));
//                            }
                        }


                    } else {
                        // 创建撰写bar
                        barList.add(createBar(0, ztjl.getCjrq(), nextZtjlSj(list, i), zxbs, kssj, syjssj));
                    }
                    break;

                case DZTJ20:// 20:点长提交
                    // 忽略
                    if (barList.size() > 0) {
                        ZtgtBar bar = barList.get(barList.size() - 1);
                        if (bar.getZt() == 2) {
                            bar.setCaption("延期提交");
                        }
                    }
                    break;

                case SYKS30:// 30:审阅开始
                    sybs++;
                    // 创建审阅bar
                    barList.add(createBar(1, ztjl.getCjrq(), nextZtjlSj(list, i), sybs, kssj, syjssj));
                    break;

                case SYTJ40:// 40:审阅提交
                    // 忽略
                    break;

                case SYWC50:// 50:审阅完成
                    // 创建完成bar
//                    barList.add(createBar(3, preZtjlSj(list, i), ztjl.getCjrq(), sybs, kssj, syjssj));
                    barList.add(createBar(3, ztjl.getCjrq(), ztjl.getCjrq(), sybs, kssj, syjssj));
                    break;

                case DZDG70: // 70:点长定稿
                    // 创建定稿bar
                    barList.add(createBar(4, ztjl.getCjrq(), ztjl.getCjrq(), sybs, kssj, syjssj));
                    break;

                default:
                    continue;
            }

        }

        SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String nowStartStr = sdf.format(now) + " 00:00:00";
        String nowEndStr = sdf.format(now) + " 23:59:59";

        Date nowStartTime = null;
        Date nowEndTime = null;
        try {
            nowStartTime = sdfTime.parse(nowStartStr);
            nowEndTime = sdfTime.parse(nowEndStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        // bar整理，截掉日期限制外的bar
        Iterator<ZtgtBar> iterator = barList.iterator();
        endDate = nextDay(endDate);
        if (endDate.compareTo(nowEndTime) > 0) {
            endDate = nowEndTime;
        }
        if (startDate.compareTo(nowStartTime) > 0) {
            barList.clear();
        }

        Date endDateS1 = subOneSecond(endDate);
        while (iterator.hasNext()) {
            ZtgtBar bar = iterator.next();
            if (bar.getStartTime().compareTo(endDate) > 0 || bar.getEndTime().compareTo(startDate) < 0) {
                iterator.remove();
            } else {
                if (bar.getStartTime().compareTo(startDate) < 0) {
                    bar.setStartTime(startDate);
                }
                if (bar.getEndTime().compareTo(endDate) >= 0) {
                    bar.setEndTime(endDateS1);
                    if (bar.getEndTime().compareTo(bar.getStartTime()) < 0) {
                        bar.setEndTime(bar.getStartTime());
                    }
                }
            }
        }
        // 加入bars
        bars.addAll(barList);
    }

    private Date nextZtjlSj(List<Bgmkztjl> list, int i) {
        if (i + 1 >= list.size()) {
            return null;
        }
        return list.get(i + 1).getCjrq();
    }

    private Date preZtjlSj(List<Bgmkztjl> list, int i) {
        if (1 < 1) {
            return null;
        }
        return list.get(i - 1).getCjrq();
    }

    // 时间改为次日凌晨0点
    private Date nextDay(Date day) {
        if (day == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(day);

        // 将时间设置为次日凌晨零点
        calendar.add(Calendar.DATE, 1); // 增加一天
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0

        return calendar.getTime();
    }

    public ZtgtBar createBar(int zt, Date startTime, Date endTime, int bjdzsbc,
                             Date minTime, Date maxTime) {
        ZtgtBar bar = new ZtgtBar();
        bar.setId((long) (Math.random() * 10000000000.0));
        bar.setZt(zt);
        bar.setBjdzsbc(bjdzsbc);

        switch (zt) {
            case 0:
                bar.setCaption("第" + bjdzsbc + "版撰写");
                break;

            case 1:
                bar.setCaption("第" + bjdzsbc + "次审阅");
                break;

            case 2:
                bar.setCaption("延期提交");
                break;

            case 3:
                bar.setCaption("已完成");
                break;

            case 4:
                bar.setCaption("定稿");
                break;
            default:
                bar.setCaption("");  // 异常
        }

        // 时间计算
        if (startTime == null) {
            startTime = minTime;
        }
        if (endTime == null) {
            endTime = maxTime;
        }
        if (startTime.compareTo(minTime) < 0) {
            startTime = minTime;
        }
        if (startTime.compareTo(maxTime) > 0) {
            startTime = maxTime;
        }
        if (endTime.compareTo(minTime) < 0) {
            endTime = minTime;
        }
        if (endTime.compareTo(maxTime) > 0) {
            endTime = maxTime;
        }
        // 将秒数减1
        endTime = subOneSecond(endTime);
        if (endTime.compareTo(startTime) < 0) {
            endTime = startTime;
        }

        bar.setStartTime(startTime);
        bar.setEndTime(endTime);

        // 间隔计算
        bar.setSeconds((int) (endTime.getTime() - startTime.getTime()) / 1000);

        return bar;
    }

    private Date subOneSecond(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }


    private String timeFormatYmd(Date date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.format(date);
    }

    private String timeFormatY(Date date) {
        DateFormat df = new SimpleDateFormat("M月");
        return df.format(date);
    }

    @SneakyThrows
    private Date timeParseYmd(String val) {
        if (StringUtils.isBlank(val)) {
            return null;
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.parse(val);
    }

    @SneakyThrows
    private Date timeParseYmdHms(String val) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.parse(val);
    }

    private ZtgtResultVO t0() {
        ZtgtResultVO ret = new ZtgtResultVO();

        // 时间线
        List<ZtgtSummarySection> summarySections = ret.getTimescale().getSummarySections();
        ZtgtSummarySection summarySection;
        List<ZtgtSection> sections;
        ZtgtSection section;

        //
        summarySection = new ZtgtSummarySection();
        summarySections.add(summarySection);
        summarySection.setCaption("第一轮撰写");
        sections = summarySection.getSections();

        section = new ZtgtSection();
        sections.add(section);
        section.setCaption("6月");
        section.setStartTime(timeParseYmd("2024-06-28"));
        section.setEndTime(timeParseYmd("2024-06-30"));

        section = new ZtgtSection();
        sections.add(section);
        section.setCaption("7月");
        section.setStartTime(timeParseYmd("2024-07-01"));
        section.setEndTime(timeParseYmd("2024-07-02"));

        //
        summarySection = new ZtgtSummarySection();
        summarySections.add(summarySection);
        summarySection.setCaption("第二轮撰写");
        sections = summarySection.getSections();

        section = new ZtgtSection();
        sections.add(section);
        section.setCaption("7月");
        section.setStartTime(timeParseYmd("2024-07-03"));
        section.setEndTime(timeParseYmd("2024-07-04"));

        //
        summarySection = new ZtgtSummarySection();
        summarySections.add(summarySection);
        summarySection.setCaption("");
        sections = summarySection.getSections();

        section = new ZtgtSection();
        sections.add(section);
        section.setCaption("7月");
        section.setStartTime(timeParseYmd("2024-07-05"));
        section.setEndTime(timeParseYmd("2024-07-06"));


        // 数据线
        List<ZtgtNode> nodes = ret.getNodes();
        ZtgtNode node;
        List<ZtgtBar> bars;
        ZtgtBar bar;

        //
        node = new ZtgtNode();
        nodes.add(node);
        node.setId(1L);
        node.setName("1.1.1 内涵内容");
        node.setZygzdfzr("王老师");
        node.setZrdw("某单位");
        node.setFzmk(true);
        bars = node.getBars();

        bar = new ZtgtBar();
        bars.add(bar);
        bar.setId(11L);
        bar.setCaption("第1版撰写");
        bar.setStartTime(timeParseYmdHms("2024-06-25 12:34:56"));
        bar.setEndTime(timeParseYmdHms("2024-06-26 18:00:00"));
        bar.setZt(1);

        bar = new ZtgtBar();
        bars.add(bar);
        bar.setId(12L);
        bar.setCaption("第1次审阅");
        bar.setStartTime(timeParseYmdHms("2024-06-25 12:34:56"));
        bar.setEndTime(timeParseYmdHms("2024-06-26 18:00:00"));
        bar.setZt(2);


        //
        node = new ZtgtNode();
        nodes.add(node);
        node.setId(2L);
        node.setName("1.1.2 测试内容666");
        node.setZygzdfzr("赵老师");
        node.setZrdw("某部门");
        node.setFzmk(false);
        bars = node.getBars();

        bar = new ZtgtBar();
        bars.add(bar);
        bar.setId(21L);
        bar.setCaption("第1版撰写");
        bar.setStartTime(timeParseYmdHms("2024-06-22 12:34:56"));
        bar.setEndTime(timeParseYmdHms("2024-06-25 18:00:00"));
        bar.setZt(1);


        return ret;
    }
}
