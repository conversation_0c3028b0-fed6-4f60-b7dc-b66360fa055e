package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bbpz;
import com.xpaas.zpbg.vo.BbpzVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-版本批注 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Repository
public interface BbpzMapper extends BaseMapper<Bbpz> {

	/**
	 * 自定义分页
	 * 分页查询教学评价-自评报告-版本批注表数据
	 * @param page
	 * @param bbpz
	 * <AUTHOR>
	 * @since 2024-09-02
	 * @return
	 */
	List<BbpzVO> selectBbpzPage(IPage page, BbpzVO bbpz);

	/**
	 * 根据版本ID删除批注
	 * @param bbId 版本ID
	 * @return 删除数量
	 */
    int deleteByBbId(String bbId);

	/**
	 * 加锁版本
	 * @param bgId 版本ID
	 */
	void lockBb(@Param("bgId") String bgId);
}
