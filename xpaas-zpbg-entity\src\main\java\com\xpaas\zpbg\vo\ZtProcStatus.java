package com.xpaas.zpbg.vo;

public enum ZtProcStatus {
    WKS0(0), // 0:未开始
    ZXZ10(10), // 10:撰写中
    YYQ20(20), // 20:已延期
    YTJ30(30), // 30:已提交、待审阅
    SYZ40(40), // 40:审阅中
    DXG60(60), // 60:待修改、已审阅
    YWC80(80), // 80:已完成
    YDG90(90); // 90:已定稿

    private final int value;

    ZtProcStatus(int value) {
        this.value = value;
    }

    public static ZtProcStatus fromValue(int value) {
        for (ZtProcStatus procStatus : ZtProcStatus.values()) {
            if (procStatus.getValue() == value) {
                return procStatus;
            }
        }
        throw new IllegalArgumentException("非法枚举值：" + value);
    }

    public int getValue() {
        return this.value;
    }
}
