package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Sjbgl;
import com.xpaas.zpbg.vo.SjbglVO;

/**
 * 教学评价-自评报告-数据表关联 服务类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface ISjbglService extends BaseService<Sjbgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjbgl
	 * @return
	 */
	IPage<SjbglVO> selectSjbglPage(IPage<SjbglVO> page, SjbglVO sjbgl);

	/**
	 * 不同的内容引用了相同的数据表
	 *
	 * @param sjbglVO
	 * @return
	 */
	String sameGl(SjbglVO sjbglVO);

	/**
	 * 新增多条记录
	 *
	 * @param sjbglVO
	 * @return
	 */
	boolean saveBatch(SjbglVO sjbglVO);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	void deleteGl(String ids);
}
