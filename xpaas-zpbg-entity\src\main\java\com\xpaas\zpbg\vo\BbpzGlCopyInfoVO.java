package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Map;


/**
 * 自评报告-版本批注拷贝信息VO
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class BbpzGlCopyInfoVO {
    private static final long serialVersionUID = 1L;

    // 版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbId;

    // 批注KEY
    private String commentId;

    // 复制目的map，仅用于复制源
    private Map<String, BbpzGlCopyInfoVO> tarMap;

}
