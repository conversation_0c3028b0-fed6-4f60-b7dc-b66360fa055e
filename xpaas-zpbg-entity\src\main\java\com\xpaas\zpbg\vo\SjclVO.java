package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Sjcl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-数据材料视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SjclVO对象", description = "教学评价-自评报告-数据材料")
public class SjclVO extends Sjcl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "报告模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgmkid;

    @ApiModelProperty(value = "所属模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ssmkid;

    @ApiModelProperty(value = "模块ID数组")
    private Long[] mkidList;

    @ApiModelProperty(value = "模块名称")
    private String mkmc;

    @ApiModelProperty(value = "引用次数")
    private Integer yycs;

    @ApiModelProperty(value = "模块类型")
    private Integer mklx;

}
