package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-报告模块状态记录实体类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BGMKZTJL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bgmkztjl对象", description = "教学评价-自评报告-报告模块状态记录")
public class Bgmkztjl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGID")
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGMKID")
	private Long bgmkid;

	/**
	* 进度管理ID
	*/
	@ExcelProperty("进度管理ID")
	@ApiModelProperty(value = "进度管理ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("JDGLID")
	private Long jdglid;

	/**
	* 操作类型
	*/
	@ExcelProperty("操作类型")
	@ApiModelProperty(value = "操作类型")
	@TableField("CZLX")
	private String czlx;

	/**
	* 报告模块进度状态
	*/
	@ExcelProperty("报告模块进度状态")
	@ApiModelProperty(value = "报告模块进度状态")
	@TableField("BGMKJDZT")
	private String bgmkjdzt;

	/**
	* 开始日期
	*/
	@ExcelProperty("开始日期")
	@ApiModelProperty(value = "开始日期")
	@TableField("KSRQ")
	private Date ksrq;

	/**
	* 结束日期
	*/
	@ExcelProperty("结束日期")
	@ApiModelProperty(value = "结束日期")
	@TableField("JSRQ")
	private Date jsrq;



}
