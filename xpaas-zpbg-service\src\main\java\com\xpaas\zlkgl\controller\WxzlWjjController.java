package com.xpaas.zlkgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import com.xpaas.zlkgl.wrapper.WxzlWjjWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 控制器
 * controller 入口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxzlWjj")
@Api(value = "教学评价-资料库平台-外校资料文件夹树表", tags = "教学评价-资料库平台-外校资料文件夹树表接口")
public class WxzlWjjController extends BaseController {
    private WxzlWjjWrapper wxzlWjjWrapper;
    private IWxzlWjjService wxzlWjjService;

    /**
     * 获取详情数据
     *
     * @param wxzlWjj wxzlWjj实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入wxzlWjj")
    public R<WxzlWjjVO> detail(WxzlWjj wxzlWjj) {
        WxzlWjj detail = wxzlWjjService.getOne(Condition.getQueryWrapper(wxzlWjj));
        return R.data(wxzlWjjWrapper.entityVO(detail));
    }

    /**
     * 根据主键集合查询 教学评价-资料库平台-外校资料文件夹树表 数据
     *
     * @param ids 主键集合
     * <AUTHOR>  作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/listByIds")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据IDs查询", notes = "传入ids")
    public R<List<WxzlWjjVO>> listByIds(String ids) {
        List<WxzlWjj> listByIds = wxzlWjjService.listByIds(Func.toLongList(ids));
        return R.data(wxzlWjjWrapper.listVO(listByIds));
    }

    /**
     * 根据条件查询 教学评价-资料库平台-外校资料文件夹树表 数据
     *
     * @param wxzlWjj wxzlWjj实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "列表", notes = "传入wxzlWjj")
    public R<List<WxzlWjjVO>> list(WxzlWjj wxzlWjj) {
        List<WxzlWjj> lists = wxzlWjjService.list(Condition.getQueryWrapper(wxzlWjj));
        return R.data(wxzlWjjWrapper.listVO(lists));
    }

    /**
     * 分页查询 教学评价-资料库平台-外校资料文件夹树表数据
     *
     * @param wxzlWjj wxzlWjj实体
     * @param query   查询条件
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入wxzlWjj")
    public R<IPage<WxzlWjjVO>> page(WxzlWjjVO wxzlWjj, Query query) {
        IPage<WxzlWjj> pages = wxzlWjjService.page(Condition.getPage(query), Condition.getQueryWrapper(wxzlWjj));
        return R.data(wxzlWjjWrapper.pageVO(pages));
    }

    /**
     * 高级查询，界面字段的高级搜索
     * (开发过程中根据需求调整精确查询或模糊查询)
     *
     * @param map   高级查询字段,请参考高级查询逻辑
     * @param query 查询条件
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    public R<IPage<WxzlWjjVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<WxzlWjj> queryWrapper = Condition.getQueryWrapper(map, WxzlWjj.class);
        IPage<WxzlWjj> pages = wxzlWjjService.page(Condition.getPage(query), queryWrapper);
        return R.data(wxzlWjjWrapper.pageVO(pages));
    }


    /**
     * 新增 教学评价-资料库平台-外校资料文件夹树表
     *
     * @param wxzlWjjVO wxzlWjjVO实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入wxzlWjj")
    public R save(@Valid @RequestBody WxzlWjjVO wxzlWjjVO) {
        boolean b = wxzlWjjService.insert(wxzlWjjVO);

        return R.status(b);
    }

    /**
     * 修改 教学评价-资料库平台-外校资料文件夹树表
     * 根据主键ID修改数据
     *
     * @param wxzlWjjVO wxzlWjjVO实体
     * <AUTHOR>
     * @since 2025-07-25
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入wxzlWjj")
    public R update(@Valid @RequestBody WxzlWjjVO wxzlWjjVO) {
        boolean b = wxzlWjjService.update(wxzlWjjVO);
        return R.status(b);
    }

    /**
     * 新增或修改 教学评价-资料库平台-外校资料文件夹树表 (优先使用save或update接口)
     * id存在的情况下进行更新操作，id不存在进行插入操作
     *
     * @param wxzlWjj wxzlWjj实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入wxzlWjj")
    public R submit(@Valid @RequestBody WxzlWjj wxzlWjj) {
        return R.status(wxzlWjjService.saveOrUpdate(wxzlWjj));
    }


    /**
     * 删除 教学评价-资料库平台-外校资料文件夹树表
     * 根据主键ID集合逻辑删除数据
     *
     * @param ids 主键集合
     * <AUTHOR> 作者
     * @since 2025-07-25 集合
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        boolean b = wxzlWjjService.deleteByIds(Func.toLongList(ids));
        return R.status(b);
    }


    /**
     * 导出Excel
     *
     * @param response    返回响应
     * @param fileName    文件名
     * @param sheetName   sheet页名称
     * @param columnNames 要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段
     * @param ids         要导出的id,多个id用逗号连接.如果为空,将导出全部数据
     * @param ascs        正排序字段,多个字段用逗号连接
     * @param descs       倒排序字段,多个字段用逗号连接
     * @param map         高级查询字段,请参考高级查询逻辑
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<WxzlWjj> queryWrapper = Condition.getQueryWrapper(map, WxzlWjj.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<WxzlWjj> list = wxzlWjjService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, WxzlWjj.class);
    }


    /**
     * 导入Excel
     *
     * @param file 文件名
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<WxzlWjj> list = ExcelUtil.read(file, WxzlWjj.class);
        wxzlWjjService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     *
     * @param response    返回的响应数据
     * @param columnNames 导入模板的字段
     * <AUTHOR> 作者
     * @since 2025-07-25 创建日期
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    public void template(HttpServletResponse response,
                         @ApiParam(value = "要导出的字段,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames) {
        QueryWrapper<WxzlWjj> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<WxzlWjj> list = wxzlWjjService.list(queryWrapper);

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        ExcelUtil.export(response, "WxzlWjj导入模板", "WxzlWjj导入模板", columnFiledNames, list, WxzlWjj.class);
    }

    /**
     * 根据条件查询 教学评价-资料库平台-外校资料文件夹树表 数据
     *
     * @param bo flglWjj实体
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/listTree")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "树形列表", notes = "传入 FlglWjjVO")
    public R<List<WxzlWjjVO>> listTree(WxzlWjjVO bo) {
        return R.data(wxzlWjjService.listTree(bo));
    }

    /**
     * 复制外校文件夹
     *
     * @param sourceId 源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @param newName 新名称（可选）
     * <AUTHOR> 作者
     * @since 2025-07-30 日期
     */
    @PostMapping("/copy")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "复制外校文件夹", notes = "复制外校文件夹及其内容到指定位置")
    public R copyFolder(@RequestParam String sourceId,
                       @RequestParam String targetFolderId,
                       @RequestParam(required = false) String newName) {
        boolean result = wxzlWjjService.copyFolder(sourceId, targetFolderId, newName);
        return R.status(result);
    }
}
