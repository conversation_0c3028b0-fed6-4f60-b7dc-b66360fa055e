package com.xpaas.zpbg.task;

import com.xpaas.zpbg.service.IXxfbService;
import com.xpaas.zpbg.service.IZtglService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定时任务管理
 *
 * <AUTHOR>
@Component
@EnableAsync // 开启多线程
@Configuration // 主要用于标记配置类，兼备Component的效果。
@EnableScheduling // 开启定时任务
@Slf4j
public class ScheduleTask implements ApplicationRunner {

    @Resource
    private IXxfbService xxfbService;
    @Autowired
    private IZtglService ztglService;

    /**
     * 添加定时任务 开启服务执行一次
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("服务启动执行");

        // 任务期限预警
//		autoBgWarn();
//		autoBgStartWarn();
//		autoBgDelayWarn();

        log.info("服务启动结束");
    }

    /**
     * 每天01点00分执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    //@Scheduled(cron = "0 */2 * * * ?") // 测试，每2分钟
    private void autoBgZtUpdate() {
        log.info("报告状态更新-开始。");

        // 获取活跃报告
        List<Long> bgidList = ztglService.whileDayChangeBgidList();

        // 遍历处理各报告
        if (bgidList != null) {
            for (Long bgid : bgidList) {
                try {
                    // 独立实物更新各报告
                    ztglService.whileDayChange(bgid);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }

        // 消息发送
        ztglService.whileDayChangeMessage();

        log.info("报告状态更新-完成。");
    }

//    /**
//     * 超管创建新进度，时间达到开始时间
//     *
//     * @Async
//     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    private void autoBgStartWarn() {
//        log.info("报告开始预警-开始。");
//
//        try {
//            xxfbService.bgStartWarn();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        log.info("报告开始预警-完成。");
//    }

//    /**
//     * 任务期限预警
//     *
//     * @Async
//     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    private void autoBgWarn() {
//        log.info("报告期限预警-开始。");
//
//        try {
//            xxfbService.bgWarn();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        log.info("报告期限预警-完成。");
//    }

//    /**
//     * 任务期限预警
//     *
//     * @Async
//     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    private void autoBgDelayWarn() {
//        log.info("报告延期预警-开始。");
//
//        try {
//            xxfbService.bgDelayWarn();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        log.info("报告延期预警-完成。");
//    }
}
