package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Sjcl;
import com.xpaas.zpbg.vo.SjclVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-数据材料包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class SjclWrapper extends BaseEntityWrapper<Sjcl, SjclVO>  {


	@Override
	public SjclVO entityVO(Sjcl sjcl) {
		SjclVO sjclVO = Objects.requireNonNull(BeanUtil.copy(sjcl, SjclVO.class));
		//User cjr = UserCache.getUser(sjcl.getCjr());
		//if (cjr != null){
		//	sjclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjcl.getGxr());
		//if (gxr != null){
		//	sjclVO.setGxrName(gxr.getName());
		//}
		return sjclVO;
	}

    @Override
    public SjclVO wrapperVO(SjclVO sjclVO) {
		//User cjr = UserCache.getUser(sjclVO.getCjr());
		//if (cjr != null){
		//	sjclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(sjclVO.getGxr());
		//if (gxr != null){
		//	sjclVO.setGxrName(gxr.getName());
		//}
        return sjclVO;
    }

}
