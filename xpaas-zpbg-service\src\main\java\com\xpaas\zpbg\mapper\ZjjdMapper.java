package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Zjjd;
import com.xpaas.zpbg.vo.ZjjdVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自评报告-文章管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Repository
public interface ZjjdMapper extends BaseMapper<Zjjd> {

	/**
	 * 获取树一级信息
	 * @param zjjdVO
	 * @return
	 */
	List<ZjjdVO> getTreeOne(ZjjdVO zjjdVO);

	/**
	 * 获取树二级信息
	 * @param zjjdVO
	 * @return
	 */
	List<ZjjdVO> getTreeTwo(ZjjdVO zjjdVO);

	/**
	 * 获取树三级信息
	 * @param zjjdVO
	 * @return
	 */
	List<ZjjdVO> getTreeThree(ZjjdVO zjjdVO);

}
