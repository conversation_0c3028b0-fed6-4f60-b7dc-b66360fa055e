package com.xpaas.zlkgl.vo;

import com.xpaas.zlkgl.entity.Zlgl;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 教学评价-资料库平台-资料管理表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ZlglVO对象", description = "教学评价-资料库平台-资料管理表")
public class ZlglVO extends Zlgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
