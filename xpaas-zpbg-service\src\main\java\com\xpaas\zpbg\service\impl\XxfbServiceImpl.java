package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.msg.feign.IMsgCenterClient;
import com.xpaas.user.entity.User;
import com.xpaas.user.feign.IUserClient;
import com.xpaas.zpbg.entity.Xxfb;
import com.xpaas.zpbg.mapper.XxfbMapper;
import com.xpaas.zpbg.service.IXxfbService;
import com.xpaas.zpbg.utils.PjgzMessage;
import com.xpaas.zpbg.vo.XxfbUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 自评报告-消息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@Service
public class XxfbServiceImpl extends BaseServiceImpl<XxfbMapper, Xxfb> implements IXxfbService {

    @Resource
    private IMsgCenterClient msgCenterClient;

    @Autowired
    private IUserClient userClient;

    // # 自评报告撰写平台-教评中心管理员-角色ID
    @Value("${xpaas.roles.zpbgzxptGlyRoleId}")
    private String[] zpbgzxptGlyRoleId;

    /**
     * 发送业务消息
     *
     * @param title    消息内容
     * @param content  角色，zxrOrDz：撰写人或点长、zj：专家、gly：管理员
     * @param userList 用户ID列表
     * @param typeStr  撰写人或点长、专家、管理员
     * @param ywid     版本ID
     */
    @Override
    public void sendMessageYw(String title, String content, List<XxfbUserVO> userList, String typeStr, Long ywid) {

        PjgzMessage.sendMsgYwid(msgCenterClient, title, content, userList, 31, typeStr, ywid);
    }

    @Override
    public void sendMessageJp(String titleJp) {

        sendMessageJpGly(titleJp);
        ;
    }

    private void sendMessageJpGly(String title) {
        String content = "gly";
        int type = 99;
        List<XxfbUserVO> userList = new ArrayList<>();

        if (zpbgzxptGlyRoleId != null && zpbgzxptGlyRoleId.length > 0) {
            for (int i = 0; i < zpbgzxptGlyRoleId.length; i++) {
                List<User> tempList = userClient.listByRoleId(Long.valueOf(zpbgzxptGlyRoleId[i])).getData();

                if (tempList != null && tempList.size() > 0) {
                    for (User user : tempList) {

                        XxfbUserVO item = new XxfbUserVO();
                        item.setUserId(String.valueOf(user.getId()));
                        userList.add(item);
                    }
                }
            }

        }

        PjgzMessage.sendMsgYwid(msgCenterClient, title, content, userList, type, "管理员", null);

    }

}
