package com.xpaas.zlkgl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.vo.FlglWjjVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-资料库平台-分类管理文件夹树表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface FlglWjjMapper extends BaseMapper<FlglWjj> {

    /**
     * 自定义分页
     * 分页查询教学评价-资料库平台-分类管理文件夹树表表数据
     *
     * @param page
     * @param flglWjj
     * @return
     * <AUTHOR>
     * @since 2025-07-24
     */
    List<FlglWjjVO> selectFlglWjjPage(IPage page, FlglWjjVO flglWjj);


    /**
     * 统计指定评价类型下，各分类管理类别的资源总数（文件 + 外链）
     *
     * @param pjLx 评价类型（必传）
     * @param jdId 节点ID（可选）
     * @return List<Map < String, Object>>，包含 flglLb, totalCount, pjLx
     */
    List<Map<String, Object>> countResourcesByPjLx(@Param("pjLx") String pjLx,
                                                   @Param("jdId") String jdId);


    /**
     * 根据ID查询文件夹
     */
    @Select("SELECT * FROM t_td_jxpj_zlk_flgl_wjj WHERE ID = #{id}")
    FlglWjj selectByIdCustom(String id);

    /**
     * 查询指定父文件夹下的所有子文件夹
     */
    @Select("SELECT * FROM t_td_jxpj_zlk_flgl_wjj WHERE fj_wjj_id = #{parentId}")
    List<FlglWjj> selectByParentId(String parentId);
}
