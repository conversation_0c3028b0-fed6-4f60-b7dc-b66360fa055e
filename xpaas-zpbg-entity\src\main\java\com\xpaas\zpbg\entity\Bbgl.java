package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-版本管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BBGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bbgl对象", description = "教学评价-自评报告-版本管理")
public class Bbgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGID")
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("BGMKID")
	private Long bgmkid;

	/**
	* 进度管理ID
	*/
	@ExcelProperty("进度管理ID")
	@ApiModelProperty(value = "进度管理ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("JDGLID")
	private Long jdglid;

	/**
	* 版本类型
	*/
	@ExcelProperty("版本类型")
	@ApiModelProperty(value = "版本类型")
	@TableField("BBLX")
	private Integer bblx;

	/**
	* 版本名称
	*/
	@ExcelProperty("版本名称")
	@ApiModelProperty(value = "版本名称")
	@TableField("BBMC")
	private String bbmc;

	/**
	* 版本说明
	*/
	@ExcelProperty("版本说明")
	@ApiModelProperty(value = "版本说明")
	@TableField("BBSM")
	private String bbsm;

	/**
	* 版本时间
	*/
	@ExcelProperty("版本时间")
	@ApiModelProperty(value = "版本时间")
	@TableField("BBSJ")
	private Date bbsj;

	/**
	* 提交人ID
	*/
	@ExcelProperty("提交人ID")
	@ApiModelProperty(value = "提交人ID")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField("TJRID")
	private Long tjrid;

	/**
	* 提交人
	*/
	@ExcelProperty("提交人")
	@ApiModelProperty(value = "提交人")
	@TableField("TJR")
	private String tjr;

	/**
	* 所属角色
	*/
	@ExcelProperty("所属角色")
	@ApiModelProperty(value = "所属角色")
	@TableField("SSJS")
	private Integer ssjs;

	/**
	* 报告模块状态
	*/
	@ExcelProperty("报告模块状态")
	@ApiModelProperty(value = "报告模块状态")
	@TableField("BGMKZT")
	private Integer bgmkzt;

	/**
	* 本进度撰审版次
	*/
	@ExcelProperty("本进度撰审版次")
	@ApiModelProperty(value = "本进度撰审版次")
	@TableField("BJDZSBC")
	private Integer bjdzsbc;

	/**
	* 文件KEY
	*/
	@ExcelProperty("文件KEY")
	@ApiModelProperty(value = "文件KEY")
	@TableField("WJKEY")
	private String wjkey;

	/**
	* 文件路径
	*/
	@ExcelProperty("文件路径")
	@ApiModelProperty(value = "文件路径")
	@TableField("WJLJ")
	private String wjlj;

	/**
	* 版本信息
	*/
	@ExcelProperty("版本信息")
	@ApiModelProperty(value = "版本信息")
	@TableField("BBXX")
	private String bbxx;

	/**
	* 定稿版本  如果为1则时定稿版本
	*/
	@ExcelProperty("定稿版本  如果为1则时定稿版本")
	@ApiModelProperty(value = "定稿版本  如果为1则时定稿版本")
	@TableField("DGBB")
	private Integer dgbb;



}
