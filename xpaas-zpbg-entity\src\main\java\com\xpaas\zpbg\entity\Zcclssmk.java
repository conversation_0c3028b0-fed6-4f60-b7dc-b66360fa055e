package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-备查材料所属模块实体类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_ZCCLSSMK")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zcclssmk对象", description = "教学评价-自评报告-备查材料所属模块")
public class Zcclssmk extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 备查材料ID
	*/
	@ExcelProperty("备查材料ID")
	@ApiModelProperty(value = "备查材料ID")
	@TableField("ZCCLID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long zcclid;

	/**
	* 模块ID
	*/
	@ExcelProperty("模块ID")
	@ApiModelProperty(value = "模块ID")
	@TableField("MKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long mkid;



}
