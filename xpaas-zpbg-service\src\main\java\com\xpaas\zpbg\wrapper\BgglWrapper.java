package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.vo.BgglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-报告管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Component
public class BgglWrapper extends BaseEntityWrapper<Bggl, BgglVO>  {


	@Override
	public BgglVO entityVO(Bggl bggl) {
		BgglVO bgglVO = Objects.requireNonNull(BeanUtil.copy(bggl, BgglVO.class));
		//User cjr = UserCache.getUser(bggl.getCjr());
		//if (cjr != null){
		//	bgglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bggl.getGxr());
		//if (gxr != null){
		//	bgglVO.setGxrName(gxr.getName());
		//}
		return bgglVO;
	}

    @Override
    public BgglVO wrapperVO(BgglVO bgglVO) {
		//User cjr = UserCache.getUser(bgglVO.getCjr());
		//if (cjr != null){
		//	bgglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(bgglVO.getGxr());
		//if (gxr != null){
		//	bgglVO.setGxrName(gxr.getName());
		//}
        return bgglVO;
    }

}
