package com.xpaas.zlkgl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-资料库平台-资料管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Repository
public interface ZlglMapper extends BaseMapper<Zlgl> {

	/**
	 * 自定义分页
	 * 分页查询教学评价-资料库平台-资料管理表表数据
	 * @param page
	 * @param zlgl
	 * <AUTHOR>
	 * @since 2025-07-25
	 * @return
	 */
	List<ZlglVO> selectZlglPage(IPage page, ZlglVO zlgl);


	/**
	 * 根据ID查询文件
	 */
	@Select("SELECT * FROM t_td_jxpj_zlk_zlgl WHERE ID = #{id}")
	Zlgl selectByIdCustom(String id);

	/**
	 * 查询指定文件夹下的所有文件
	 */
	@Select("SELECT * FROM t_td_jxpj_zlk_zlgl WHERE wjj_id = #{wjjId}")
	List<Zlgl> selectByWjjId(String wjjId);
}
