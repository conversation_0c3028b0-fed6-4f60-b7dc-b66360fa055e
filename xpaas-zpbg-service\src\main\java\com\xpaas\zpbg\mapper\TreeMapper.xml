<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.TreeMapper">

    <!--取得一级模块树-->
    <select id="getMkTreeOne" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            (case when bztx.YJZBID is null then bztx.ID else bztx.YJZBID end) as value,
            (case when bztx.YJZBID is null then IF(bztx.CMM IS NULL OR bztx.CMM = '', bztx.MKMC, bztx.CMM) else bztx.YJZB end) as label
        from
            t_dt_jxpj_zpbg_mkgl bztx
        where
            bztx.SCBJ = 0
            <if test="mklx != null and mklx != ''">
                AND bztx.mklx = #{mklx}
            </if>
        GROUP BY
                value
        order by
            min(bztx.PX), max(bztx.ID)
    </select>

    <!--取得二级模块树-->
    <select id="getMkTreeTwo" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            bztx.EJZBID as value,
            bztx.EJZB as label
        from
            t_dt_jxpj_zpbg_mkgl bztx
        where
            bztx.SCBJ = 0
            AND bztx.YJZBID = #{sjid}
            <if test="mklx != null and mklx != ''">
                AND bztx.mklx = #{mklx}
            </if>
        group by
            value
        order by
            min(bztx.PX), max(bztx.ID)
    </select>

    <!--取得三级模块树-->
    <select id="getMkTreeThree" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            bztx.ID as value,
            IF(bztx.CMM IS NULL OR bztx.CMM = '', bztx.MKMC, bztx.CMM) as label
        from
            t_dt_jxpj_zpbg_mkgl bztx
        where
            bztx.SCBJ = 0
            AND bztx.EJZBID = #{sjid}

            <if test="mklx != null and mklx != ''">
                AND bztx.mklx = #{mklx}
            </if>
        order by
            bztx.PX, bztx.ID desc
    </select>

    <!--取得一级报告模块树-->
    <select id="getBgTreeOne" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            (case when bztx.YJZBID is null then bztx.ID else bztx.YJZBID end) as value,
            (case when bztx.YJZBID is null then IF(bztx.CMM IS NULL OR bztx.CMM = '', bztx.MKMC, bztx.CMM) else bztx.YJZB end) as label
        from
            t_dt_jxpj_zpbg_bgmk bztx
        inner join
            T_DT_JXPJ_ZPBG_JDGL jd
                on jd.BGID = bztx.BGID
                and jd.SCBJ = 0
        inner join
            T_DT_JXPJ_ZPBG_JDMKGL jdgl
                on jdgl.JDGLID = jd.ID
                and jdgl.BGMKID = bztx.ID
                and jdgl.SCBJ = 0
        where
            bztx.SCBJ = 0
            <if test="bgid != null">
                AND bztx.BGID = #{bgid}
            </if>
        GROUP BY
            value
        order by
            min(bztx.PX), max(bztx.ID)
    </select>

    <!--取得二级报告模块树-->
    <select id="getBgTreeTwo" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            bztx.EJZBID as value,
            bztx.EJZB as label
        from
            t_dt_jxpj_zpbg_bgmk bztx
        inner join
            T_DT_JXPJ_ZPBG_JDGL jd
                on jd.BGID = bztx.BGID
                and jd.SCBJ = 0
        inner join
            T_DT_JXPJ_ZPBG_JDMKGL jdgl
                on jdgl.JDGLID = jd.ID
                and jdgl.BGMKID = bztx.ID
                and jdgl.SCBJ = 0
        where
            bztx.SCBJ = 0
            AND bztx.YJZBID = #{sjid}
            <if test="bgid != null">
                AND bztx.bgid = #{bgid}
            </if>
        group by
            value
        order by
            min(bztx.PX), max(bztx.ID)
    </select>

    <!--取得三级报告模块树-->
    <select id="getBgTreeThree" resultType="com.xpaas.zpbg.vo.TreeVO">
        select
            bztx.ID as value,
            IF(bztx.CMM IS NULL OR bztx.CMM = '', bztx.MKMC, bztx.CMM) as label
        from
            t_dt_jxpj_zpbg_bgmk bztx
        inner join
            T_DT_JXPJ_ZPBG_JDGL jd
                on jd.BGID = bztx.BGID
                and jd.SCBJ = 0
        inner join
            T_DT_JXPJ_ZPBG_JDMKGL jdgl
                on jdgl.JDGLID = jd.ID
                and jdgl.BGMKID = bztx.ID
                and jdgl.SCBJ = 0
        where
            bztx.SCBJ = 0
            AND bztx.EJZBID = #{sjid}

            <if test="bgid != null">
                AND bztx.bgid = #{bgid}
            </if>
        order by
            bztx.PX, bztx.ID desc
    </select>

</mapper>
