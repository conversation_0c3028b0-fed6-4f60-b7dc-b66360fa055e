package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Tree;
import com.xpaas.zpbg.mapper.TreeMapper;
import com.xpaas.zpbg.service.ITreeService;
import com.xpaas.zpbg.vo.TreeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 自评报告-文章管理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@Service
public class TreeServiceImpl extends BaseServiceImpl<TreeMapper, Tree> implements ITreeService {

	@Override
	public List<TreeVO> getMkTree(TreeVO treeVO) {
		return getMkChildData(null, "0", treeVO);
	}

	private List<TreeVO> getMkChildData(String sjid, String jb, TreeVO treeVO) {
		List<TreeVO> listTree = new ArrayList<>();

		if(jb.equals("0")) {
			treeVO.setSjid(null);
			listTree = baseMapper.getMkTreeOne(treeVO);
			if(listTree != null) {
				for(int i = 0; i < listTree.size(); i++) {
					TreeVO vo = listTree.get(i);

					List<TreeVO> child = getMkChildData(vo.getValue(), "1", treeVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}
				}
			}
		} else if(jb.equals("1")) {
			treeVO.setSjid(sjid);
			listTree = baseMapper.getMkTreeTwo(treeVO);
			if(listTree != null) {
				for(int i = 0; i < listTree.size(); i++) {
					TreeVO vo = listTree.get(i);

					List<TreeVO> child = getMkChildData(vo.getValue(), "2", treeVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}
				}
			}
		} else if(jb.equals("2")) {
			treeVO.setSjid(sjid);
			listTree = baseMapper.getMkTreeThree(treeVO);
		} else {
			return null;
		}

		return listTree;
	}

	@Override
	public List<TreeVO> getBgTree(TreeVO treeVO) {
		return getBgChildData(null, "0", treeVO);
	}

	private List<TreeVO> getBgChildData(String sjid, String jb, TreeVO treeVO) {
		List<TreeVO> listTree = new ArrayList<>();

		if(jb.equals("0")) {
			treeVO.setSjid(null);
			listTree = baseMapper.getBgTreeOne(treeVO);
			if(listTree != null) {
				for(int i = 0; i < listTree.size(); i++) {
					TreeVO vo = listTree.get(i);

					List<TreeVO> child = getBgChildData(vo.getValue(), "1", treeVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}
				}
			}
		} else if(jb.equals("1")) {
			treeVO.setSjid(sjid);
			listTree = baseMapper.getBgTreeTwo(treeVO);
			if(listTree != null) {
				for(int i = 0; i < listTree.size(); i++) {
					TreeVO vo = listTree.get(i);

					List<TreeVO> child = getBgChildData(vo.getValue(), "2", treeVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}
				}
			}
		} else if(jb.equals("2")) {
			treeVO.setSjid(sjid);
			listTree = baseMapper.getBgTreeThree(treeVO);
		} else {
			return null;
		}

		return listTree;
	}
}
