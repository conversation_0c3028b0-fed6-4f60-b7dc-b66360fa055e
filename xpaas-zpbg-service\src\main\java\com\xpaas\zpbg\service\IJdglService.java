package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.vo.JdglVO;

import java.util.List;

/**
 * 教学评价-自评报告-进度管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface IJdglService extends BaseService<Jdgl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param jdgl
     * @return
     */
    IPage<JdglVO> selectJdglPage(IPage<JdglVO> page, JdglVO jdgl);

    /**
     * 报告信息取得
     *
     * @param page
     * @param jdgl
     * @return
     */
    IPage<JdglVO> getHeaderInfo(IPage<JdglVO> page, JdglVO jdgl);

    /**
     * 报告模块取得
     *
     * @param jdgl
     * @return
     */
    List<JdglVO> getBgmkInfo(JdglVO jdgl);

    /**
     * 进度模块关联取得
     *
     * @param jdgl
     * @return
     */
    List<JdglVO> getJdmkglInfo(JdglVO jdgl);

    /**
     * 进度模块关联取得
     *
     * @param jdgl
     * @return
     */
    int checkExistByInfo(JdglVO jdgl);

//    /**
//     * 查询报告当前进度信息
//     *
//     * @param bgidList
//     * @return
//     */
//    List<Jdgl> selectJdglByBggl(List<Long> bgidList);
}