package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Bgmkztjl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-报告模块状态记录视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BgmkztjlVO对象", description = "教学评价-自评报告-报告模块状态记录")
public class BgmkztjlVO extends Bgmkztjl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
