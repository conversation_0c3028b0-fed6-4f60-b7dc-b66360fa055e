package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.elasticsearch.dto.SearchDTO;
import com.xpaas.elasticsearch.feign.IZjjyClient;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.entity.Zzclssmk;
import com.xpaas.zpbg.mapper.*;
import com.xpaas.zpbg.service.IZzclService;
import com.xpaas.zpbg.service.IZzclssmkService;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;
import com.xpaas.zpbg.vo.ZzclssmkVO;
import com.xpaas.zpbg.wrapper.ZzclWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 教学评价-自评报告-佐证材料 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@Service
public class ZzclServiceImpl extends BaseServiceImpl<ZzclMapper, Zzcl> implements IZzclService {
    @Resource
    public IOssClient ossClient;
    @Autowired
    IZzclssmkService iZzclssmkService;
    @Autowired
    ZzclssmkMapper zzclssmkMapper;
    @Autowired
    MkglMapper mkglMapper;
    @Autowired
    private ZzclWrapper zzclWrapper;
    @Autowired
    private ZzclglMapper zzclglMapper;
    @Autowired
    private ZcclMapper zcclMapper;
    @Autowired
    IZjjyClient zjjyClient;

    @Override
    public IPage<ZzclVO> selectZzclPage(IPage<ZzclVO> page, ZzclVO zzcl)  {
        LoginUser user = AuthUtil.getUser();
        String ssmkids = zzcl.getSsmkids();
        List<String> ssmkidslist = new ArrayList<>();
        if (ssmkids != null) {
           String[] ssmkidsArr = ssmkids.split(",");
            ssmkidslist = Arrays.asList(ssmkidsArr);
            zzcl.setSsmkid(null);
        }
        return page.setRecords(baseMapper.selectZzclPage(page, zzcl, user.getUserId(),ssmkidslist));
    }

    @Override
    public Boolean checkZzcl(ZzclVO zzcl) {
        zzcl.setScbj(0);
        return baseMapper.checkZzcl(zzcl).size() > 0;
    }

    @Override
    public List<ZzclVO> getZzclId(ZzclVO zzcl) {
        zzcl.setScbj(0);
        return baseMapper.getZzclId(zzcl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveZZCLSSMK(ZzclVO zzclVO) {
        baseMapper.deleteByZzclId(zzclVO.getId());
        for (Long mkid : zzclVO.getMkidList()) {
            Zzclssmk zzclssmk = new Zzclssmk();
            zzclssmk.setZzclid(zzclVO.getId());
            zzclssmk.setMkid(mkid);
            iZzclssmkService.save(zzclssmk);
        }
        return true;
    }

    @Override
    public ZzclVO getZzclDetail(Zzcl zzcl) {
        Zzcl detail = getOne(Condition.getQueryWrapper(zzcl));
        ZzclVO zzclVO = zzclWrapper.entityVO(detail);
        List<ZzclssmkVO> zzclssmkVOList = zzclssmkMapper.selectZzclssmkList(zzclVO.getId());
        String ssmkIds = "";
        for (int i = 0; i < zzclssmkVOList.size(); i++) {
            ssmkIds = ssmkIds + zzclssmkVOList.get(i).getMkid() + ",";
        }
        ssmkIds = ssmkIds.substring(0, ssmkIds.length() - 1);
        zzclVO.setSsmk(ssmkIds);
        return zzclVO;
    }

    @Override
    public IPage<ZzclVO> getZzclList(ZzclglVO zzclVO, IPage<ZzclVO> page) {
        return page.setRecords(baseMapper.getZzclList(zzclVO, page));
    }

    @Override
    public void requestZjjyApi(Long clid,String wjlj,String name,String wjmc,int updataOrsave){
        String pdfData = "";
        List<String> yjzbidList = new ArrayList<>();
        List<String> ejzbidList  = new ArrayList<>();
        List<String> zygzjList  = new ArrayList<>();
        List<String> yjzbname = new ArrayList<>();
        List<String> ejzbname  = new ArrayList<>();
        SearchDTO searchDTO = new SearchDTO();
        //材料id
        searchDTO.setId(clid+"");
        // 文件路径
        searchDTO.setUrl(wjlj);
        // 3佐证材料
        searchDTO.setLx(3);
        //标题
        searchDTO.setTitle(name);
        if(!"".equals(wjlj)){
            pdfData = zjjyESApi(wjlj);
            // 内容
            searchDTO.setIntro(pdfData);
        }
        List<ZzclssmkVO> zzclssmkVOList = zzclssmkMapper.selectZzclssmkList(clid);
        if(zzclssmkVOList.size()>0){
            List<String> idlist = new ArrayList();
            for (ZzclssmkVO zzclssmkVO : zzclssmkVOList) {
                idlist.add(zzclssmkVO.getMkid().toString());
            }
            List<Mkgl> mkglList =  mkglMapper.getMkglIdDate(idlist);
            if(mkglList.size()>0){
                for (Mkgl mkgl : mkglList) {
                    yjzbidList.add(mkgl.getYjzbid()==null?"":mkgl.getYjzbid().toString());
                    ejzbidList.add(mkgl.getEjzbid()==null?"":mkgl.getEjzbid().toString());
                    yjzbname.add(mkgl.getYjzb()==null?"":mkgl.getYjzb());
                    ejzbname.add(mkgl.getEjzb()==null?"":mkgl.getEjzb());
                    zygzjList.add(mkgl.getBztxid()==null?"":mkgl.getBztxid().toString());
                }
                List<String> zygzdNamelist = zcclMapper.getZygzdName(zygzjList);
                searchDTO.setYjzbid( String.join(",", yjzbidList));
                searchDTO.setEjzbid( String.join(",", ejzbidList));
                searchDTO.setZygzdid(String.join(",", zygzjList));
                searchDTO.setYjzbmc(String.join(",", yjzbname));
                searchDTO.setEjzbmc(String.join(",", ejzbname));
                searchDTO.setZygzdmc(String.join(",", zygzdNamelist));
                searchDTO.setWjmc(wjmc);
            }
            List<SearchDTO> list = new ArrayList<>();
            list.add(searchDTO);
            if(updataOrsave==1){
                zjjyClient.createDocuments(list);
            }
            else if(updataOrsave==2){
                zjjyClient.createDocument(searchDTO);
            }
        }
    }
    public String zjjyESApi(String wjlj) {
        //读取pdf文件
        try {
            String pdfBody = "";
            R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj, "minio11");
            if (inputR.isSuccess()) {
                log.info("取得文件流成功");
                byte[] bytes = inputR.getData();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                PDDocument document = PDDocument.load(inputStream);
                // 获取文档的目录
                PDDocumentCatalog catalog = document.getDocumentCatalog();
                // 获取文档的所有页面
                PDPageTree pages = catalog.getPages();
                for (int i = 1; i <= pages.getCount(); i++) {
                    PDFTextStripper stripper = new PDFTextStripper();
                    stripper.setStartPage(i);
                    stripper.setEndPage(i);
                    String content = stripper.getText(document);
                    content = content.replaceAll(" ", "").replaceAll(" +", "").replaceAll("[\\t\\n\\r]", "");
                    pdfBody += content;
                }
            }
            return pdfBody;
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }
   @Override
   public List<ZzclglVO> getQuote(Zzclgl zzclgl){
        return zzclglMapper.getQuote(zzclgl);
   }

    /**
     * 根据老的批次名称和新的批次名称更改佐证材料的批次信息
     * @param oldPcid
     * @param newPcid
     */
    @Override
    public void updateZzclByPcid(Long oldPcid, Long newPcid) {
        //查询佐证材料是否有老批次相关的并且和新的批次没有关系的
        List<Zzcl> list = this.list(
                new QueryWrapper<Zzcl>().lambda().eq(Zzcl::getScbj, "0")
                        .apply(" FIND_IN_SET({0},PC) ", oldPcid)
                        .apply(" NOT FIND_IN_SET({0},PC) ", newPcid)
        );
        if(Func.isNotEmpty(list) && list.size()>0){//如果有则更改这种材料的批次信息
            baseMapper.updateZzclByPcid(oldPcid,newPcid);
        }
    }

    /**
     * 根据材料名称查询所有的相同名称的材料
     */
    @Override
    public List<ZzclVO> listByClmc(Zzcl zzcl){
        return baseMapper.listByClmc(zzcl);
    }
}
