<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.RwfgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="rwfgResultMap" type="com.xpaas.zpbg.entity.Rwfg">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="FGLX" property="fglx"/>
        <result column="DWID" property="dwid"/>
        <result column="DWMC" property="dwmc"/>
        <result column="RYID" property="ryid"/>
        <result column="RYXM" property="ryxm"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="rwfgResultMapVO" type="com.xpaas.zpbg.vo.RwfgVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="FGLX" property="fglx"/>
        <result column="DWID" property="dwid"/>
        <result column="DWMC" property="dwmc"/>
        <result column="RYID" property="ryid"/>
        <result column="RYXM" property="ryxm"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="rwryResultMap" type="com.xpaas.zpbg.dto.RwryDTO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMC" property="bgmc"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="ND" property="nd"/>
        <result column="mkid" property="mkid"/>
        <result column="mkmc" property="mkmc"/>
        <result column="fglx" property="fglx"/>
        <result column="dwid" property="dwid"/>
        <result column="dwmc" property="dwmc"/>
        <result column="ryid" property="ryid"/>
        <result column="ryxm" property="ryxm"/>
    </resultMap>

    <!-- 任务人员 -->
    <sql id="rwry">
        select bggl.id as bgid,bggl.bgmc,bggl.nd, rwfg.id, bgmk.id as bgmkid, bgmk.mkid,IF(bgmk.cmm is null or bgmk.cmm = '',bgmk.mkmc,bgmk.cmm) as mkmc,rwfg.fglx,rwfg.dwid,rwfg.dwmc,rwfg.ryid,rwfg.ryxm,bgmk.px
        from T_DT_JXPJ_ZPBG_BGGL bggl
        left join T_DT_JXPJ_ZPBG_BGMK bgmk on bggl.id = bgmk.bgid and bgmk.scbj = 0
        left join t_dt_jxpj_zpbg_rwfg rwfg on bgmk.id = rwfg.bgmkid and rwfg.scbj = 0
        order by bgmk.px,bgmk.mkid DESC
    </sql>

    <!-- 删除任务人员信息 -->
    <delete id="deletePhysical" parameterType="java.lang.Long">
        delete from T_DT_JXPJ_ZPBG_RWFG where bgid = #{bgid}
    </delete>

    <!-- 数据查询 -->
    <select id="selectRwfgPage" resultMap="rwfgResultMapVO">
        select * from T_DT_JXPJ_ZPBG_RWFG where scbj = 0
    </select>

    <!-- 查询任务人员信息 -->
    <select id="selectMkry" resultMap="rwryResultMap">
        select bggl.id as bgid,bggl.bgmc,bggl.nd, jdgl.kssj, rwfg.id, bgmk.id as bgmkid, bgmk.mkid,IF(bgmk.cmm is null or bgmk.cmm = '',bgmk.mkmc,bgmk.cmm) as mkmc, bgmk.px ,rwfg.fglx,rwfg.dwid,rwfg.dwmc,rwfg.ryid,rwfg.ryxm
        from T_DT_JXPJ_ZPBG_BGGL bggl
        left join (select bgid,min(KSSJ) as kssj from T_DT_JXPJ_ZPBG_JDGL where bgid = #{bggl.id}) jdgl on bggl.id = jdgl.bgid
        left join T_DT_JXPJ_ZPBG_BGMK bgmk on bggl.id = bgmk.bgid and bgmk.scbj = 0
        left join t_dt_jxpj_zpbg_rwfg rwfg on bgmk.id = rwfg.bgmkid and rwfg.scbj = 0
        where bggl.id = #{bggl.id}
        order by bgmk.px,bgmk.mkid DESC,rwfg.ryid
    </select>


    <!-- 根据条件查询任务人员信息 -->
    <select id="selectMkryByWrapper" resultType="com.xpaas.zpbg.dto.RwryDTO">
        SELECT * FROM (
        <include refid="rwry"></include>
        )RwryDTO ${ew.customSqlSegment}
    </select>

    <!-- 任务人员信息 -->
    <select id="selectRwfgInfo" resultType="com.xpaas.zpbg.vo.RwfgVO">
        select bgid,bgmkid,fglx,ryid from T_DT_JXPJ_ZPBG_RWFG where scbj = 0 and bgid = #{bgid} and fglx in (1,2)
    </select>

    <!-- 报告模块信息 -->
    <select id="selectBgmkInfo" resultType="com.xpaas.zpbg.vo.RwfgVO">
        select id as bgmkid,bgid from T_DT_JXPJ_ZPBG_BGMK where scbj = 0 and bgid = #{bgid}
    </select>

</mapper>
