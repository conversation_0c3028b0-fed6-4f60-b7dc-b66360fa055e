package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.vo.ZcclglVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Repository
public interface ZcclglMapper extends BaseMapper<Zcclgl> {

    /**
     * 自定义分页
     *
     * @param page
     * @param zcclgl
     * @return
     */
    List<ZcclglVO> selectZcclglPage(IPage page, ZcclglVO zcclgl);

    /**
     * 不同的内容引用了相同的备查材料
     *
     * @param zcclgl
     * @return
     */
    List<ZcclglVO> sameGl(Zcclgl zcclgl);

    int checkGlkey(ZcclglVO zcclgl);

    /**
     * 批注用
     *
     * @param bbid
     * @param glkeyList
     * @return
     */
    List<ZcclglVO> getzcclList(String bbid, List<String> glkeyList);

    /**
     * 获取数据
     *
     * @param zcclgl
     * @return
     */
    @SqlParser(filter=true)
    List<ZcclglVO> getZclglList(Zcclgl zcclgl);

    /**
     * 更新排序顺序
     *
     * @param zcid
     * @param newpx
     * @return
     */
    boolean updataPx(String zcid, String newpx);

    /**
     * 该材料是否引用了
     *
     * @param zcclgl
     * @return
     */
    List<ZcclglVO> getQuote(Zcclgl zcclgl);
    /**
     * 获取最大排序值
     * @return
     */
    int getMaxPx();

}
