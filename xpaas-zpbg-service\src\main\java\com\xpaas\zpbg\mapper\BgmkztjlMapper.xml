<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.BgmkztjlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bgmkztjlResultMap" type="com.xpaas.zpbg.entity.Bgmkztjl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="CZLX" property="czlx"/>
        <result column="BGMKJDZT" property="bgmkjdzt"/>
        <result column="KSRQ" property="ksrq"/>
        <result column="JSRQ" property="jsrq"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="bgmkztjlResultMapVO" type="com.xpaas.zpbg.vo.BgmkztjlVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="JDGLID" property="jdglid"/>
        <result column="CZLX" property="czlx"/>
        <result column="BGMKJDZT" property="bgmkjdzt"/>
        <result column="KSRQ" property="ksrq"/>
        <result column="JSRQ" property="jsrq"/>
    </resultMap>

    <select id="selectBgmkztjlPage" resultMap="bgmkztjlResultMapVO">
        select * from T_DT_JXPJ_ZPBG_BGMKZTJL where scbj = 0
    </select>

</mapper>
