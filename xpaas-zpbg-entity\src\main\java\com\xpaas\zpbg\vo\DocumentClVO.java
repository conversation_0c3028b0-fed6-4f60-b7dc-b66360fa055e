package com.xpaas.zpbg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自评报告-文档合并-材料信息
 */
@Data
@ApiModel(value = "DocumentClVO对象", description = "自评报告-文档合并-材料信息")
public class DocumentClVO {

    @ApiModelProperty(value = "序号")
    private int index;

    @ApiModelProperty(value = "类型")
    private int type;

    @ApiModelProperty(value = "材料名称")
    private String clmc;

    @ApiModelProperty(value = "存档位置")
    private String cfwz;

    @ApiModelProperty(value = "备注")
    private String bz;
}
