package com.xpaas.zpbg.vo;

import com.xpaas.zpbg.entity.Jdgl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-进度管理视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "JdglVO对象", description = "教学评价-自评报告-进度管理")
public class JdglVO extends Jdgl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "报告名称")
    private String bgmc;
    @ApiModelProperty(value = "模块名称")
    private String mkmc;
    @ApiModelProperty(value = "报告模块状态")
    private String bgmkzt;
    @ApiModelProperty(value = "报告模块ID")
    private String bgmkid;
    @ApiModelProperty(value = "提交数")
    private Integer tjcount;
    @ApiModelProperty(value = "审阅数")
    private Integer sycount;
    @ApiModelProperty(value = "模块总数")
    private Integer mkcount;

}
