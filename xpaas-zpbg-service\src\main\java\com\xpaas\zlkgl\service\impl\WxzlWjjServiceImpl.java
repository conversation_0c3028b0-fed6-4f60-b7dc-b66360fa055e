package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.WxzlWjjMapper;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.utils.TreeUtils;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import com.xpaas.zlkgl.wrapper.WxzlWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxzlWjjServiceImpl extends BaseServiceImpl<WxzlWjjMapper, WxzlWjj> implements IWxzlWjjService {

    private final WxzlWjjWrapper wxzlWjjWrapper;
    private final IZlglService zlglService;

    @Override
    public IPage<WxzlWjjVO> selectWxzlWjjPage(IPage<WxzlWjjVO> page, WxzlWjjVO wxzlWjj) {
        return page.setRecords(baseMapper.selectWxzlWjjPage(page, wxzlWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<WxzlWjjVO> listTree(WxzlWjjVO bo) {
        List<WxzlWjjVO> vos = wxzlWjjWrapper.listVO(baseMapper.selectList(Condition.getQueryWrapper(bo)));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                WxzlWjjVO::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(WxzlWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 转换为实体类并保存
        return save(wxzlWjjWrapper.wrapperVO(vo));
    }

    /**
     * 修改
     */
    @Override
    public boolean update(WxzlWjjVO vo) {

        // 1. 获取原数据
        WxzlWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WxzlWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 更新数据
        return baseMapper.updateById(wxzlWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 删除
     */
    @Override
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            WxzlWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<WxzlWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(WxzlWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }

}
