package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.WxzlWjjMapper;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.utils.TreeUtils;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import com.xpaas.zlkgl.wrapper.WxzlWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxzlWjjServiceImpl extends BaseServiceImpl<WxzlWjjMapper, WxzlWjj> implements IWxzlWjjService {

    private final WxzlWjjWrapper wxzlWjjWrapper;
    private final IZlglService zlglService;

    @Override
    public IPage<WxzlWjjVO> selectWxzlWjjPage(IPage<WxzlWjjVO> page, WxzlWjjVO wxzlWjj) {
        return page.setRecords(baseMapper.selectWxzlWjjPage(page, wxzlWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<WxzlWjjVO> listTree(WxzlWjjVO bo) {
        List<WxzlWjjVO> vos = wxzlWjjWrapper.listVO(baseMapper.selectList(Condition.getQueryWrapper(bo)));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                WxzlWjjVO::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(WxzlWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 转换为实体类并保存
        return save(wxzlWjjWrapper.wrapperVO(vo));
    }

    /**
     * 修改
     */
    @Override
    public boolean update(WxzlWjjVO vo) {

        // 1. 获取原数据
        WxzlWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WxzlWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 更新数据
        return baseMapper.updateById(wxzlWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 删除
     */
    @Override
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            WxzlWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<WxzlWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(WxzlWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }

    /**
     * 复制外校文件夹（包括子文件夹和文件）
     *
     * @param sourceId 源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @param newName 新名称（可选，为空时自动生成"副本"名称）
     * @return 复制是否成功
     */
    @Override
    public boolean copyFolder(String sourceId, String targetFolderId, String newName) {
        try {
            // 1. 查询源文件夹
            WxzlWjj sourceFolder = this.getById(sourceId);
            if (sourceFolder == null) {
                log.error("源外校文件夹不存在，ID: {}", sourceId);
                return false;
            }

            // 2. 生成新名称
            String finalName = StringUtil.isBlank(newName) ?
                generateUniqueFolderName(sourceFolder.getWjjMc(), targetFolderId) : newName;

            // 3. 创建新文件夹记录
            WxzlWjj newFolder = new WxzlWjj();
            copyFolderProperties(sourceFolder, newFolder);
            newFolder.setId(null); // 让数据库自动生成新ID
            newFolder.setFjWjjId(targetFolderId);
            newFolder.setWjjMc(finalName);

            // 4. 保存新文件夹
            if (!this.save(newFolder)) {
                log.error("保存新外校文件夹失败");
                return false;
            }

            // 5. 如果是普通文件夹(wjjLx=1)，递归复制子项
            if ("1".equals(sourceFolder.getWjjLx())) {
                return copyFolderContents(sourceId, String.valueOf(newFolder.getId()));
            }

            return true;
        } catch (Exception e) {
            log.error("复制外校文件夹失败，sourceId: {}, targetFolderId: {}, newName: {}", sourceId, targetFolderId, newName, e);
            return false;
        }
    }

    /**
     * 复制文件夹内容（子文件夹和文件）
     *
     * @param sourceFolderId 源文件夹ID
     * @param targetFolderId 目标文件夹ID
     * @return 复制是否成功
     */
    private boolean copyFolderContents(String sourceFolderId, String targetFolderId) {
        try {
            // 1. 复制子文件夹
            List<WxzlWjj> subFolders = this.list(
                new LambdaQueryWrapper<WxzlWjj>().eq(WxzlWjj::getFjWjjId, sourceFolderId)
            );
            for (WxzlWjj subFolder : subFolders) {
                if (!copyFolder(String.valueOf(subFolder.getId()), targetFolderId, null)) {
                    log.error("复制子外校文件夹失败，ID: {}", subFolder.getId());
                    return false;
                }
            }

            // 2. 复制文件
            List<Zlgl> files = zlglService.list(
                new LambdaQueryWrapper<Zlgl>().eq(Zlgl::getWjjId, sourceFolderId)
            );
            for (Zlgl file : files) {
                if (!zlglService.copyFile(String.valueOf(file.getId()), targetFolderId, null)) {
                    log.error("复制文件失败，ID: {}", file.getId());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("复制外校文件夹内容失败", e);
            return false;
        }
    }

    /**
     * 生成唯一文件夹名（处理命名冲突）
     *
     * @param baseName 基础名称
     * @param parentId 父文件夹ID
     * @return 唯一名称
     */
    private String generateUniqueFolderName(String baseName, String parentId) {
        String candidateName = baseName + "副本";
        int counter = 1;

        while (isFolderNameExists(candidateName, parentId)) {
            candidateName = baseName + "副本(" + counter + ")";
            counter++;
        }

        return candidateName;
    }

    /**
     * 检查文件夹名是否已存在
     *
     * @param folderName 文件夹名
     * @param parentId 父文件夹ID
     * @return 是否存在
     */
    private boolean isFolderNameExists(String folderName, String parentId) {
        return this.count(new LambdaQueryWrapper<WxzlWjj>()
            .eq(WxzlWjj::getFjWjjId, parentId)
            .eq(WxzlWjj::getWjjMc, folderName)) > 0;
    }

    /**
     * 复制外校文件夹属性
     *
     * @param source 源文件夹
     * @param target 目标文件夹
     */
    private void copyFolderProperties(WxzlWjj source, WxzlWjj target) {
        target.setPjLx(source.getPjLx());
        target.setWjjMc(source.getWjjMc());
        target.setWjjLx(source.getWjjLx());
        target.setWbljDz(source.getWbljDz());
        target.setWjjPx(source.getWjjPx());
    }

}
