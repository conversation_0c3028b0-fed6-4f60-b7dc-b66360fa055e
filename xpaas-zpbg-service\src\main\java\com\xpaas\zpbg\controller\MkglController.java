package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.dto.TreeData;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.service.IMkglService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.MkglVO;
import com.xpaas.zpbg.vo.TreeVO;
import com.xpaas.zpbg.vo.ZygzdglSync;
import com.xpaas.zpbg.wrapper.MkglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * 教学评价-自评报告-模块管理 控制器
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/mkgl")
@Api(value = "教学评价-自评报告-模块管理", tags = "教学评价-自评报告-模块管理接口")
public class MkglController extends BaseController {

	private MkglWrapper mkglWrapper;
	private IMkglService mkglService;

	/**
	 * 详情 教学评价-自评报告-模块管理
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入mkgl")
	@ApiLog("模块管理-详情")
	public R<MkglVO> detail(Mkgl mkgl) {
		Mkgl detail = mkglService.getOne(Condition.getQueryWrapper(mkgl));
		return R.data(mkglWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-模块管理 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入mkgl")
	@ApiLog("模块管理-分页")
	public R<IPage<MkglVO>> list(Mkgl mkgl, Query query) {
		IPage<Mkgl> pages = mkglService.page(Condition.getPage(query), Condition.getQueryWrapper(mkgl));
		return R.data(mkglWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-模块管理
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "自定义分页", notes = "传入mkgl")
	@ApiLog("模块管理-自定义分页")
    public R<IPage<MkglVO>> page(MkglVO mkgl, Query query, TreeVO tree) {
        IPage<MkglVO> pages = mkglService.selectMkglPage(Condition.getPage(query), mkgl, tree);
        return R.data(mkglWrapper.wrapperPageVO(pages));
    }

	/**
	 * 标准体系字典取得 教学评价-自评报告-模块管理
	 */
	@GetMapping("bztxOptions")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "标准体系字典取得")
	@ApiLog("模块管理-标准体系字典取得")
	public List<TreeData> bztxOptions(String mklxid){
		return mkglService.bztxOptions(mklxid);
	}

	/**
	 * 模块管理与一期标准体系同步增加修改 教学评价-自评报告-模块管理
	 */
	@PostMapping("/getMkglFlg")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "模块管理标准体系同步", notes = "传入mkgl")
	@ApiLog("模块管理-模块管理与一期标准体系同步增加修改")
	public R getMkglFlg(@RequestBody ZygzdglSync zygzdglSync) {
		boolean b = false;
		if ("create".equals(zygzdglSync.getAction())) {
			MkglVO mkglVO = mkglService.getSaveMkgl(zygzdglSync.getDetail().getId());
			if (mkglVO != null) {
				b = mkglService.saveMkgl(mkglVO);
			}
		} else if ("update".equals(zygzdglSync.getAction())) {
			b = mkglService.updateMkgl(zygzdglSync.getDetail());
		}
		return R.status(b);
	}

	/**
	 * 模块管理主动同步 教学评价-自评报告-模块管理
	 */
	@PostMapping("/bztxAsync")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "模块管理主动同步", notes = "传入mkgl")
	@ApiLog("模块管理-模块管理主动同步")
	public boolean bztxAsync() {
		List<Mkgl> list = mkglService.getSaveMkglZd();
		boolean save = mkglService.saveBatch(list);
		boolean update = mkglService.updateMkglZd();
		return save || update;
	}

	/**
	 * 新增 教学评价-自评报告-模块管理
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入mkgl")
	@ApiLog("模块管理-新增")
	public R save(@Valid @RequestBody MkglVO mkglVO) {
		boolean b = mkglService.save(mkglVO);
		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-模块管理
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入mkgl")
	@ApiLog("模块管理-修改")
	public R update(@Valid @RequestBody MkglVO mkglVO) {
		boolean b = mkglService.updateById(mkglVO);
		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-模块管理 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入mkgl")
	@ApiLog("模块管理-新增或修改")
	public R submit(@Valid @RequestBody Mkgl mkgl) {
		return R.status(mkglService.saveOrUpdate(mkgl));
	}

	
	/**
	 * 删除 教学评价-自评报告-模块管理
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("模块管理-删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = mkglService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}


	/**
	 * 删除模版 教学评价-自评报告-模块管理
	 */
	@PostMapping("/removeModel")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("模块管理-删除模版")
	public R removeModel(@Valid @RequestBody MkglVO mkglVO) {
		mkglVO.setYjzbid(null);
		mkglVO.setEjzbid(null);
		mkglVO.setMbwjlj("");
		mkglVO.setMbwjkey("");
		boolean b = mkglService.updateById(mkglVO);
		return R.status(b);
	}

	/**
	 * 报告名称取得 教学评价-自评报告-模块管理
	 */
	@GetMapping("/getBgmc")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "报告名称取得", notes = "传入mkid")
	@ApiLog("模块管理-报告名称取得")
	public String getBgmc(String mkid) {
		return mkglService.getBgmc(mkid);
	}

	
	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("模块管理-高级查询")
	public R<IPage<MkglVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Mkgl> queryWrapper = Condition.getQueryWrapper(map, Mkgl.class);
		IPage<Mkgl> pages = mkglService.page(Condition.getPage(query), queryWrapper);
		return R.data(mkglWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("模块管理-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Mkgl> queryWrapper = Condition.getQueryWrapper(map, Mkgl.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Mkgl> list = mkglService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Mkgl.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("模块管理-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Mkgl> list = ExcelUtil.read(file, Mkgl.class);
		//TODO 此处需要根据具体业务添加代码
		mkglService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("模块管理-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Mkgl> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Mkgl> list = mkglService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Mkgl导入模板", "Mkgl导入模板",columnFiledNames, list, Mkgl.class);
	}
}
