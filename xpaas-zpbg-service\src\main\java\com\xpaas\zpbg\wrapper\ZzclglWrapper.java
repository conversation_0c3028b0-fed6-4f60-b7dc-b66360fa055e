package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zzclgl;
import com.xpaas.zpbg.vo.ZzclglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-佐证材料关联包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class ZzclglWrapper extends BaseEntityWrapper<Zzclgl, ZzclglVO>  {


	@Override
	public ZzclglVO entityVO(Zzclgl zzclgl) {
		ZzclglVO zzclglVO = Objects.requireNonNull(BeanUtil.copy(zzclgl, ZzclglVO.class));
		//User cjr = UserCache.getUser(zzclgl.getCjr());
		//if (cjr != null){
		//	zzclglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzclgl.getGxr());
		//if (gxr != null){
		//	zzclglVO.setGxrName(gxr.getName());
		//}
		return zzclglVO;
	}

    @Override
    public ZzclglVO wrapperVO(ZzclglVO zzclglVO) {
		//User cjr = UserCache.getUser(zzclglVO.getCjr());
		//if (cjr != null){
		//	zzclglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zzclglVO.getGxr());
		//if (gxr != null){
		//	zzclglVO.setGxrName(gxr.getName());
		//}
        return zzclglVO;
    }

}
