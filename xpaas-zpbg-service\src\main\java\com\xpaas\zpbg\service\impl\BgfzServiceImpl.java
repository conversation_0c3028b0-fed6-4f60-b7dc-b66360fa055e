package com.xpaas.zpbg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.tenant.mp.TenantEntity;
import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 教学评价-自评报告-报告复制 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Slf4j
@Service
public class BgfzServiceImpl implements IBgfzService {

    @Autowired
    IBgglService bgglService;
    @Autowired
    IBgmkService bgmkService;
    @Autowired
    IBbglService bbglService;
    @Autowired
    IBbwjnrService bbwjnrService;
    @Autowired
    IBbpzService bbpzService;
    @Autowired
    ISjzbglService sjzbglService;
    @Autowired
    ISjbglService sjbglService;
    @Autowired
    IZzclglService zzclglService;
    @Autowired
    IZcclglService zcclglService;
    @Autowired
    IRwfgService rwfgService;

    /**
     * 报告复制
     *
     * @param bggl
     */
    @Override
    @Transactional
    public void bgfz(Bggl bggl) {
        Long bgidSrc = bggl.getId();
        Long bgidNew;

        Map<Long, Long> bgmkidDic = new HashMap<>(); // 报告模块ID转换
        Map<Long, Long> bbidDic = new HashMap<>();// 版本ID转换
        Set<Long> bbidSet; // 源版本ID集合
        Set<String> glkeySet = new HashSet<>(); // 版本批注KEY集合

        // 【报告】
        Bggl bgglNew = bgglService.getById(bgidSrc);
        //校验报告名称
        if(bgglNew.getBgmc().equals(bggl.getBgmc())){
            throw new ServiceException("相同年度任务类型报告已存在");
        }
        clearPropForNew(bgglNew);

        bgglNew.setBgmc(bggl.getBgmc()); // 报告名称
        bgglNew.setBgzt(1); // 状态：未发布
        bgglNew.setJdglid(null); // 进度管理ID：无

        bgglService.save(bgglNew);
        bgidNew = bgglNew.getId();

        // 【报告模块】
        List<Bgmk> bgmkList = bgmkService.list(
                new LambdaQueryWrapper<Bgmk>()
                        .eq(Bgmk::getBgid, bgidSrc));
        for (Bgmk item : bgmkList) {
            Long idOld = item.getId();
            clearPropForNew(item);

            item.setBgid(bgidNew); // 报告ID
            item.setBgmkzt(0); // 状态：未开始

            bgmkService.save(item);
            bgmkidDic.put(idOld, item.getId());
        }

        // 【版本管理】
        List<Bbgl> bbglList = bbglService.list(
                new LambdaQueryWrapper<Bbgl>()
                        .eq(Bbgl::getBgid, bgidSrc)
                        .eq(Bbgl::getBblx, 1));
        for (Bbgl item : bbglList) {
            Long idOld = item.getId();
            clearPropForNew(item);

            item.setBgid(bgidNew); // 报告ID
            item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
            item.setJdglid(null); // 进度管理ID：无
            item.setBgmkzt(0); // 状态：未开始
            item.setBjdzsbc(1); // 撰审版次
            item.setWjkey(UUID.randomUUID().toString()); // 文件KEY

            bbglService.save(item);
            bbidDic.put(idOld, item.getId());
        }
        bbidSet = bbidDic.keySet();

        // 【版本内容】
        if (!bbidSet.isEmpty()) {
            List<Bbwjnr> bbwjnrList = bbwjnrService.list(
                    new LambdaQueryWrapper<Bbwjnr>()
                            .in(Bbwjnr::getBbid, bbidSet));
            for (Bbwjnr item : bbwjnrList) {
                clearPropForNew(item);

                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                bbwjnrService.save(item);
            }
        }

        // 【版本批注】
        if (!bbidSet.isEmpty()) {
            List<Bbpz> bbpzList = bbpzService.list(
                    new LambdaQueryWrapper<Bbpz>()
                            .in(Bbpz::getBbid, bbidSet));
            for (Bbpz item : bbpzList) {
                clearPropForNew(item);

                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                bbpzService.save(item);
                glkeySet.add(item.getCommentId()); // 记录批注Key
            }
        }

        // 【数据指标关联】
        if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
            List<Sjzbgl> sjzbglList = sjzbglService.list(
                    new LambdaQueryWrapper<Sjzbgl>()
                            .in(Sjzbgl::getBbid, bbidSet)
                            .in(Sjzbgl::getGlkey, glkeySet));
            for (Sjzbgl item : sjzbglList) {
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                sjzbglService.save(item);
            }
        }

        // 【数据表关联】
        if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
            List<Sjbgl> sjbglList = sjbglService.list(
                    new LambdaQueryWrapper<Sjbgl>()
                            .in(Sjbgl::getBbid, bbidSet)
                            .in(Sjbgl::getGlkey, glkeySet));
            for (Sjbgl item : sjbglList) {
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                sjbglService.save(item);
            }
        }

        // 【佐证材料关联】
        if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
            List<Zzclgl> zzclglList = zzclglService.list(
                    new LambdaQueryWrapper<Zzclgl>()
                            .in(Zzclgl::getBbid, bbidSet)
                            .in(Zzclgl::getGlkey, glkeySet));
            for (Zzclgl item : zzclglList) {
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                zzclglService.save(item);
            }
        }

        // 【备查材料关联】
        if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
            List<Zcclgl> zcclglList = zcclglService.list(
                    new LambdaQueryWrapper<Zcclgl>()
                            .in(Zcclgl::getBbid, bbidSet)
                            .in(Zcclgl::getGlkey, glkeySet));
            for (Zcclgl item : zcclglList) {
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                zcclglService.save(item);
            }
        }

        // 【任务分工】
        List<Rwfg> rwfgList = rwfgService.list(
                new LambdaQueryWrapper<Rwfg>()
                        .eq(Rwfg::getBgid, bgidSrc));
        for (Rwfg item : rwfgList) {
            clearPropForNew(item);

            item.setBgid(bgidNew); // 报告ID
            item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
            item.setClzt(1); // 1 撰写中、未审阅

            rwfgService.save(item);
        }
    }

    @Override
    public void bgfzByPcid(Long oldPcid,Long newPcid) {
        QueryWrapper<Bggl> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Bggl::getScbj,"0").eq(Bggl::getPcid,oldPcid);
        List<Bggl> listBggls = bgglService.list(queryWrapper);
        for(Bggl bggl:listBggls){
            Long bgidSrc = bggl.getId();
            Long bgidNew;

            Map<Long, Long> bgmkidDic = new HashMap<>(); // 报告模块ID转换
            Map<Long, Long> bbidDic = new HashMap<>();// 版本ID转换
            Set<Long> bbidSet; // 源版本ID集合
            Set<String> glkeySet = new HashSet<>(); // 版本批注KEY集合

            // 【报告】
            Bggl bgglNew = bgglService.getById(bgidSrc);
            clearPropForNew(bgglNew);

            bgglNew.setPcid(newPcid);//新批次id赋值
            bgglNew.setBgmc(bggl.getBgmc()); // 报告名称
            bgglNew.setBgzt(1); // 状态：未发布
            bgglNew.setJdglid(null); // 进度管理ID：无

            bgglService.save(bgglNew);
            bgidNew = bgglNew.getId();

            // 【报告模块】
            List<Bgmk> bgmkList = bgmkService.list(
                    new LambdaQueryWrapper<Bgmk>()
                            .eq(Bgmk::getBgid, bgidSrc));
            for (Bgmk item : bgmkList) {
                Long idOld = item.getId();
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkzt(0); // 状态：未开始

                bgmkService.save(item);
                bgmkidDic.put(idOld, item.getId());
            }

            // 【版本管理】
            List<Bbgl> bbglList = bbglService.list(
                    new LambdaQueryWrapper<Bbgl>()
                            .eq(Bbgl::getBgid, bgidSrc)
                            .eq(Bbgl::getBblx, 1));
            for (Bbgl item : bbglList) {
                Long idOld = item.getId();
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setJdglid(null); // 进度管理ID：无
                item.setBgmkzt(0); // 状态：未开始
                item.setBjdzsbc(1); // 撰审版次
                item.setWjkey(UUID.randomUUID().toString()); // 文件KEY

                bbglService.save(item);
                bbidDic.put(idOld, item.getId());
            }
            bbidSet = bbidDic.keySet();

            // 【版本内容】
            if (!bbidSet.isEmpty()) {
                List<Bbwjnr> bbwjnrList = bbwjnrService.list(
                        new LambdaQueryWrapper<Bbwjnr>()
                                .in(Bbwjnr::getBbid, bbidSet));
                for (Bbwjnr item : bbwjnrList) {
                    clearPropForNew(item);

                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    bbwjnrService.save(item);
                }
            }

            // 【版本批注】
            if (!bbidSet.isEmpty()) {
                List<Bbpz> bbpzList = bbpzService.list(
                        new LambdaQueryWrapper<Bbpz>()
                                .in(Bbpz::getBbid, bbidSet));
                for (Bbpz item : bbpzList) {
                    clearPropForNew(item);

                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    bbpzService.save(item);
                    glkeySet.add(item.getCommentId()); // 记录批注Key
                }
            }

            // 【数据指标关联】
            if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
                List<Sjzbgl> sjzbglList = sjzbglService.list(
                        new LambdaQueryWrapper<Sjzbgl>()
                                .in(Sjzbgl::getBbid, bbidSet)
                                .in(Sjzbgl::getGlkey, glkeySet));
                for (Sjzbgl item : sjzbglList) {
                    clearPropForNew(item);

                    item.setBgid(bgidNew); // 报告ID
                    item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    sjzbglService.save(item);
                }
            }

            // 【数据表关联】
            if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
                List<Sjbgl> sjbglList = sjbglService.list(
                        new LambdaQueryWrapper<Sjbgl>()
                                .in(Sjbgl::getBbid, bbidSet)
                                .in(Sjbgl::getGlkey, glkeySet));
                for (Sjbgl item : sjbglList) {
                    clearPropForNew(item);

                    item.setBgid(bgidNew); // 报告ID
                    item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    sjbglService.save(item);
                }
            }

            // 【佐证材料关联】
            if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
                List<Zzclgl> zzclglList = zzclglService.list(
                        new LambdaQueryWrapper<Zzclgl>()
                                .in(Zzclgl::getBbid, bbidSet)
                                .in(Zzclgl::getGlkey, glkeySet));
                for (Zzclgl item : zzclglList) {
                    clearPropForNew(item);

                    item.setBgid(bgidNew); // 报告ID
                    item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    zzclglService.save(item);
                }
            }

            // 【备查材料关联】
            if (!bbidSet.isEmpty() && !glkeySet.isEmpty()) {
                List<Zcclgl> zcclglList = zcclglService.list(
                        new LambdaQueryWrapper<Zcclgl>()
                                .in(Zcclgl::getBbid, bbidSet)
                                .in(Zcclgl::getGlkey, glkeySet));
                for (Zcclgl item : zcclglList) {
                    clearPropForNew(item);

                    item.setBgid(bgidNew); // 报告ID
                    item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                    item.setBbid(bbidDic.get(item.getBbid())); // 版本ID

                    zcclglService.save(item);
                }
            }

            // 【任务分工】
            List<Rwfg> rwfgList = rwfgService.list(
                    new LambdaQueryWrapper<Rwfg>()
                            .eq(Rwfg::getBgid, bgidSrc));
            for (Rwfg item : rwfgList) {
                clearPropForNew(item);

                item.setBgid(bgidNew); // 报告ID
                item.setBgmkid(bgmkidDic.get(item.getBgmkid())); // 报告模块ID
                item.setClzt(1); // 1 撰写中、未审阅

                rwfgService.save(item);
            }
        }
    }


    private void clearPropForNew(TenantEntity entity) {
        entity.setId(null);
        entity.setZhid(null);
    }
}
