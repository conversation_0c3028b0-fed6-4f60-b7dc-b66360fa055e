package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Bztxgl;
import com.xpaas.zpbg.entity.Zjjdcl;
import com.xpaas.zpbg.service.IBztxglService;
import com.xpaas.zpbg.service.IZjjdService;
import com.xpaas.zpbg.service.IZjjdclService;
import com.xpaas.zpbg.service.IZjmkSpService;
import com.xpaas.zpbg.vo.BztxglVO;
import com.xpaas.zpbg.vo.ZjjdVO;
import com.xpaas.zpbg.vo.ZjmkSpVO;
import com.xpaas.zpbg.wrapper.BztxglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 教学评价-自评报告-专家解读 控制器
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zjjd")
@Api(value = "自评报告-专家解读", tags = "自评报告--专家解读接口")
public class ZjjdController extends BaseController {

	private IZjjdService zjjdService;

	private BztxglWrapper bztxglWrapper;
	private IBztxglService bztxglService;

	private IZjjdclService zjjdclService;

	private IZjmkSpService zjmkSpService;

	/**
	 * 获取树
	 */
	@GetMapping("/getTree")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取树", notes = "传入zjjdVO")
	@ApiLog("自评报告-专家解读-获取树")
	public R<List<ZjjdVO>> getTree(ZjjdVO zjjdVO) {
		// 需要参数，模块类型
		List<ZjjdVO> list = zjjdService.getTree(zjjdVO);
		return R.data(list);
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiLog("自评报告-标准体系-获取详情")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入pjtxgl")
	public R<BztxglVO> detail(Bztxgl bztxgl) {
		List<Bztxgl> list = bztxglService.list(Condition.getQueryWrapper(bztxgl));
		if(list != null && list.size() > 0){
			return R.data(bztxglWrapper.entityVO(list.get(0)));
		}else{
			return R.data(bztxglWrapper.entityVO(null));
		}

	}

	/**
	 * 分页 教学评价-自评报告-专家解读材料 (优先使用search接口)
	 */
	@PostMapping("/getMaterialsData")
	@ApiLog("自评报告-专家解读材料-获取分页数据")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入zjjdcl")
	public R<List<Zjjdcl>> getMaterialsData(Zjjdcl zjjdcl, Query query) {
		List<Zjjdcl> list = zjjdclService.list1(zjjdcl);
		return R.data(list);
	}

	/**
	 * 自评报告-标准体系-获取专家慕课视频
	 */
	@GetMapping("/getZjmkSp")
	@ApiLog("自评报告-标准体系-获取专家慕课视频")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "详情", notes = "传入zjmkSp")
	public R<ZjmkSpVO> getZjmkSp(ZjmkSpVO zjmkSp) {

		ZjmkSpVO zjmkSpVO = zjmkSpService.getZjmkSp(zjmkSp);
		return R.data(zjmkSpVO);

	}
}
