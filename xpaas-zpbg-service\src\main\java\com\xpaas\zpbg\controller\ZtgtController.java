package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.zpbg.service.IZtgtService;
import com.xpaas.zpbg.vo.ZtgtBar;
import com.xpaas.zpbg.vo.ZtgtNode;
import com.xpaas.zpbg.vo.ZtgtParamVO;
import com.xpaas.zpbg.vo.ZtgtResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 教学评价-自评报告-状态甘特 控制器
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/ztgt")
@Api(value = "教学评价-自评报告-状态甘特", tags = "教学评价-自评报告-状态甘特接口")
public class ZtgtController extends BaseController {

    private IZtgtService ztgtService;

    /**
     * 获取状态甘特信息
     */
    @GetMapping("/ztgt")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取状态甘特信息", notes = "传入甘特图参数")
    @ApiLog("状态信息-详情")
    public R<ZtgtResultVO> ztgt(ZtgtParamVO param) {
        return R.data(ztgtService.ztgt(param));
    }

    /**
     * 状态数量
     */
    @GetMapping("/ztsl")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "状态数量", notes = "状态数量")
    @ApiLog("状态数量")
    public R ztsl(ZtgtParamVO param){
        Integer[] sl={0,0,0,0};
        ZtgtResultVO ztgt = ztgtService.ztgt(param);
        if(ztgt!=null&& Func.isNotEmpty(ztgt.getNodes())){
            for(ZtgtNode node:ztgt.getNodes()){
                if(Func.isNotEmpty(node.getBars())){
                    for(ZtgtBar bar:node.getBars()){
                        for(int i=0;i<sl.length;i++){
                            if(bar.getZt().equals(i)){
                                sl[i]++;
                            }
                        }
                    }
                }
            }
        }
        return R.data(sl);
    }
}
