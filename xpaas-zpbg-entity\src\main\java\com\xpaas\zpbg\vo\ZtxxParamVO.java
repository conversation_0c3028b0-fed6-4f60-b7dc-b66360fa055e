package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;


/**
 * 自评报告-状态信息参数类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class ZtxxParamVO {
    private static final long serialVersionUID = 1L;

    // 版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbid;

    // 操作角色
    private ZtxxRoleType role;

}
