package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.vo.JdglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-进度管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Component
public class JdglWrapper extends BaseEntityWrapper<Jdgl, JdglVO>  {


	@Override
	public JdglVO entityVO(Jdgl jdgl) {
		JdglVO jdglVO = Objects.requireNonNull(BeanUtil.copy(jdgl, JdglVO.class));
		//User cjr = UserCache.getUser(jdgl.getCjr());
		//if (cjr != null){
		//	jdglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdgl.getGxr());
		//if (gxr != null){
		//	jdglVO.setGxrName(gxr.getName());
		//}
		return jdglVO;
	}

    @Override
    public JdglVO wrapperVO(JdglVO jdglVO) {
		//User cjr = UserCache.getUser(jdglVO.getCjr());
		//if (cjr != null){
		//	jdglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(jdglVO.getGxr());
		//if (gxr != null){
		//	jdglVO.setGxrName(gxr.getName());
		//}
        return jdglVO;
    }

}
