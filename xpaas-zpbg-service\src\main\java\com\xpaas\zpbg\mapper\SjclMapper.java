package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Sjcl;
import com.xpaas.zpbg.vo.SjclVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-数据材料 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface SjclMapper extends BaseMapper<Sjcl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sjcl
	 * @return
	 */
	List<SjclVO> selectSjclPage(IPage page, SjclVO sjcl);

}
