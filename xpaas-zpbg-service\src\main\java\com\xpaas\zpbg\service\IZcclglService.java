package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.vo.ZcclglVO;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料关联 服务类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface IZcclglService extends BaseService<Zcclgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zcclgl
	 * @return
	 */
	IPage<ZcclglVO> selectZcclglPage(IPage<ZcclglVO> page, ZcclglVO zcclgl);

	/**
	 * 不同的内容引用了相同的备查材料
	 *
	 * @param zcclglVO
	 * @return
	 */
	String sameGl(ZcclglVO zcclglVO);

	int checkGlkey(ZcclglVO zzclglVO);
	List<ZcclglVO> getzcclList(String bbid, List<String> glkeyList);
	List<ZcclglVO> getZclglList(Zcclgl zzclgl);
	boolean updataPx(String id,String newpx);
	int getMaxPx();

}
