package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-进度模块关联表实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_JDMKGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Jdmkgl对象", description = "教学评价-自评报告-进度模块关联表")
public class Jdmkgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 进度管理ID
	*/
	@ExcelProperty("进度管理ID")
	@ApiModelProperty(value = "进度管理ID")
	@TableField("JDGLID")

	@JsonSerialize(using = ToStringSerializer.class)
	private Long jdglid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")

	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;



}
