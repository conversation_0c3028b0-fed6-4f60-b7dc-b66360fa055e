package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Gjcjcjl;
import com.xpaas.zpbg.vo.GjcjcjlVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-关键词检测记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class GjcjcjlWrapper extends BaseEntityWrapper<Gjcjcjl, GjcjcjlVO>  {


	@Override
	public GjcjcjlVO entityVO(Gjcjcjl gjcjcjl) {
		GjcjcjlVO gjcjcjlVO = Objects.requireNonNull(BeanUtil.copy(gjcjcjl, GjcjcjlVO.class));
		//User cjr = UserCache.getUser(gjcjcjl.getCjr());
		//if (cjr != null){
		//	gjcjcjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcjcjl.getGxr());
		//if (gxr != null){
		//	gjcjcjlVO.setGxrName(gxr.getName());
		//}
		return gjcjcjlVO;
	}

    @Override
    public GjcjcjlVO wrapperVO(GjcjcjlVO gjcjcjlVO) {
		//User cjr = UserCache.getUser(gjcjcjlVO.getCjr());
		//if (cjr != null){
		//	gjcjcjlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcjcjlVO.getGxr());
		//if (gxr != null){
		//	gjcjcjlVO.setGxrName(gxr.getName());
		//}
        return gjcjcjlVO;
    }

}
