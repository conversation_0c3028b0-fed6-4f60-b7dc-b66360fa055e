package com.xpaas.zpbg.service.impl;

import com.xpaas.zpbg.mapper.DownloadFilesMapper;
import com.xpaas.zpbg.service.IDownloadFilesService;
import com.xpaas.zpbg.vo.DownloadFilesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/7/2 15:01
 */
@Slf4j
@Service
public class DownloadFilesServiceImpl implements IDownloadFilesService {

    @Autowired
    DownloadFilesMapper downloadFilesMapper;

    /**
     * 获取下载文件URL列表
     *
     * @param downloadFilesVO
     * @return
     */
    @Override
    public List<Map<String, Object>> getFileUrlList(DownloadFilesVO downloadFilesVO) {
        return downloadFilesMapper.getFileUrlList(downloadFilesVO);
    }
}
