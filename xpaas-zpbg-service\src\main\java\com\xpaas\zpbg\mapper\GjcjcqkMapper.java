package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Gjcjcqk;
import com.xpaas.zpbg.vo.GjcjcqkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-关键词检测情况 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface GjcjcqkMapper extends BaseMapper<Gjcjcqk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gjcjcqk
	 * @return
	 */
	List<GjcjcqkVO> selectGjcjcqkPage(IPage page, GjcjcqkVO gjcjcqk);

}
