package com.xpaas.zpbg.controller;

import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.ICfbztxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 刷数据相关接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/data")
@Api(value = "刷数据相关接口", tags = "刷数据相关接口")
public class CfbztxController {

    private ICfbztxService cfbztxService;

    /**
     * 模块管理刷新数据
     *
     * @author: luzhaojun
     * @time: 2025-03-24 16:03:33
     */
    @GetMapping("/mkglSxsj")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "模块管理刷新数据")
    @ApiLog("模块管理刷新数据")
    public R mkglSxsj() {
        return cfbztxService.mkglSxsj();
    }

    /**
     * 报告管理刷新数据
     *
     * @author: luzhaojun
     * @time: 2025-03-24 16:03:33
     */
    @GetMapping("/bgglSxsj")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "报告管理刷新数据")
    @ApiLog("报告管理刷新数据")
    public R bgglSxsj() {
        return cfbztxService.bgglSxsj();
    }


}
