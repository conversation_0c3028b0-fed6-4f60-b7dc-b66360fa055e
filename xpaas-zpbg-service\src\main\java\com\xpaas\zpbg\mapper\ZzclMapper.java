package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Zzcl;
import com.xpaas.zpbg.vo.ZzclVO;
import com.xpaas.zpbg.vo.ZzclglVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-佐证材料 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Repository
public interface ZzclMapper extends BaseMapper<Zzcl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zzcl
	 * @return
	 */
	List<ZzclVO> selectZzclPage(IPage page, ZzclVO zzcl,Long userId,List<String> ssmkidslist);

	/**
	 * 查询材料数据
	 *
	 * @param zzcl
	 * @return
	 */
	List<ZzclVO> checkZzcl(ZzclVO zzcl);

	/**
	 * 删除佐证材料数据
	 *
	 * @param zzclId
	 * @return
	 */
	int deleteByZzclId(Long zzclId);

	/**
	 * 查询材料数据
	 *
	 * @param zzcl
	 * @param page
	 * @return
	 */
	List<ZzclVO> getZzclList(ZzclglVO zzcl,IPage page);

	/**
	 * 查询材料数据
	 *
	 * @param zzcl
	 * @return
	 */
	List<ZzclVO> getZzclId (ZzclVO zzcl);


	/**
	 * 根据老的批次名称和新的批次名称更改佐证材料的批次信息
	 * @param oldPcid
	 * @param newPcid
	 */
	boolean updateZzclByPcid(@Param("oldPcid") Long oldPcid, @Param("newPcid") Long newPcid);

	/**
	 * 根据材料名称查询所有的相同名称的材料
	 */
	List<ZzclVO> listByClmc(@Param("cl") Zzcl zzcl);

}
