package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Jdmkgl;
import com.xpaas.zpbg.vo.JdmkglVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-进度模块关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Repository
public interface JdmkglMapper extends BaseMapper<Jdmkgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param jdmkgl
	 * @return
	 */
	List<JdmkglVO> selectJdmkglPage(IPage page, JdmkglVO jdmkgl);

	/**
	 * 删除当前进度关联的模块
	 *
	 * @param jdmkgl
	 * @return
	 */
	boolean jdmkglDelete(JdmkglVO jdmkgl);

	/**
	 * 消息发送信息
	 *
	 * @param jdmkgl
	 * @return
	 */
	List<Map<String,Object>> selectMessage(JdmkglVO jdmkgl);


}
