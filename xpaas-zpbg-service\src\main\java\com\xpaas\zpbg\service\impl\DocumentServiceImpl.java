package com.xpaas.zpbg.service.impl;


import com.alibaba.fastjson.JSON;
import com.deepoove.poi.util.TableTools;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.documents4j.api.DocumentType;
import com.documents4j.api.IConverter;
import com.documents4j.job.LocalConverter;
import com.esotericsoftware.minlog.Log;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.oss.model.XpaasFile;
import com.xpaas.core.tool.api.R;
import com.xpaas.resource.feign.IOssClient;
import com.xpaas.system.entity.DictBiz;
import com.xpaas.system.feign.IDictBizClient;
import com.xpaas.system.feign.IParamClient;
import com.xpaas.zpbg.entity.*;
import com.xpaas.zpbg.mapper.DocumentMapper;
import com.xpaas.zpbg.service.IBbglService;
import com.xpaas.zpbg.service.IBgglService;
import com.xpaas.zpbg.service.IDocumentService;
import com.xpaas.zpbg.vo.DocumentClVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.util.StringUtil;
import org.apache.poi.util.Units;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xwpf.model.XWPFCommentsDecorator;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.impl.xb.xmlschema.SpaceAttribute;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 自评报告-文档 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@Service
public class DocumentServiceImpl extends BaseServiceImpl<DocumentMapper, Demt> implements IDocumentService {

    private static String ZPDJ_KEY = "【自评等级】";
    private static String WENZHANG = "WENZHANG_";
    private static String FENGMIAN = "FENGMIAN_";
    private static String ZHENGWEN = "ZHENGWEN_";
    private static String ZHANGJIE1 = "ZHANGJIE1_";
    private static String ZHANGJIE2 = "ZHANGJIE2_";

    @Autowired
    public IOssClient ossClient;

    @Resource
    private IDictBizClient dictBizClient;

    @Resource
    private IBbglService bbglService;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    IParamClient paramClient;

    @Autowired
    IBgglService bgglService;

    @Autowired
    ProgressProc progressProc;

    // 材料首页是否初始化
    private int initClPage = 0;

    @Override
    public void exportMbWord(Demt demt, HttpServletRequest request, HttpServletResponse response) {
        try {
            if(StringUtils.isNoneBlank(demt.getMbids())) {
                demt.setMbidList(Arrays.asList(demt.getMbids().split(",")));
            }

            List<Demt> mkList = baseMapper.getMkList(demt);
            if(mkList != null && mkList.size() > 0) {
                NiceXWPFDocument document = mergeMbWord(mkList);
                if(document == null) {
                    document = new NiceXWPFDocument();
                }

                response.reset();
                response.setContentType("application/force-download");

                String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".docx";
                response.setHeader("Content-Disposition", fileName);

                // 返回文件
                OutputStream stream = response.getOutputStream();
                document.write(stream);
                document.close();
            } else {
                throw new ServiceException("没有取到模块文件！");
            }
        } catch(Exception e) {
            e.printStackTrace();

            throw new ServiceException("文件生成失败！");
        }
    }

    @Override
    public String previewMbWord(Demt demt) throws Exception {
        String url = "";

        try {
            if(StringUtils.isNoneBlank(demt.getMbids())) {
                demt.setMbidList(Arrays.asList(demt.getMbids().split(",")));
            }

            List<Demt> mkList = baseMapper.getMkList(demt);
            if(mkList != null && mkList.size() > 0) {
                NiceXWPFDocument document = mergeMbWord(mkList);
                if(document == null) {
                    document = new NiceXWPFDocument();
                }

                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                document.write(baos);
                ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray());

                // 上传附件
                MultipartFile multipartFile = new MockMultipartFile("file", UUID.randomUUID().toString() + ".docx", null, inputStream);
                R<XpaasFile> xpaasFile = ossClient.putFile(multipartFile);
                url = xpaasFile.getData().getLink();

                // 清除缓存
                inputStream.close();
                baos.close();
                document.close();
            } else {
                throw new ServiceException("没有取到模块文件！");
            }
        } catch(Exception e) {
            e.printStackTrace();

            throw new ServiceException("文件生成失败！");
        }

        return url;
    }

    @Override
    public void exportBgWord(Demt demt, HttpServletRequest request, HttpServletResponse response) {
        try {
            NiceXWPFDocument document = mergeBgWord(demt);

            response.reset();
            response.setContentType("application/force-download");

            String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".docx";
            response.setHeader("Content-Disposition", fileName);

            // 返回文件
            OutputStream stream = response.getOutputStream();
            document.write(stream);
            document.close();
        } catch(Exception e) {
            e.printStackTrace();

            //throw new ServiceException("文件生成失败！");
            emptyWord(response);
        }
    }

    @Override
    public void exportBgPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            String pdf = mergeBgPdf(demt);

            response.reset();
            response.setContentType("application/force-download");

            String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".pdf";
            response.setHeader("Content-Disposition", fileName);

            // 获取输出流
            ServletOutputStream outputStream = response.getOutputStream();

            File file = new File(pdf);
            FileInputStream fis = new FileInputStream(file);
            IOUtils.copy(fis, outputStream);

            // 关闭流
            outputStream.close();
            fis.close();
            file.delete();
        } catch(Exception e) {
            e.printStackTrace();

            //throw new ServiceException("文件生成失败！");
            emptyPdf(response);
        }
    }

    @Override
    public void exportMkPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            String pdf = getMkPdf(demt);

            response.reset();
            response.setContentType("application/force-download");

            String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".pdf";
            response.setHeader("Content-Disposition", fileName);

            // 获取输出流
            ServletOutputStream outputStream = response.getOutputStream();

            File file = new File(pdf);
            FileInputStream fis = new FileInputStream(file);
            IOUtils.copy(fis, outputStream);

            // 关闭流
            outputStream.close();
            fis.close();
            file.delete();
        } catch(Exception e) {
            e.printStackTrace();

            //throw new ServiceException("文件生成失败！");
            emptyPdf(response);
        }
    }

    // 更新导出进度
    private void updateProgress(Demt demt, ProgressInfo progressInfo, int mkindex, int bzindex) {
        if(progressInfo == null) {
            return;
        }

        int current = 0;
        int total = demt.getMksl() + demt.getBzsl() * demt.getBzjd();

        if(mkindex >= 0) {
            // 读取模块
            current = mkindex;
        } else {
            // 步骤处理
            current = demt.getMksl() + bzindex * demt.getBzjd();
        }

        progressInfo.setPercent(Math.round(current * 100 / total));
        progressProc.updateProgress(progressInfo);
    }

    private NiceXWPFDocument mergeBgWord(Demt demt) throws Exception {
        // 创建任务
        String taskId = demt.getTaskId();
        ProgressInfo progressInfo = progressProc.createProgress(taskId);

        // 然后这个progressInfo要传来传去的
        progressInfo.setPercent(0);
        progressInfo.setInfo("文件导出中，请稍后...");

        String fileUuid = UUID.randomUUID().toString();
        String fm = FENGMIAN + fileUuid + ".docx";
        String zj1 = ZHANGJIE1 + fileUuid + ".docx";
        String zj2 = ZHANGJIE2 + fileUuid + ".docx";

        List<Demt> bgmkList = baseMapper.getBgmkList(demt);
        if(bgmkList == null || bgmkList.size() == 0) {
            throw new ServiceException("没有模块信息！");
        } else {
            // 模块+封面
            demt.setMksl(bgmkList.size() + 1);
            // 自定义
            demt.setBzsl(3);
            // 自定义
            demt.setBzjd(3);
        }

        int reportBg = 1;
        int reportZccl = 1;
        int reportZzcl = 1;
        initClPage = 0;

        if(StringUtil.isNotBlank(demt.getDcnr())) {
            reportBg = Integer.parseInt(demt.getDcnr().split(",")[0]);
            reportZccl = Integer.parseInt(demt.getDcnr().split(",")[1]);
            reportZzcl = Integer.parseInt(demt.getDcnr().split(",")[2]);
        }

        // 取得佐证材料
        List<Zzclgl> zzclList = baseMapper.getZzclList(demt);
        // 取得备查材料
        List<Zcclgl> zcclList = baseMapper.getZcclList(demt);

        // 文件整合
        // 生成封面
        if(reportBg == 1) {
            // 更新导出进度
            updateProgress(demt, progressInfo, 1, -1);

            saveFm(bgmkList, fm, null, demt.getBgid(), demt);
        }

        // 生成正文
        boolean zj2Flag = saveZj2(demt, progressInfo, bgmkList, zj2, zzclList, zcclList, reportBg, reportZccl, reportZzcl);

        // 生成列表章节，暂时不用生成
        boolean zj1Flag = false;
        if(reportBg == 1 && false) {
            zj1Flag = saveZj1(bgmkList, zj1, zzclList, zcclList);
        }

        File wzFile = null;
        FileInputStream inputStream = null;
        NiceXWPFDocument wzDocument = null;
        if(reportBg == 1) {
            wzFile = new File(fm);
            inputStream = new FileInputStream(wzFile);
            wzDocument = new NiceXWPFDocument(inputStream);
        } else {
            wzDocument = new NiceXWPFDocument();
        }

        // 更新导出进度
        updateProgress(demt, progressInfo, -1, 1);

        // 章节1，章节2
        if(zj1Flag) {
            wzDocument = mergeFile(wzDocument, zj1, true);
        }
        if(zj2Flag) {
            wzDocument = mergeFile(wzDocument, zj2, false);
        }

        // 更新导出进度
        updateProgress(demt, progressInfo, -1, 2);

        // 清除自定义内容
        cleanDocument(wzDocument);

        // 更新导出进度
        updateProgress(demt, progressInfo, -1, 3);

        // 设置纸张大小
        CTBody body = wzDocument.getDocument().getBody();
        if(!body.isSetSectPr()){
            body.addNewSectPr();
        }
        CTSectPr section = body.getSectPr();
        if(!section.isSetPgSz()){
            section.addNewPgSz();
        }
        CTPageSz pgsz = section.getPgSz();
        pgsz.setW(BigInteger.valueOf(11906));
        pgsz.setH(BigInteger.valueOf(16838));

        // 页边距未设置成上下左右分别为“3.7 3.5 2.8 2.6cm”，比例567
        CTPageMar pageMar = section.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(1588)); // 左边距，单位为 1/20 磅
        pageMar.setRight(BigInteger.valueOf(1474)); // 右边距，单位为 1/20 磅
        pageMar.setTop(BigInteger.valueOf(2098)); // 上边距，单位为 1/20 磅
        pageMar.setBottom(BigInteger.valueOf(1985)); // 下边距，单位为 1/20 磅

        // 设置页脚
        initFooter(wzDocument);
        setFooter(wzDocument);

        // 设置页眉
        setHeader(wzDocument);

        if(wzFile != null) {
            inputStream.close();
            wzFile.delete();
        }

        if(reportBg == 1) {
            wzDocument = cleanStyle(wzDocument);
        }

        //设置wps目录格式
        updateTocStyle(wzDocument);
        //设置标题格式
        updateHeadingStyle(wzDocument);
        return wzDocument;
    }

    /**
     * 设置标题格式
     */
    private void updateHeadingStyle(NiceXWPFDocument wzDocument) throws Exception {
        CTStyles styles = wzDocument.getStyle();
        List<CTStyle> styleList = styles.getStyleList();
        for(int i=1;i<4;i++){
            CTStyle style = null;
            for(int j=0;j<styleList.size();j++){
                System.out.println();
                if(("heading "+i).equals(styleList.get(j).getName().getVal().toLowerCase())){
                    style=styleList.get(i);
                    break;
                }
            }
            if(style==null){
                style = styles.addNewStyle();
                //id
                style.setStyleId("heading"+i);
                //type
                style.setType(STStyleType.PARAGRAPH);
                //w:name
                style.addNewName().setVal("heading "+i);
            }
            //w:basedOn
            CTString basedOn = style.getBasedOn()!=null?style.getBasedOn():style.addNewBasedOn();
            basedOn.setVal("1");
            //w:next
            CTString next = style.getNext()!=null?style.getNext():style.addNewNext();
            next.setVal("1");
            //w:rPr
            CTRPr rPr = style.getRPr()!=null?style.getRPr():style.addNewRPr();
            CTFonts rFonts = rPr.getRFontsList().size()>0?rPr.getRFontsList().get(0):rPr.addNewRFonts();
            CTHpsMeasure sz = rPr.getSzList().size()>0?rPr.getSzList().get(0):rPr.addNewSz();
            if(i==1){
                rFonts.setAscii("黑体");
                rFonts.setHAnsi("黑体");
                rFonts.setEastAsia("黑体");
                sz.setVal(36);
            }else{
                rFonts.setAscii("仿宋");
                rFonts.setHAnsi("仿宋");
                rFonts.setEastAsia("仿宋");
                sz.setVal(32);
            }
        }
        wzDocument.getStyles().setStyles(styles);
    }

    /**
     * 设置wps目录格式
     */
    private void updateTocStyle(NiceXWPFDocument wzDocument) throws Exception {
        CTStyles styles = wzDocument.getStyle();
        List<CTStyle> styleList = styles.getStyleList();
        for(int i=1;i<=4;i++){
            CTStyle style = null;
            for(int j=0;j<styleList.size();j++){
                if(("toc "+i).equals(styleList.get(j).getName().getVal().toLowerCase())){
                    style=styleList.get(j);
                    break;
                }
            }
            if(style==null){
                style = styles.addNewStyle();
                //id
                style.setStyleId("toc"+i);
                //type
                style.setType(STStyleType.PARAGRAPH);
                //w:name
                style.addNewName().setVal("toc "+i);
            }
            //w:basedOn
            style.addNewBasedOn().setVal("1");
            //w:next
            style.addNewNext().setVal("1");
            //w:uiPriority
            style.addNewUiPriority().setVal(new BigInteger("39"));
            //w:rPr
            CTRPr rprCTR = style.addNewRPr();
            CTFonts rFontsCTF = rprCTR.addNewRFonts();
            //第一个是黑体，其余楷体
            if(i==1){
                rFontsCTF.setEastAsia("黑体");
                rFontsCTF.setAscii("黑体");
                rFontsCTF.setHAnsi("黑体");
            }else{
                rFontsCTF.setEastAsia("楷体");
                rFontsCTF.setAscii("楷体");
                rFontsCTF.setHAnsi("楷体");
            }
            rFontsCTF.setCstheme(STTheme.MINOR_BIDI);
            //第二个加粗
            if(i==2){
                rprCTR.addNewB();
            }
            rprCTR.addNewSz().setVal("32");
        }
        wzDocument.getStyles().setStyles(styles);
    }

    private String mergeBgPdf(Demt demt) throws Exception {
        String fileUuid = UUID.randomUUID().toString();
        String fm = FENGMIAN + fileUuid + ".docx";
        String zw = ZHENGWEN + fileUuid + ".docx";
        String zj1 = ZHANGJIE1 + fileUuid + ".docx";
        String zj2 = ZHANGJIE2 + fileUuid + ".docx";
        String mainPdf = null;

        List<Demt> bgmkList = baseMapper.getBgmkList(demt);
        if(bgmkList == null || bgmkList.size() == 0) {
            throw new ServiceException("没有模块信息！");
        }

        // 取得佐证材料
        List<Zzclgl> zzclList = baseMapper.getZzclList(demt);
        // 取得备查材料
        List<Zcclgl> zcclList = baseMapper.getZcclList(demt);

        // 文件整合
        boolean zj2Flag = saveZj2(demt, null, bgmkList, zj2, zzclList, zcclList, 1, 1, 1);
        //boolean zj1Flag = saveZj1(bgmkList, zj1, zzclList, zcclList);

        if(true) {
            //File zwFile = new File(zj1);
            //FileInputStream zwInputStream = new FileInputStream(zwFile);
            //NiceXWPFDocument zwDocument = new NiceXWPFDocument(zwInputStream);
            NiceXWPFDocument zwDocument = null;

            if(zj2Flag) {
                File zj2File = new File(zj2);
                FileInputStream zj2InputStream = new FileInputStream(zj2File);
                zwDocument = new NiceXWPFDocument(zj2InputStream);

                //zwDocument = zwDocument.merge(zj2Document);

                zj2InputStream.close();
                zj2File.delete();
            }

            //zwInputStream.close();
            //zwFile.delete();

            // 保存正文文件
            File docFile = new File(zw);
            FileOutputStream fos = new FileOutputStream(docFile);
            zwDocument.write(fos);
            fos.close();

            // 正文转pdf
            String zwPdf = wordToPdf(zw);

            // 读取pdf的目录
            List<WordMenu> menuList = getPdfMenu(bgmkList, zwPdf, 1);

            // 生成封面和目录
            saveFm(bgmkList, fm, menuList, demt.getBgid(), demt);

            String fmPdf = wordToPdf(fm);
            mainPdf = mergePdf(fmPdf, zwPdf);
        } else {
            // 生成封面和目录
            saveFm(bgmkList, fm, null, demt.getBgid(), demt);

            mainPdf = wordToPdf(fm);
        }

        return mainPdf;
    }

    private String getMkPdf(Demt demt) throws Exception {
        String fileUuid = UUID.randomUUID().toString();
        String zw = ZHENGWEN + fileUuid + ".docx";

        // 取得版本信息
        Bbgl bbgl = bbglService.getById(demt.getBbid());

        if(bbgl == null) {
            throw new ServiceException("没有模块信息！");
        }

        // 取得佐证材料
        List<Zzclgl> zzclList = baseMapper.getZzclList(demt);
        // 取得备查材料
        List<Zcclgl> zcclList = baseMapper.getZcclList(demt);

        // 取得模块文档
        NiceXWPFDocument document = getNiceXWPFDocument(bbgl.getWjlj());

        // 清除自定义内容
        cleanDocument(document);

        // 插入材料页
        List<DocumentClVO> clList = getClList(document, zzclList, zcclList, bbgl.getId());
        createZzclTable(document, clList, 1, null, 1);
        createZcclTable(document, clList, 1, null, 1);

        // 保存正文文件
        File docFile = new File(zw);
        FileOutputStream fos = new FileOutputStream(docFile);
        document.write(fos);
        fos.close();

        // 正文转pdf
        String zwPdf = wordToPdf(zw);

        return zwPdf;
    }

    private String wordToPdf(String wordPath) throws Exception {
        String os = System.getProperty("os.name").toLowerCase();
        String pdfPath = "";

        if (os.contains("win")) {
            // Windows操作系统
            pdfPath = createWindowsPdf(wordPath);
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            // Unix/Linux/Mac操作系统
            pdfPath = createLinusPdf(wordPath);
        }

        return pdfPath;
    }

    /**
     * word 转 pdf 临时生成PDF
     *
     * @throws Exception IO异常
     */
    private String createWindowsPdf(String docName) throws Exception {
        // 读取Word
        File docFile = new File(docName);
        FileInputStream inputStream = new FileInputStream(docFile);
        // 生成PDF
        String pdfName = new Date().getTime() + ".PDF";
        File pdfFile = new File(pdfName);
        FileOutputStream fosP = new FileOutputStream(pdfFile);
        IConverter converter = LocalConverter.builder().build();
        converter.convert(inputStream)
                .as(DocumentType.DOCX)
                .to(fosP)
                .as(DocumentType.PDF).execute();
        // 关闭
        converter.shutDown();
        fosP.close();
        inputStream.close();

        if (docFile.exists()) {
            docFile.delete();
        }

        return pdfName;
    }

    private String createLinusPdf(String docName) throws Exception {
        // 执行转换命令
        try {
            File docFile = new File(docName);
            // 构建LibreOffice的命令行工具命令
            String commands = "libreoffice --headless --convert-to pdf:writer_pdf_Export "+ docName;
            boolean result = executeLinuxCmd(commands);
            if (result) {
                // 转换成功，返回转换后的PDF文件
                String pdfFilePath = docName.replace(".docx", "") + ".pdf";
                File newFilePDF = new File(pdfFilePath);
                // 通过输出流生成输入流
                FileInputStream inputStreamPDF = new FileInputStream(newFilePDF);
                if (docFile.exists()) {
                    docFile.delete();
                }
                inputStreamPDF.close();
                return pdfFilePath;
            } else {
                return null;
            }
        } catch (Exception e) {
            // 转换失败
            Log.error("Word文档转换为PDF失败，原因：执行命令时出现异常。", e);
            return null;
        }
    }

    private boolean executeLinuxCmd(String cmd) throws IOException {
        // 执行命令行工具命令
        Process process = Runtime.getRuntime().exec(cmd);
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    // 合成PDF
    private String mergePdf(String filePath1, String filePath2) throws Exception{
        // 合并pdf生成的文件名
        String destinationFileName = UUID.randomUUID().toString();

        // 生成PDF1
        File pdfFile1 = new File(filePath1);
        // 生成PDF2
        File pdfFile2 = new File(filePath2);
        // 需要合并的PDF文件
        List<InputStream> inputStreams = new ArrayList<>();
        inputStreams.add(new FileInputStream(pdfFile1));
        inputStreams.add(new FileInputStream(pdfFile2));

        // org.apache.pdfbox.util.PDFMergerUtility：pdf合并工具类
        PDFMergerUtility mergePdf = new PDFMergerUtility();

        mergePdf.addSources(inputStreams);
        // 设置合并生成pdf文件名称
        mergePdf.setDestinationFileName(destinationFileName + ".PDF");
        // 合并PDF
        mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
        for (InputStream in : inputStreams) {
            if (in != null) {
                in.close();
            }
        }

        if (pdfFile1.exists()) {
            pdfFile1.delete();
        }
        if (pdfFile2.exists()) {
            pdfFile2.delete();
        }

        return destinationFileName + ".PDF";
    }

    // 获取页码
    private List<WordMenu> getPdfMenu(List<Demt> bgmkList, String filePath, int mklx) throws Exception {
        List<WordMenu> menuList = new ArrayList<WordMenu>();
        List<DictBiz> dictData = dictBizClient.getList("zpbg_bt").getData();

        // 三个大标题
        if(dictData != null) {
            for(DictBiz biz : dictData) {
                if(mklx == 1) {
                    if(Integer.parseInt(biz.getDictKey()) < 20) {
                        WordMenu menu = new WordMenu();
                        menu.setName(biz.getDictValue());
                        menu.setLevel(1);
                        menu.setPage(0);
                        menuList.add(menu);
                    }
                }
                if(mklx == 2) {
                    if(Integer.parseInt(biz.getDictKey()) > 20) {
                        WordMenu menu = new WordMenu();
                        menu.setName(biz.getDictValue());
                        menu.setLevel(1);
                        menu.setPage(0);
                        menuList.add(menu);
                    }
                }
            }
        }

        String yjzb = null;
        String ejzb = null;
        for(Demt demt : bgmkList) {
            if(StringUtil.isNotBlank(demt.getYjzb())) {
                if(yjzb == null || !yjzb.equals(demt.getYjzb())) {
                    WordMenu menu = new WordMenu();
                    menu.setName(demt.getYjzb());
                    menu.setLevel(2);
                    menu.setPage(0);
                    menuList.add(menu);
                }
                if(ejzb == null || !ejzb.equals(demt.getEjzb())) {
                    WordMenu menu = new WordMenu();
                    menu.setName(demt.getEjzb());
                    menu.setLevel(3);
                    menu.setPage(0);
                    menuList.add(menu);
                }

                WordMenu menu = new WordMenu();
                menu.setName(demt.getMkmc());
                menu.setLevel(4);
                menu.setPage(0);
                menuList.add(menu);

                yjzb = demt.getYjzb();
                ejzb = demt.getEjzb();
            }
        }

        // 读取PDF文件
        File docFile = new File(filePath);
        FileInputStream inputStream = new FileInputStream(docFile);
        PDDocument document = PDDocument.load(inputStream);
        // 获取文档的目录
        PDDocumentCatalog catalog = document.getDocumentCatalog();
        // 获取文档的所有页面
        PDPageTree pages = catalog.getPages();

        // 所搜第二三部分的起始页
        int startPage = 0;
        for (int i = 1; i <= pages.getCount(); i++) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setStartPage(i);
            stripper.setEndPage(i);
            String content = stripper.getText(document);
            content = content.replaceAll(" ","").replaceAll(" +","").replaceAll("[\\t\\n\\r]","");

            if(menuList.get(0).getPage() == 0 && content.indexOf(menuList.get(0).getName().replaceAll(" ", "")) >= 0) {
                menuList.get(0).setPage(i);
            }
            if(menuList.get(1).getPage() == 0 && content.indexOf(menuList.get(1).getName().replaceAll(" ", "")) >= 0) {
                menuList.get(1).setPage(i);
            }
            if(menuList.get(2).getPage() == 0 && content.indexOf(menuList.get(2).getName().replaceAll(" ", "")) >= 0) {
                menuList.get(2).setPage(i);
                startPage = i;
                break;
            }
        }

        // 遍历第三部分的起始页开始
        for (int i = startPage; i <= pages.getCount(); i++) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setStartPage(i);
            stripper.setEndPage(i);
            String content = stripper.getText(document);
            content = content.replaceAll(" ","").replaceAll(" +","").replaceAll("[\\t\\n\\r]","");

            for (int k = 0; k < menuList.size(); k++) {
                if(menuList.get(k).getPage() == 0 && content.indexOf(menuList.get(k).getName().replaceAll(" ", "")) >= 0) {
                    menuList.get(k).setPage(i);
                }
            }
        }

        // 关闭流
        document.close();
        inputStream.close();

        // 删除找不到的模块
        for (int k = 0; k < menuList.size(); k++) {
            if(menuList.get(k).getPage() == 0) {
                menuList.remove(k);
                k--;
            }
        }

        return menuList;
    }

    private NiceXWPFDocument mergeFile(NiceXWPFDocument wzDocument, String fileName, boolean newPage) throws Exception {
        File zjFile = new File(fileName);
        FileInputStream zjInputStream = new FileInputStream(zjFile);
        NiceXWPFDocument zjDocument = new NiceXWPFDocument(zjInputStream);

        if(newPage) {
            wzDocument = wzDocument.merge(zjDocument);
        } else {
            //XWPFRun lastRun = getLastRun(wzDocument);
            //wzDocument = wzDocument.merge(Arrays.asList(zjDocument).iterator(), lastRun);
            wzDocument = wzDocument.merge(zjDocument);
        }

        zjDocument.close();
        zjInputStream.close();
        zjFile.delete();

        return wzDocument;
    }

    private void saveFm(List<Demt> bgmkList, String fm, List<WordMenu> menuList, Long bgid, Demt demt) throws Exception {
        XWPFDocument document = null;

        if(bgmkList.get(0).getSfwfm() == 0) {
            String wjlj = baseMapper.getFmlj(demt);

            if(StringUtil.isNotBlank(wjlj)) {
                // 读取模版封面
                document = getXWPFDocument(wjlj);
            } else {
                // 自动生成封面
                document = new XWPFDocument();

                // 封面
                createFm(document, bgid);
            }
        } else {
            // 读取模版封面
            String wjlj = bgmkList.get(0).getWjlj();
            document = getXWPFDocument(wjlj);
        }

        // 插入分节
        CTBody body1 = document.getDocument().getBody();
        CTPPr ctpPr1 = body1.addNewP().addNewPPr();
        CTSectPr ctSectPr1 = ctpPr1.addNewSectPr();
        CTPageNumber pagetNumber1 = ctSectPr1.addNewPgNumType();
        pagetNumber1.setStart(null);

        // 设置纸张大小
        CTPageSz pgsz = ctSectPr1.isSetPgSz() ? ctSectPr1.getPgSz() : ctSectPr1.addNewPgSz();
        pgsz.setW(BigInteger.valueOf(11906));
        pgsz.setH(BigInteger.valueOf(16838));

        // 页边距未设置成上下左右分别为“3.7 3.5 2.8 2.6cm”，比例567
        CTPageMar pageMar = ctSectPr1.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(1588)); // 左边距，单位为 1/20 磅
        pageMar.setRight(BigInteger.valueOf(1474)); // 右边距，单位为 1/20 磅
        pageMar.setTop(BigInteger.valueOf(2098)); // 上边距，单位为 1/20 磅
        pageMar.setBottom(BigInteger.valueOf(1985)); // 下边距，单位为 1/20 磅

        // 目录
        if(menuList == null) {
            createWordMenu(document);
        } else {
            createPdfMenu(document, menuList);
        }

        // 清除自定义内容
        cleanDocument(document);

        // 保存文件
        File docFile = new File(fm);
        FileOutputStream fos = new FileOutputStream(docFile);
        document.write(fos);
        fos.close();
    }

    private void createPdfMenu(XWPFDocument document, List<WordMenu> menuList) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        // 设置段落缩进
        //titleParagraph.setIndentationFirstLine(560);
        //titleParagraph.setIndentationLeft(560);

        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setFontFamily("方正小标宋简体");
        titleRun.setFontSize(18);
        titleRun.setColor("000000");
        titleRun.setText("目录");
        //titleRun.addCarriageReturn();

        XWPFParagraph titleParagraph1 = document.createParagraph();
        titleParagraph1.setAlignment(ParagraphAlignment.RIGHT);
        // 设置段落缩进
        //titleParagraph1.setIndentationFirstLine(560);
        //titleParagraph1.setIndentationLeft(560);

        XWPFRun titleRun1 = titleParagraph1.createRun();
        titleRun1.setFontFamily("黑体");
        titleRun1.setFontSize(16);
        titleRun1.setColor("000000");
        for(WordMenu ex : menuList) {
            titleRun1.addCarriageReturn();
            if("1".equals(ex.getLevel())) {
                titleRun1.setBold(true);
            }else{
                titleRun1.setBold(false);
            }
            titleRun1.setText(ex.getName() + "" + getDianNum(ex.getName(), Integer.valueOf(ex.getPage()), Integer.valueOf(ex.getLevel())) + "" + ex.getPage());
            //titleRun1.addCarriageReturn();
        }

        document.createParagraph().createRun();

        // 插入分节
        CTBody body2 = document.getDocument().getBody();
        CTSectPr ctSectPr2 = null;
        if(body2.getSectPr() == null) {
            ctSectPr2 = body2.addNewSectPr();
        } else {
            ctSectPr2 = body2.getSectPr();
        }

        // 设置纸张大小
        CTPageSz pgsz = ctSectPr2.isSetPgSz() ? ctSectPr2.getPgSz() : ctSectPr2.addNewPgSz();
        pgsz.setW(BigInteger.valueOf(11906));
        pgsz.setH(BigInteger.valueOf(16838));

        // 页边距未设置成上下左右分别为“3.7 3.5 2.8 2.6cm”，比例567
        CTPageMar pageMar = ctSectPr2.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(1588)); // 左边距，单位为 1/20 磅
        pageMar.setRight(BigInteger.valueOf(1474)); // 右边距，单位为 1/20 磅
        pageMar.setTop(BigInteger.valueOf(2098)); // 上边距，单位为 1/20 磅
        pageMar.setBottom(BigInteger.valueOf(1985)); // 下边距，单位为 1/20 磅
    }

    /**
     *  返回连接串  .....
     * @param str 标题
     * @param ym 页码
     * @param level 标题级别
     * @return
     */
    private String getDianNum(String str, int ym, int level) {
        int max = 1100;
        int lv = 100;
        int num = 25;
        int point = 14;

        // 页码占位
        int numYm = 0;
        if(ym > 99) {
            numYm = 3;
        } else if(ym > 9) {
            numYm = 2;
        } else {
            numYm = 1;
        }

        // 文字占位
        int strLength = countDigits(str);

        // 空白部分长度
        int number = (max - strLength - numYm * num - (level - 1) * lv) / point;

        String ret = ".";
        for(int i = 0; i < number; i++) {
            ret += ".";
        }
        return ret;
    }

    private int countDigits(String str) {
        int wz = 40;
        int space = 14;
        int num = 25;
        int point = 14;

        int length = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);

            if (Character.isDigit(c)){
                length += num;
            } else if(c == '.') {
                length += point;
            } else if(c == ' ') {
                length += space;
            } else {
                length += wz;
            }
        }

        return length;
    }

    private boolean saveZj1(List<Demt> bgmkList, String zj1, List<Zzclgl> zzclList, List<Zcclgl> zcclList) throws Exception {
        XWPFDocument document = new XWPFDocument();

        // 表格标题
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                16,
                "黑体",
                false,
                "综合自评各主要关注点评价等级及结论",
                "000000",
                false,
                false);

        // 表格内容
        createTable(document, bgmkList);

        document.createParagraph().createRun().addCarriageReturn();

        // 保存文件
        File docFile = new File(zj1);
        FileOutputStream fos = new FileOutputStream(docFile);
        document.write(fos);
        fos.close();

        return true;
    }

    private boolean saveZj2(Demt demt, ProgressInfo progressInfo, List<Demt> bgmkList, String zj2, List<Zzclgl> zzclList, List<Zcclgl> zcclList, int reportBg, int reportZccl, int reportZzcl) throws Exception {
        int index = 0;

        if(bgmkList.get(0).getSfwfm() == 0) {
            index = 0;
        } else {
            index = 1;
        }

        return saveMergeWord(demt, progressInfo, bgmkList, zj2, index, zzclList, zcclList, reportBg, reportZccl, reportZzcl);
    }

    private List<DocumentClVO> getClList(XWPFDocument document, List<Zzclgl> zzclOrgList, List<Zcclgl> zcclOrgList, Long bbid) {
        List<DocumentClVO> clList = new ArrayList();
        int index = 1;

        // 过滤本模块的记录
        List<Zzclgl> zzclList = new ArrayList();
        for(Zzclgl cl : zzclOrgList) {
            if(String.valueOf(bbid).equals(String.valueOf(cl.getBbid()))) {
                zzclList.add(cl);
            }
        }
        List<Zcclgl> zcclList = new ArrayList();
        for(Zcclgl cl : zcclOrgList) {
            if(String.valueOf(bbid).equals(String.valueOf(cl.getBbid()))) {
                zcclList.add(cl);
            }
        }

        // 没有材料时，返回
        if(zzclList.size() == 0 && zcclList.size() == 0) {
            return clList;
        }

        for (XWPFParagraph paragraph : document.getParagraphs()) {
            List<XWPFRun> runs = paragraph.getRuns();

            String title = "";

            for(XWPFRun run : runs ) {
                // 找到黑体标题
                if(("黑体".equals(run.getFontFamily()) || "SimHei".equals(run.getFontFamily()))
                        && run.getFontSizeAsDouble() != null && run.getFontSizeAsDouble() == 16) {
                    if(StringUtil.isNotBlank(run.text())) {
                        title += run.text();
                    }
                }
            }

            if(StringUtil.isNotBlank(title)) {
                DocumentClVO dc = new DocumentClVO();
                dc.setIndex(index);
                dc.setType(1);
                dc.setClmc(title);

                clList.add(dc);
                index++;
            }

            // 找到备注的文字
            XWPFCommentsDecorator d = new XWPFCommentsDecorator(paragraph, null);
            if (d != null && d.getCommentText().length() > 0) {
                String commentText = d.getCommentText();

                // 加入找到的材料
                for(int i = 0; i < zzclList.size(); i++) {
                    Zzclgl cl = zzclList.get(i);
                    if(commentText.indexOf(cl.getGlkey()) >= 0) {
                        DocumentClVO dc = new DocumentClVO();
                        dc.setIndex(index);
                        dc.setType(2);
                        dc.setClmc(cl.getClmc());
                        dc.setCfwz(objTostr(cl.getCfwz()));

                        clList.add(dc);
                        index++;

                        zzclList.remove(i);
                        i--;
                    }
                }

                for(int i = 0; i < zcclList.size(); i++) {
                    Zcclgl cl = zcclList.get(i);
                    if(commentText.indexOf(cl.getGlkey()) >= 0) {
                        DocumentClVO dc = new DocumentClVO();
                        dc.setIndex(index);
                        dc.setType(3);
                        dc.setClmc(cl.getClmc());
                        dc.setCfwz(objTostr(cl.getCfwz()));
                        dc.setBz(objTostr(cl.getBcsm()));

                        clList.add(dc);
                        index++;

                        zcclList.remove(i);
                        i--;
                    }
                }
            }
        }

        return clList;
    }

    private boolean createZzclTable(XWPFDocument document, List<DocumentClVO> clList, int reportBg, String mkmc, int reportType) {
        if(reportType == 0) {
            return false;
        }

        String[] titles = new String[] {"序号", "佐证材料名称", "存放位置"};

        List<DocumentClVO> clFilterList = clList.stream().filter(e -> (e.getType() == 1 || e.getType() == 2)).collect(Collectors.toList());

        // 删除没有材料的标题
        if(clFilterList != null && clFilterList.size() > 0) {
            for(int i = 0; i < clFilterList.size(); i++) {
                if(i == clFilterList.size() - 1) {
                    // 最后一行是标题，删除
                    if(clFilterList.get(i).getType() == 1) {
                        clFilterList.remove(i);
                        i--;
                    }
                } else if(clFilterList.get(i).getType() == 1 && clFilterList.get(i + 1).getType() == 1) {
                    // 连续两个标题，删除
                    clFilterList.remove(i);
                    i--;
                }
            }
        }

        // 没有材料时，返回
        if(clFilterList == null || clFilterList.size() == 0) {
            return false;
        }

        // 只导出材料时，首页不是空白
        if(reportBg == 0 && initClPage == 0) {
            initClPage = 1;
        } else {
            // 插入换页
            document.createParagraph().createRun().addBreak(BreakType.PAGE);
        }

        // 不导出报告时，生成模块名称
        if(reportBg == 0) {
            // 模块名称
            createParagraph(document,
                    ParagraphAlignment.LEFT,
                    0.0f,
                    0.0f,
                    1.5f,
                    16,
                    "黑体",
                    false,
                    mkmc,
                    "000000",
                    false,
                    false);
        }

        // 表格标题
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                16,
                "黑体",
                false,
                "佐证材料目录",
                "000000",
                false,
                false);

        // 创建一个空的table
        XWPFTable table = document.createTable(clFilterList.size() + 1, titles.length);

        // 设置边线
        setTableBorder(table);

        int totalWidth = 100000;
        for (XWPFTableRow row : table.getRows()) {
            // 行高为1厘米
            row.setHeight(568);

            // 设置列宽
            for (int i = 0; i < row.getTableCells().size(); i++) {
                XWPFTableCell cell = row.getTableCells().get(i);

                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER); //垂直居中对齐
                XWPFParagraph paragraph1 = cell.getParagraphArray(0);
                paragraph1.setAlignment(ParagraphAlignment.CENTER); //水平居中对齐

                CTP ctp = paragraph1.getCTP();
                CTPPr ctpPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                CTSpacing ctSpacing = ctpPr.isSetSpacing() ? ctpPr.getSpacing() : ctpPr.addNewSpacing();
                ctSpacing.setAfter(BigInteger.valueOf(0));

                CTTcPr tcPr = cell.getCTTc().addNewTcPr();
                CTTblWidth width = tcPr.addNewTcW();
                width.setType(STTblWidth.DXA);
                if(i == 0) {
                    // 列宽度为1.4厘米
                    width.setW(BigInteger.valueOf(795));
                } else if(i == 1) {
                    // 列宽度为10.6厘米
                    width.setW(BigInteger.valueOf(6020));
                } else if(i == 2) {
                    // 列宽度为3厘米
                    width.setW(BigInteger.valueOf(1703));
                }
            }
        }

        // 设置标题行
        CTRow ctRow = table.getRow(0).getCtRow();
        CTTrPr trPr = ctRow.isSetTrPr() ? ctRow.getTrPr() : ctRow.addNewTrPr();
        trPr.addNewTblHeader();

        // 设置表头
        for(int i = 0; i < titles.length; i++) {
            XWPFTableCell cell = table.getRow(0).getCell(i);
            setCellText(cell, titles[i], "黑体", 12.0, false);
        }

        int rowIndex = 1;
        int rowXh = 1;

        for(int i = 0; i < clFilterList.size(); i++) {
            DocumentClVO dc = clFilterList.get(i);

            if(dc.getType() == 1) {
                // 合并列
                table.getRow(rowIndex).getCell(0).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                table.getRow(rowIndex).getCell(1).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                table.getRow(rowIndex).getCell(2).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);

                setCellText(table.getRow(rowIndex).getCell(0), dc.getClmc(), "黑体", 12.0, false);
            } else {
                setCellText(table.getRow(rowIndex).getCell(0), String.valueOf(rowXh), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(1), dc.getClmc(), "宋体", 10.5, true);
                setCellText(table.getRow(rowIndex).getCell(2), dc.getCfwz(), "宋体", 10.5, false);

                rowXh++;
            }

            rowIndex++;
        }

        return true;
    }

    private boolean createZcclTable(XWPFDocument document, List<DocumentClVO> clList, int reportBg, String mkmc, int reportType) {
        if(reportType == 0) {
            return false;
        }

        String[] titles = new String[] {"序号", "备查材料名称", "存放位置", "备注"};

        List<DocumentClVO> clFilterList = clList.stream().filter(e -> (e.getType() == 1 || e.getType() == 3)).collect(Collectors.toList());

        // 删除没有材料的标题
        if(clFilterList != null && clFilterList.size() > 0) {
            for(int i = 0; i < clFilterList.size(); i++) {
                if(i == clFilterList.size() - 1) {
                    // 最后一行是标题，删除
                    if(clFilterList.get(i).getType() == 1) {
                        clFilterList.remove(i);
                        i--;
                    }
                } else if(clFilterList.get(i).getType() == 1 && clFilterList.get(i + 1).getType() == 1) {
                    // 连续两个标题，删除
                    clFilterList.remove(i);
                    i--;
                }
            }
        }

        // 没有材料时，返回
        if(clFilterList == null || clFilterList.size() == 0) {
            return false;
        }

        // 只导出材料时，首页不是空白
        if(reportBg == 0 && initClPage == 0) {
            initClPage = 1;
        } else {
            // 插入换页
            document.createParagraph().createRun().addBreak(BreakType.PAGE);
        }

        // 不导出报告时，生成模块名称
        if(reportBg == 0) {
            // 模块名称
            createParagraph(document,
                    ParagraphAlignment.LEFT,
                    0.0f,
                    0.0f,
                    1.5f,
                    16,
                    "黑体",
                    false,
                    mkmc,
                    "000000",
                    false,
                    false);
        }

        // 表格标题
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                16,
                "黑体",
                false,
                "备查材料目录",
                "000000",
                false,
                false);

        // 创建一个空的table
        XWPFTable table = document.createTable(clFilterList.size() + 1, titles.length);

        // 设置边线
        setTableBorder(table);

        int totalWidth = 100000;
        for (XWPFTableRow row : table.getRows()) {
            // 行高为1厘米
            row.setHeight(568);

            // 设置列宽
            for (int i = 0; i < row.getTableCells().size(); i++) {
                XWPFTableCell cell = row.getTableCells().get(i);

                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER); //垂直居中对齐
                XWPFParagraph paragraph1 = cell.getParagraphArray(0);
                paragraph1.setAlignment(ParagraphAlignment.CENTER); //水平居中对齐

                CTP ctp = paragraph1.getCTP();
                CTPPr ctpPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                CTSpacing ctSpacing = ctpPr.isSetSpacing() ? ctpPr.getSpacing() : ctpPr.addNewSpacing();
                ctSpacing.setAfter(BigInteger.valueOf(0));

                CTTcPr tcPr = cell.getCTTc().addNewTcPr();
                CTTblWidth width = tcPr.addNewTcW();
                width.setType(STTblWidth.DXA);
                if(i == 0) {
                    // 列宽度为1.4厘米
                    width.setW(BigInteger.valueOf(795));
                } else if(i == 1) {
                    // 剩余宽度
                    width.setW(BigInteger.valueOf(4317));
                } else if(i == 2) {
                    // 位置列宽为3厘米
                    width.setW(BigInteger.valueOf(1703));
                } else if(i == 3) {
                    // 位置列宽为3厘米
                    width.setW(BigInteger.valueOf(1703));
                }
            }
        }

        // 设置标题行
        CTRow ctRow = table.getRow(0).getCtRow();
        CTTrPr trPr = ctRow.isSetTrPr() ? ctRow.getTrPr() : ctRow.addNewTrPr();
        trPr.addNewTblHeader();

        // 设置表头
        for(int i = 0; i < titles.length; i++) {
            XWPFTableCell cell = table.getRow(0).getCell(i);
            setCellText(cell, titles[i], "黑体", 12.0, false);
        }

        int rowIndex = 1;
        int rowXh = 1;

        for(int i = 0; i < clFilterList.size(); i++) {
            DocumentClVO dc = clFilterList.get(i);

            if(dc.getType() == 1) {
                // 合并列
                table.getRow(rowIndex).getCell(0).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.RESTART);
                table.getRow(rowIndex).getCell(1).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                table.getRow(rowIndex).getCell(2).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                table.getRow(rowIndex).getCell(3).getCTTc().getTcPr().addNewHMerge().setVal(STMerge.CONTINUE);

                setCellText(table.getRow(rowIndex).getCell(0), dc.getClmc(), "黑体", 12.0, false);
            } else {
                setCellText(table.getRow(rowIndex).getCell(0), String.valueOf(rowXh), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(1), dc.getClmc(), "宋体", 10.5, true);
                setCellText(table.getRow(rowIndex).getCell(2), dc.getCfwz(), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(3), dc.getBz(), "宋体", 10.5, false);

                rowXh++;
            }

            rowIndex++;
        }

        return true;
    }

    private String objTostr(Object obj) {
        if(obj == null) {
           return "";
        } else {
            return String.valueOf(obj);
        }
    }

    private void createTable(XWPFDocument document, List<Demt> bgmkList) {
        String[] titles = new String[] {"一级指标", "二级指标", "主要关注点", "自评等级", "牵头单位"};
        int zbtxSize = 0;
        for(Demt d : bgmkList) {
            if(d.getSfwfm() == 0 && StringUtils.isNotEmpty(d.getYjzb())) {
                zbtxSize++;
            }
        }

        // 创建一个空的table
        XWPFTable table = document.createTable(zbtxSize + 1, titles.length);

        // 设置边线
        setTableBorder(table);

        int totalWidth = 100000;
        for (XWPFTableRow row : table.getRows()) {
            // 设置行最小高度，换算值0.92
            row.setHeight(526);

            // 设置列宽
            for (int i = 0; i < row.getTableCells().size(); i++) {
                XWPFTableCell cell = row.getTableCells().get(i);

                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER); //垂直居中对齐
                XWPFParagraph paragraph1 = cell.getParagraphArray(0);
                paragraph1.setAlignment(ParagraphAlignment.CENTER); //水平居中对齐

                CTP ctp = paragraph1.getCTP();
                CTPPr ctpPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                CTSpacing ctSpacing = ctpPr.isSetSpacing() ? ctpPr.getSpacing() : ctpPr.addNewSpacing();
                ctSpacing.setAfter(BigInteger.valueOf(0));

                CTTcPr tcPr = cell.getCTTc().addNewTcPr();
                CTTblWidth width = tcPr.addNewTcW();
                width.setType(STTblWidth.DXA);
                if(i == 0) {
                    width.setW(BigInteger.valueOf(1821));
                } else if(i == 1) {
                    width.setW(BigInteger.valueOf(1496));
                } else if(i == 2) {
                    width.setW(BigInteger.valueOf(3466));
                } else if(i == 3) {
                    width.setW(BigInteger.valueOf(1693));
                } else if(i == 4) {
                    width.setW(BigInteger.valueOf(1523));
                }
            }
        }

        // 设置标题行
        CTRow ctRow = table.getRow(0).getCtRow();
        CTTrPr trPr = ctRow.isSetTrPr() ? ctRow.getTrPr() : ctRow.addNewTrPr();
        trPr.addNewTblHeader();


        // 设置表头
        for(int i = 0; i < titles.length; i++) {
            XWPFTableCell cell = table.getRow(0).getCell(i);
            setCellText(cell, titles[i], "黑体", 12.0, false);
        }

        int rowIndex = 1;
        int yjzbRowIndex = rowIndex;
        int ejzbRowIndex = rowIndex;
        String yjzbOld = null;
        String ejzbOld = null;

        for(int i = 0; i < bgmkList.size(); i++) {
            Demt d = bgmkList.get(i);
            if(d.getSfwfm() == 0 && StringUtils.isNotEmpty(d.getYjzb())) {
                // 名称变化时，合并
                if(yjzbOld != null && !yjzbOld.equals(d.getYjzb())) {
                    if(yjzbRowIndex != rowIndex - 1) {
                        TableTools.mergeCellsVertically(table, 0, yjzbRowIndex, rowIndex - 1);
                        yjzbRowIndex = rowIndex;
                    }
                }
                if(ejzbOld != null && !ejzbOld.equals(d.getEjzb())) {
                    if(ejzbRowIndex != rowIndex - 1) {
                        TableTools.mergeCellsVertically(table, 1, ejzbRowIndex, rowIndex - 1);
                        ejzbRowIndex = rowIndex;
                    }
                }

                setCellText(table.getRow(rowIndex).getCell(0), d.getYjzb(), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(1), d.getEjzb(), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(2), d.getMkmc(), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(3), d.getZpdj(), "宋体", 10.5, false);
                setCellText(table.getRow(rowIndex).getCell(4), d.getQtdw(), "宋体", 10.5, false);

                // 最后一行
                if(i == bgmkList.size() - 1) {
                    if(yjzbRowIndex != rowIndex) {
                        TableTools.mergeCellsVertically(table, 0, yjzbRowIndex, rowIndex);
                    }
                    if(ejzbRowIndex != rowIndex) {
                        TableTools.mergeCellsVertically(table, 1, ejzbRowIndex, rowIndex);
                    }
                }

                rowIndex++;
                yjzbOld = d.getYjzb();
                ejzbOld = d.getEjzb();
            }
        }
    }

    private void setTableBorder(XWPFTable table) {
        BigInteger line1 = new BigInteger("1");
        BigInteger line2 = new BigInteger("15");

        CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();

        CTBorder hBorder = borders.addNewInsideH();
        hBorder.setVal(STBorder.Enum.forString("single"));  // 线条类型
        hBorder.setSz(line1); // 线条大小
        hBorder.setColor("000000"); // 设置颜色

        CTBorder vBorder = borders.addNewInsideV();
        vBorder.setVal(STBorder.Enum.forString("single"));
        vBorder.setSz(line1);
        vBorder.setColor("000000");

        CTBorder lBorder = borders.addNewLeft();
        lBorder.setVal(STBorder.Enum.forString("single"));
        lBorder.setSz(line2);
        lBorder.setColor("000000");

        CTBorder rBorder = borders.addNewRight();
        rBorder.setVal(STBorder.Enum.forString("single"));
        rBorder.setSz(line2);
        rBorder.setColor("000000");

        CTBorder tBorder = borders.addNewTop();
        tBorder.setVal(STBorder.Enum.forString("single"));
        tBorder.setSz(line2);
        tBorder.setColor("000000");

        CTBorder bBorder = borders.addNewBottom();
        bBorder.setVal(STBorder.Enum.forString("single"));
        bBorder.setSz(line2);
        bBorder.setColor("000000");
    }

    private void setCellText(XWPFTableCell cell, String text, String fontFamily, Double fontSize, boolean isLeft) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);    //获取单元格的段落。

        // 文字居左
        if(isLeft) {
            paragraph.setAlignment(ParagraphAlignment.LEFT);
        }

        XWPFRun run = paragraph.createRun();    //创建一个新的运行对象。
        run.setText(text);                //设置单元格中的文本内容。
        run.setFontFamily(fontFamily);    //设置字体
        run.setFontSize(fontSize);
    }

    private void createFm(XWPFDocument document, Long bgid) throws Exception {

        // 报告信息
        Bggl bggl = bgglService.getById(bgid);

        // 空行
        createEmpty(document, ParagraphAlignment.CENTER);
        createEmpty(document, ParagraphAlignment.CENTER);
        createEmpty(document, ParagraphAlignment.CENTER);
        createEmpty(document, ParagraphAlignment.CENTER);

        // 标题1
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                36,
                "方正小标宋简体",
                false,
                bggl.getNd() + "年度",
                "000000",
                false,
                false);

        // 标题2
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                36,
                "方正小标宋简体",
                false,
                bggl.getBgmc(),
                "000000",
                false,
                false);

        // 空行
        createEmpty(document, ParagraphAlignment.LEFT);
        createEmpty(document, ParagraphAlignment.LEFT);
        createEmpty(document, ParagraphAlignment.LEFT);

        // 图片
        createPicture(document, "classpath:doc/main_img.png", 200, 200);

        // 空行
        createEmpty(document, ParagraphAlignment.LEFT);
        createEmpty(document, ParagraphAlignment.LEFT);
        createEmpty(document, ParagraphAlignment.LEFT);

        // 学院名
        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                20,
                "楷体_GB2312",
                false,
                "海军潜艇学院",
                "000000",
                false,
                false);

        // 日期
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());

        createParagraph(document,
                ParagraphAlignment.CENTER,
                0.0f,
                0.0f,
                1.5f,
                20,
                "楷体_GB2312",
                false,
                cal.get(Calendar.YEAR) + "年" + (cal.get(Calendar.MONTH) + 1) + "月",
                "000000",
                false,
                false);
    }

    /**
     * 插入空行
     *
     */
    private void createEmpty(XWPFDocument document, ParagraphAlignment linePosition) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(linePosition);

        XWPFRun rEmpty = paragraph.createRun();
        rEmpty.setText("");
    }

    /**
     * 插入图片
     *
     */
    private void createPicture(XWPFDocument document, String fileName, int width, int height) throws Exception {
        // 获取文件流
        org.springframework.core.io.Resource resource = resourceLoader.getResource(fileName);
        InputStream inputStream= resource.getInputStream();

        // 创建段落
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 创建一个新的运行块
        XWPFRun run = paragraph.createRun();
        run.addPicture(inputStream, XWPFDocument.PICTURE_TYPE_PNG, "", Units.toEMU(width), Units.toEMU(height));
        run.addCarriageReturn();
    }

    /**
     * 插入行
     *
     */
    private void createParagraph(XWPFDocument document, ParagraphAlignment linePosition, float beforePointSize, float afterPointSize, float linePointSize,
                              int fontSize, String fontFamily, boolean isAddTab, String contentDesc, String color, boolean isLast, boolean isUnderLine) {
        XWPFParagraph paragraph = document.createParagraph();

        paragraph.setAlignment(linePosition);
        setLineHeight(paragraph, beforePointSize, afterPointSize, linePointSize);

        createContentParagraph(paragraph, fontSize, fontFamily, isAddTab, contentDesc, color, isUnderLine);
    }

    /**
     * 设置行高
     *
     * @param paragraph  段落变量
     * @param beforePointSize 段前
     * @param afterPointSize  段后
     * @param linePointSize   行距
     */
    private void setLineHeight(XWPFParagraph paragraph, float beforePointSize, float afterPointSize, float linePointSize) {
        CTP ctp = paragraph.getCTP();
        CTPPr ctpPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
        CTSpacing ctSpacing = ctpPr.isSetSpacing() ? ctpPr.getSpacing() : ctpPr.addNewSpacing();
        ctSpacing.setBefore(BigInteger.valueOf(Math.round(beforePointSize * 20)));
        ctSpacing.setAfter(BigInteger.valueOf(Math.round(afterPointSize * 20)));
        ctSpacing.setLine(BigInteger.valueOf(Math.round(linePointSize * 240)));
        ctSpacing.setLineRule(STLineSpacingRule.AUTO);
    }

    /**
     * 创建正文内容样式
     *
     * @param paragraph   段落变量
     * @param fontSize    文字大小
     * @param fontFamily  文字样式
     * @param isAddTab    是否添加首行缩进tab
     * @param contentDesc 正文描述
     * @param color       文字颜色
     */
    private void createContentParagraph(XWPFParagraph paragraph, int fontSize, String fontFamily, boolean isAddTab, String contentDesc, String color, boolean isUnderLine) {
        XWPFRun contentFun = paragraph.createRun();
        contentFun.setColor(color);
        contentFun.setFontSize(fontSize);
        contentFun.setFontFamily(fontFamily);
        if (isUnderLine) {
            contentFun.setUnderline(UnderlinePatterns.SINGLE);
        }
        if (isAddTab) {
            contentFun.addTab();
        }
        contentFun.setText(contentDesc);
    }

    /**
     * 生成目录
     *
     * @param document Word文档对象
     */
    private void createWordMenu(XWPFDocument document) {
        // 添加目录
        // 创建段落对象
        XWPFParagraph paragraph = document.createParagraph();

        // 设置段落样式
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setVerticalAlignment(TextAlignment.CENTER);
        paragraph.setSpacingAfter(11);
        paragraph.setSpacingBefore(11);
        paragraph.setStyle("TOCHeading");

        XWPFRun tocRun = paragraph.createRun();
        tocRun.setText("目录");
        tocRun.setFontFamily("方正小标宋简体");
        tocRun.setFontSize(18);

        // 创建目录
        CTP ctP = paragraph.getCTP();
        CTSimpleField toc = ctP.addNewFldSimple();
        toc.setInstr("TOC \\o \"1-5\" \\h \\z \\u");
        toc.setDirty(true);

        // 换页
        //document.createParagraph().setPageBreak(true);

        // 插入分节
        CTBody body2 = document.getDocument().getBody();
        CTPPr ctpPr2 = body2.addNewP().addNewPPr();
        CTSectPr ctSectPr2 = ctpPr2.addNewSectPr();
        CTPageNumber pagetNumber2 = ctSectPr2.addNewPgNumType();
        pagetNumber2.setStart(null);

        // 设置纸张大小
        CTPageSz pgsz = ctSectPr2.isSetPgSz() ? ctSectPr2.getPgSz() : ctSectPr2.addNewPgSz();
        pgsz.setW(BigInteger.valueOf(11906));
        pgsz.setH(BigInteger.valueOf(16838));

        // 页边距未设置成上下左右分别为“3.7 3.5 2.8 2.6cm”，比例567
        CTPageMar pageMar = ctSectPr2.addNewPgMar();
        pageMar.setLeft(BigInteger.valueOf(1588)); // 左边距，单位为 1/20 磅
        pageMar.setRight(BigInteger.valueOf(1474)); // 右边距，单位为 1/20 磅
        pageMar.setTop(BigInteger.valueOf(2098)); // 上边距，单位为 1/20 磅
        pageMar.setBottom(BigInteger.valueOf(1985)); // 下边距，单位为 1/20 磅
    }

    /**
     * 合并文件
     *
     * @param bgmkList 报告模块列表
     * @param fileName 文件路径
     * @param index 开始位置
     * @throws Exception 异常
     */
    private boolean saveMergeWord(Demt demt,
                                  ProgressInfo progressInfo,
                                  List<Demt> bgmkList,
                                  String fileName,
                                  int index,
                                  List<Zzclgl> zzclList,
                                  List<Zcclgl> zcclList,
                                  int reportBg,
                                  int reportZccl,
                                  int reportZzcl) throws Exception {
        NiceXWPFDocument document = null;

        if(bgmkList != null && index < bgmkList.size()) {
            // 更新导出进度
            updateProgress(demt, progressInfo, index + 1, -1);

            String mkmc = bgmkList.get(index).getMkmc();
            Long bbid = bgmkList.get(index).getId();
            document = getNiceXWPFDocument(bgmkList.get(index).getWjlj());

            List<DocumentClVO> clList = getClList(document, zzclList, zcclList, bbid);

            // 不导出报告时，用空文档
            if(reportBg == 0) {
                document = new NiceXWPFDocument();
            }

            boolean clFlag1 = createZzclTable(document, clList, reportBg, mkmc, reportZzcl);
            boolean clFlag2 = createZcclTable(document, clList, reportBg, mkmc, reportZccl);

            if(reportBg == 1 && (clFlag1 || clFlag2)) {
                // 插入换页
                document.createParagraph().createRun().addBreak(BreakType.PAGE);
            }

            for(int i = index + 1; i < bgmkList.size(); i++) {
                Demt bgmk = bgmkList.get(i);
                mkmc = bgmk.getMkmc();
                bbid = bgmk.getId();

                if(StringUtils.isEmpty(bgmk.getWjlj())) {
                    continue;
                }

                // 更新导出进度
                updateProgress(demt, progressInfo, i + 1, -1);

                NiceXWPFDocument bgmkDocument = getNiceXWPFDocument(bgmk.getWjlj());
                if(bgmkDocument == null) {
                    continue;
                }

                // 取得自评等级
                bgmk.setZpdj(getZpdj(bgmkDocument));

                clList = getClList(bgmkDocument, zzclList, zcclList, bbid);

                // 不导出报告时，用空文档
                if(reportBg == 0) {
                    bgmkDocument = new NiceXWPFDocument();
                }

                clFlag1 = createZzclTable(bgmkDocument, clList, reportBg, mkmc, reportZzcl);
                clFlag2 = createZcclTable(bgmkDocument, clList, reportBg, mkmc, reportZccl);

                if(reportBg == 1 && (clFlag1 || clFlag2)) {
                    // 插入换页
                    bgmkDocument.createParagraph().createRun().addBreak(BreakType.PAGE);
                }

                // 文档合并
                //XWPFRun lastRun = getLastRun(document);
                //document = document.merge(Arrays.asList(bgmkDocument).iterator(), lastRun);
                document = document.merge(bgmkDocument);
            }
        } else {
            return false;
        }

        // 清除自定义内容
        cleanDocument(document);

        // 保存文件
        File docFile = new File(fileName);
        FileOutputStream fos = new FileOutputStream(docFile);
        document.write(fos);
        fos.close();

        return true;
    }

    /**
     * 合并模版文件
     *
     * @param bgmkList 报告模块列表
     * @throws Exception 异常
     */
    private NiceXWPFDocument mergeMbWord(List<Demt> bgmkList) throws Exception {
        NiceXWPFDocument document = null;

        if(bgmkList != null && bgmkList.size() > 0) {
            document = getNiceXWPFDocument(bgmkList.get(0).getWjlj());

            // 多余一页时，加入分页
            if(bgmkList.get(0).getSfwfm() == 1 && bgmkList.size() > 1) {
                document.createParagraph().setPageBreak(true);
            }

            for(int i = 1; i < bgmkList.size(); i++) {
                Demt bgmk = bgmkList.get(i);

                System.out.println(bgmk.getWjlj());
                System.out.println(bgmk.getWjlj());
                System.out.println(bgmk.getWjlj());

                NiceXWPFDocument bgmkDocument = getNiceXWPFDocument(bgmk.getWjlj());

                if (bgmkDocument != null) {
                    if(i == 1) {
                        document = document.merge(bgmkDocument);
                    } else {
                        // 文档合并
                        //XWPFRun lastRun = getLastRun(document);
                        //document = document.merge(Arrays.asList(bgmkDocument).iterator(), lastRun);
                        document = document.merge(bgmkDocument);
                    }
                }
            }
        }

        return document;
    }

    /**
     * 取得章节最后的run
     *
     * @param document 文件对象
     */
    private XWPFRun getLastRun2(NiceXWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.addBreak();
        return run;
    }

    /**
     * 取得文件对象
     *
     * @param wjlj 文件路径
     */
    private NiceXWPFDocument getNiceXWPFDocument(String wjlj) throws Exception {
        if(StringUtil.isBlank(wjlj)) {
            return new NiceXWPFDocument();
        }

        NiceXWPFDocument document = null;
        R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj,"minio11");

        if (inputR.isSuccess()) {
            log.info("取得文件流成功");
            byte[] bytes = inputR.getData();
            InputStream inputStream = new ByteArrayInputStream(bytes);

//            FileOutputStream fos = new FileOutputStream(UUID.randomUUID().toString() + ".docx");
//            byte[] b = new byte[1];
//            while ((inputStream.read(b)) != -1) {
//                fos.write(b);// 写入数据
//            }
//            fos.close();

            if(inputStream.available() > 0) {
                document = new NiceXWPFDocument(inputStream);
            }
        } else {
            log.info("取得文件流失败");
            log.info(JSON.toJSONString(inputR));

            throw new ServiceException("文件服务器下载文件失败");
        }

        return document;
    }

    /**
     * 取得文件对象
     *
     * @param wjlj 文件路径
     */
    private XWPFDocument getXWPFDocument(String wjlj) throws Exception {
        if(StringUtil.isBlank(wjlj)) {
            return new XWPFDocument();
        }

        XWPFDocument document = null;

        System.out.println(wjlj);
        System.out.println(wjlj);
        System.out.println(wjlj);

        R<byte[]> inputR = ossClient.getFileBuffer("/" + wjlj,"minio11");
        if (inputR.isSuccess()) {
            log.info("取得文件流成功");
            byte[] bytes = inputR.getData();
            InputStream inputStream = new ByteArrayInputStream(bytes);

            if(inputStream.available() > 0) {
                document = new XWPFDocument(inputStream);
            }
        } else {
            log.info("取得文件流失败");
            log.info(JSON.toJSONString(inputR));

            throw new ServiceException("文件服务器下载文件失败");
        }

        return document;
    }

    /**
     * 取得自评等级
     *
     * @param document 文件对象
     */
    private String getZpdj(NiceXWPFDocument document) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if(text.indexOf(ZPDJ_KEY) >= 0) {
                return text.replace(ZPDJ_KEY, "");
            }
        }

        return "";
    }

    /**
     * 清除自定义内容
     *
     * @param document 文件对象
     */
    private void cleanDocument(XWPFDocument document) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            List<XWPFRun> runs = paragraph.getRuns();

            // 删除批注
            for(XWPFRun run : runs ) {
                CTR ctr = run.getCTR();

                if(ctr != null) {
                    List<CTMarkup> markupList = ctr.getCommentReferenceList();
                    if(markupList != null) {
                        // 从后往前删除
                        for(int i = markupList.size() - 1; i >= 0; i--) {
                            ctr.removeCommentReference(i);
                        }
                    }
                }
            }

            // 文字高亮
            for(XWPFRun run: runs){
                if(run.getCTR() != null && run.getCTR().getRPr() != null) {
                    List<CTHighlight> highlightList = run.getCTR().getRPr().getHighlightList();
                    if (highlightList != null && highlightList.size() > 0) {
                        for (CTHighlight ctHighlight : highlightList) {
                            ctHighlight.setVal(STHighlightColor.NONE);
                        }
                    }
                }
            }

            // 文字颜色
            for(XWPFRun run: runs){
                if(run.getColor() != null) {
                    run.setColor(null);
                }
            }
        }
    }

    private void setHeader(XWPFDocument document) throws Exception {
        if(true) {
            XWPFHeader header = document.createHeader(HeaderFooterType.FIRST);
            XWPFParagraph paragraph = header.createParagraph();
            paragraph.setBorderBottom(Borders.NONE);
            paragraph.setAlignment(ParagraphAlignment.LEFT); //设置靠左
            paragraph.setSpacingBefore(120);
        }
        if(true) {
            XWPFHeader header = document.createHeader(HeaderFooterType.DEFAULT);
            XWPFParagraph paragraph = header.createParagraph();
            paragraph.setBorderBottom(Borders.NONE);
            paragraph.setAlignment(ParagraphAlignment.LEFT); //设置靠左
            paragraph.setSpacingBefore(120);
        }
        if(true) {
            XWPFHeader header = document.createHeader(HeaderFooterType.EVEN);
            XWPFParagraph paragraph = header.createParagraph();
            paragraph.setBorderBottom(Borders.NONE);
            paragraph.setAlignment(ParagraphAlignment.LEFT); //设置靠左
            paragraph.setSpacingBefore(120);
        }
    }

    private void initFooter(XWPFDocument document) throws Exception {
        // set evenAndOddHeaders in settings.xml
        Field _settings = XWPFDocument.class.getDeclaredField("settings");
        _settings.setAccessible(true);
        XWPFSettings xwpfsettings = (XWPFSettings)_settings.get(document);
        Field _ctSettings = XWPFSettings.class.getDeclaredField("ctSettings");
        _ctSettings.setAccessible(true);
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSettings ctsettings =
                (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSettings)_ctSettings.get(xwpfsettings);


        // 开启奇偶不同模式
        ctsettings.addNewEvenAndOddHeaders();
    }

    private void setFooter(XWPFDocument document) throws Exception {
        //section setting for section above == last section in document
        CTDocument1 ctDocument = document.getDocument();
        CTBody ctBody = ctDocument.getBody();
        CTSectPr ctSectPrLastSect = ctBody.getSectPr(); //there must be a SectPr already because of the footer settings above

        if(ctSectPrLastSect != null) {
            List<CTHdrFtrRef> ctHdrFtrRefList = ctSectPrLastSect.getFooterReferenceList();
            for (int i = 0; i < ctHdrFtrRefList.size(); i++) {
                if (i == ctHdrFtrRefList.size() - 1) {
                    ctHdrFtrRefList.get(i).setType(STHdrFtr.DEFAULT);
                } else {
                    ctHdrFtrRefList.get(i).setType(STHdrFtr.FIRST);
                }
            }
        }

        //get footer reference of first footer and move this to be footer reference for section 2
        //CTHdrFtrRef ctHdrFtrRef = ctSectPrLastSect.getFooterReferenceArray(0);

        //ctHdrFtrRef.setType(STHdrFtr.DEFAULT); //change this from STHdrFtr.FIRST to STHdrFtr.DEFAULT
        //CTHdrFtrRef[] ctHdrFtrRefs = new CTHdrFtrRef[]{ctHdrFtrRef};
//        ctSectPrSect2.setFooterReferenceArray(ctHdrFtrRefs);
//        ctSectPrLastSect.removeFooterReference(0);

        //unset "there is a title page" for the whole document because we have a section for the title (cover)
        //ctSectPrLastSect.unsetTitlePg();

        if(true) {
            // 设置所有页码靠左显示。
            // 设置所有页码靠左显示。
            // 设置所有页码靠左显示。
            XWPFFooter footer = document.createFooter(HeaderFooterType.FIRST);//创建一个新的XWPFFooter对象，HeaderFooterType.DEFAULT表示所有页
            XWPFParagraph paragraph = footer.createParagraph();//创建新的XWPFParagraph对象
            paragraph.setAlignment(ParagraphAlignment.LEFT);//设置页码靠左
            paragraph.setSpacingAfter(660);

            //设置段落对象
            XWPFRun runPre = paragraph.createRun();//新的段落对象
            runPre.setText("—");
            setXWPFRunStyle(runPre,"宋体",14);

            XWPFRun run = paragraph.createRun();//新的段落对象
            CTFldChar fldChar = run.getCTR().addNewFldChar();//新的CTFldChar对象
            fldChar.setFldCharType(STFldCharType.Enum.forString("begin"));
            CTText ctText = run.getCTR().addNewInstrText();
            ctText.setStringValue("PAGE  \\* MERGEFORMAT");
            ctText.setSpace(SpaceAttribute.Space.Enum.forString("preserve"));
            setXWPFRunStyle(run,"宋体",14);
            fldChar = run.getCTR().addNewFldChar();
            fldChar.setFldCharType(STFldCharType.Enum.forString("end"));

            //设置段落对象
            XWPFRun runSuf = paragraph.createRun();//新的段落对象
            runSuf.setText("—");
            setXWPFRunStyle(runSuf,"宋体",14);
        }

        if(true) {
            // 设置所有页码靠左显示。
            // 设置所有页码靠左显示。
            // 设置所有页码靠左显示。
            XWPFFooter footer = document.createFooter(HeaderFooterType.DEFAULT);//创建一个新的XWPFFooter对象，HeaderFooterType.DEFAULT表示所有页
            XWPFParagraph paragraph = footer.createParagraph();//创建新的XWPFParagraph对象
            paragraph.setAlignment(ParagraphAlignment.LEFT);//设置页码靠左
            paragraph.setSpacingAfter(660);

            //设置段落对象
            XWPFRun runPre = paragraph.createRun();//新的段落对象
            runPre.setText("—");
            setXWPFRunStyle(runPre,"宋体",14);

            XWPFRun run = paragraph.createRun();//新的段落对象
            CTFldChar fldChar = run.getCTR().addNewFldChar();//新的CTFldChar对象
            fldChar.setFldCharType(STFldCharType.Enum.forString("begin"));
            CTText ctText = run.getCTR().addNewInstrText();
            ctText.setStringValue("PAGE  \\* MERGEFORMAT");
            ctText.setSpace(SpaceAttribute.Space.Enum.forString("preserve"));
            setXWPFRunStyle(run,"宋体",14);
            fldChar = run.getCTR().addNewFldChar();
            fldChar.setFldCharType(STFldCharType.Enum.forString("end"));

            //设置段落对象
            XWPFRun runSuf = paragraph.createRun();//新的段落对象
            runSuf.setText("—");
            setXWPFRunStyle(runSuf,"宋体",14);
        }

        if(true) {
            // 设置偶数的页码靠右显示。
            // 设置偶数的页码靠右显示。
            // 设置偶数的页码靠右显示。
            XWPFFooter footer = document.createFooter(HeaderFooterType.EVEN);//创建一个新的XWPFFooter对象，HeaderFooterType.EVEN 表示偶数页
            XWPFParagraph paragraph = footer.createParagraph();//创建新的XWPFParagraph对象
            paragraph.setAlignment(ParagraphAlignment.RIGHT);//设置页码靠右
            paragraph.setSpacingAfter(660);

            //设置段落对象
            XWPFRun runPre = paragraph.createRun();//新的段落对象
            runPre.setText("—");
            setXWPFRunStyle(runPre,"宋体",14);

            XWPFRun run = paragraph.createRun();//新的段落对象
            CTFldChar fldChar = run.getCTR().addNewFldChar();//新的CTFldChar对象
            fldChar.setFldCharType(STFldCharType.Enum.forString("begin"));
            CTText ctText = run.getCTR().addNewInstrText();
            ctText.setStringValue("PAGE  \\* MERGEFORMAT");
            ctText.setSpace(SpaceAttribute.Space.Enum.forString("preserve"));
            setXWPFRunStyle(run,"宋体",14);
            fldChar = run.getCTR().addNewFldChar();
            fldChar.setFldCharType(STFldCharType.Enum.forString("end"));

            //设置段落对象
            XWPFRun runSuf = paragraph.createRun();//新的段落对象
            runSuf.setText("—");
            setXWPFRunStyle(runSuf,"宋体",14);
        }

    }

    /**
     * 设置页脚的字体样式
     * @param xr 段落元素
     * @param font 段落元素
     * @param fontSize 的大小
     */
    private void setXWPFRunStyle(XWPFRun xr, String font, int fontSize) {
        xr.setFontSize(fontSize);
        CTRPr rpr = xr.getCTR().isSetRPr() ? xr.getCTR().getRPr() : xr.getCTR().addNewRPr();
        CTFonts fonts = rpr.addNewRFonts();
        fonts.setAscii(font);
        fonts.setEastAsia(font);
        fonts.setHAnsi(font);
    }


    @Override
    public String previewBgPdf(Demt demt) throws Exception {
        String url = null;

        try {
            String pdf = mergeBgPdf(demt);

            File file = new File(pdf);
            FileInputStream inputStream = new FileInputStream(file);

            // 上传附件
            MultipartFile multipartFile = new MockMultipartFile("file", UUID.randomUUID().toString() + ".docx", null, inputStream);
            R<XpaasFile> xpaasFile = ossClient.putFile(multipartFile);
            url = xpaasFile.getData().getLink();

            inputStream.close();
            file.delete();
        } catch(Exception e) {
            e.printStackTrace();

            throw new ServiceException("文件生成失败！");
        }

        return url;
    }

    private void emptyWord(HttpServletResponse response) {
        try {
            //获取文件流
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:doc/empty.docx");
            InputStream inStream = resource.getInputStream();
            NiceXWPFDocument document = new NiceXWPFDocument(inStream);

            response.reset();
            response.setContentType("application/force-download");

            String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".docx";
            response.setHeader("Content-Disposition", fileName);

            // 返回文件
            OutputStream stream = response.getOutputStream();
            document.write(stream);
            document.close();
        } catch(Exception e) {
            e.printStackTrace();
        }
    }

    private void emptyPdf(HttpServletResponse response) {
        try {
            //获取文件流
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:doc/empty.pdf");
            InputStream inStream = resource.getInputStream();

            response.reset();
            response.setContentType("application/force-download");

            String fileName = "attachment;filename=" + UUID.randomUUID().toString() + ".docx";
            response.setHeader("Content-Disposition", fileName);

            // 返回文件
            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.copy(inStream, outputStream);

            // 关闭流
            outputStream.close();
            inStream.close();
        } catch(Exception e) {
            e.printStackTrace();
        }
    }

    private NiceXWPFDocument cleanStyle(NiceXWPFDocument document) throws Exception {
        String docxFilePath = "自评报告-临时"+ new SimpleDateFormat("yyMMddHHmmss").format(new Date()) + Math.round(Math.random() * 100000)+".docx";

        Set<String> usedStyleIds = null;
        try {
            usedStyleIds = getUsedStyles(document);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 保存文件
        File docFile = new File(docxFilePath);
        FileOutputStream fos = new FileOutputStream(docFile);
        document.write(fos);
        fos.close();

        // 解压缩 .docx 文件
        String unzipDir = "unzipped" + new SimpleDateFormat("yyMMddHHmmss").format(new Date()) + Math.round(Math.random() * 100000);
        unzip(docxFilePath, unzipDir);

        // 读取并解析 styles.xml 文件
        String stylesFilePath = unzipDir + "/word/styles.xml";
        org.w3c.dom.Document stylesDoc = parseXML(stylesFilePath);

        removeStylesExcept(stylesDoc, usedStyleIds);

        replaceStylesArial(stylesDoc);

        // 保存修改后的 styles.xml 文件
        saveXML(stylesDoc, stylesFilePath);

        // 重新压缩为 .docx 文件
        //String newDocxFilePath = "C:/TEMP/fff.docx";
        zip(unzipDir, docxFilePath);

        // 清理临时文件
        deleteDirectory(Paths.get(unzipDir));

        // 读取文件
        FileInputStream inputStream = new FileInputStream(docxFilePath);
        NiceXWPFDocument retDocument = new NiceXWPFDocument(inputStream);

        // 删除临时文件
        docFile.delete();

        return retDocument;
    }

    // 似乎未考虑baseOn问题，可能会出错
    private Set<String> getUsedStyles(XWPFDocument document) {
        Set<String> usedStyles = new HashSet<>();

        // 获取所有段落的样式ID
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (paragraph.getStyleID() != null) {
                usedStyles.add(paragraph.getStyleID());
            }
        }

        // 获取所有表格单元格的样式ID
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        if (paragraph.getStyleID() != null) {
                            usedStyles.add(paragraph.getStyleID());
                        }
                    }
                }
            }
        }

        // 获取所有页眉的样式ID
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                if (paragraph.getStyleID() != null) {
                    usedStyles.add(paragraph.getStyleID());
                }
            }
        }

        // 获取所有页脚的样式ID
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                if (paragraph.getStyleID() != null) {
                    usedStyles.add(paragraph.getStyleID());
                }
            }
        }

        return usedStyles;
    }

    private void unzip(String zipFilePath, String destDir) throws IOException {
        Path destPath = Paths.get(destDir);
        if (!Files.exists(destPath)) {
            Files.createDirectories(destPath);
        }
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zipIn.getNextEntry()) != null) {
                Path filePath = destPath.resolve(entry.getName());
                if (!entry.isDirectory()) {
                    Files.createDirectories(filePath.getParent());
                    Files.copy(zipIn, filePath, StandardCopyOption.REPLACE_EXISTING);
                } else {
                    Files.createDirectories(filePath);
                }
                zipIn.closeEntry();
            }
        }
    }

    private org.w3c.dom.Document parseXML(String filePath) throws Exception {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        return dBuilder.parse(new File(filePath));
    }

    private void removeStylesExcept(org.w3c.dom.Document doc, Set<String> usedStyleIds) {
        NodeList styleNodes = doc.getElementsByTagName("w:style");
        for (int i = styleNodes.getLength() - 1; i >= 0; i--) {
            org.w3c.dom.Node styleNode = styleNodes.item(i);
            String styleId = styleNode.getAttributes().getNamedItem("w:styleId").getNodeValue();
            if (styleId != null && !usedStyleIds.contains(styleId)) {
                styleNode.getParentNode().removeChild(styleNode);
            }
        }
    }

    private void replaceStylesArial(org.w3c.dom.Document doc) {
        NodeList styleNodes = doc.getElementsByTagName("w:rFonts");
        for (int i = 0; i < styleNodes.getLength(); i++) {
            org.w3c.dom.Node styleNode = styleNodes.item(i);

            if(styleNode.getAttributes().getNamedItem("w:ascii") == null) {
                continue;
            }

            String ascii = styleNode.getAttributes().getNamedItem("w:ascii").getNodeValue();

            if("Arial".equals(ascii)) {
                styleNode.getAttributes().getNamedItem("w:ascii").setNodeValue("Arial Unicode MS");
                styleNode.getAttributes().getNamedItem("w:eastAsia").setNodeValue("Arial Unicode MS");
                styleNode.getAttributes().getNamedItem("w:hAnsi").setNodeValue("Arial Unicode MS");
                styleNode.getAttributes().getNamedItem("w:cs").setNodeValue("Arial Unicode MS");
            }
        }
    }

    private void saveXML(org.w3c.dom.Document doc, String filePath) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        DOMSource source = new DOMSource(doc);
        StreamResult result = new StreamResult(new File(filePath));
        transformer.transform(source, result);
    }

    private void zip(String sourceDirPath, String zipFilePath) throws IOException {
        Path sourceDir = Paths.get(sourceDirPath);
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFilePath))) {
            Files.walk(sourceDir).filter(path -> !Files.isDirectory(path)).forEach(path -> {
                ZipEntry zipEntry = new ZipEntry(sourceDir.relativize(path).toString().replace("\\", "/"));
                try {
                    zipOut.putNextEntry(zipEntry);
                    Files.copy(path, zipOut);
                    zipOut.closeEntry();
                } catch (IOException e) {
                    System.err.println(e);
                }
            });
        }
    }

    private void deleteDirectory(Path path) throws IOException {
        if (Files.exists(path)) {
            Files.walk(path)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }
}
