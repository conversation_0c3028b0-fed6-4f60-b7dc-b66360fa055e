package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Wzlljl;
import com.xpaas.zpbg.vo.WzlljlVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-文章浏览记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Component
public class WzlljlWrapper extends BaseEntityWrapper<Wzlljl, WzlljlVO>  {


	@Override
	public WzlljlVO entityVO(Wzlljl wzlljl) {
		WzlljlVO wzlljlVO = Objects.requireNonNull(BeanUtil.copy(wzlljl, WzlljlVO.class));
		//User cjr = UserCache.getUser(wzlljl.getCjr());
		//if (cjr != null){
		//	wzlljlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(wzlljl.getGxr());
		//if (gxr != null){
		//	wzlljlVO.setGxrName(gxr.getName());
		//}
		return wzlljlVO;
	}

    @Override
    public WzlljlVO wrapperVO(WzlljlVO wzlljlVO) {
		//User cjr = UserCache.getUser(wzlljlVO.getCjr());
		//if (cjr != null){
		//	wzlljlVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(wzlljlVO.getGxr());
		//if (gxr != null){
		//	wzlljlVO.setGxrName(gxr.getName());
		//}
        return wzlljlVO;
    }

}
