package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.vo.ZcclglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-备查材料关联包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Component
public class ZcclglWrapper extends BaseEntityWrapper<Zcclgl, ZcclglVO>  {


	@Override
	public ZcclglVO entityVO(Zcclgl zcclgl) {
		ZcclglVO zcclglVO = Objects.requireNonNull(BeanUtil.copy(zcclgl, ZcclglVO.class));
		//User cjr = UserCache.getUser(zcclgl.getCjr());
		//if (cjr != null){
		//	zcclglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zcclgl.getGxr());
		//if (gxr != null){
		//	zcclglVO.setGxrName(gxr.getName());
		//}
		return zcclglVO;
	}

    @Override
    public ZcclglVO wrapperVO(ZcclglVO zcclglVO) {
		//User cjr = UserCache.getUser(zcclglVO.getCjr());
		//if (cjr != null){
		//	zcclglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zcclglVO.getGxr());
		//if (gxr != null){
		//	zcclglVO.setGxrName(gxr.getName());
		//}
        return zcclglVO;
    }

}
