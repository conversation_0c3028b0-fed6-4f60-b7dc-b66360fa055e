package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Syzj;
import lombok.Data;

import java.util.List;


/**
 * 自评报告-状态变化操作参数类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class ZtProcInfoVO {
    private static final long serialVersionUID = 1L;

    // 报告ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgid;

    // 版本ID - 主线版本
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbid;

    // 要创建的版本名称 空的话使用内部规则
    private String bbmc;

    // 要创建的版本说明
    private String bbsm;

    // 审阅专家列表(点长提交用)
    private List<Syzj> syzjList;

    // 操作结果 保留
    private Integer procResult;

    // 版本ID - 创建版本的版本ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbidNew;
}
