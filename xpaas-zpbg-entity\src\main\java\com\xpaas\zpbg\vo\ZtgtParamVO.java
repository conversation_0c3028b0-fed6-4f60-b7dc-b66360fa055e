package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;


/**
 * 自评报告-状态甘特参数类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
public class ZtgtParamVO {
    private static final long serialVersionUID = 1L;

    // 报告ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgid;

    // 进度ID
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jdid;

    private String startDate;

    private String endDate;

}
