package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.dto.TreeData;
import com.xpaas.zpbg.entity.Mkgl;
import com.xpaas.zpbg.entity.Zygzdgl;
import com.xpaas.zpbg.vo.MkglVO;
import com.xpaas.zpbg.vo.TreeVO;

import java.util.List;

/**
 * 教学评价-自评报告-模块管理 服务类
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IMkglService extends BaseService<Mkgl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mkgl
	 * @return
	 */
	IPage<MkglVO> selectMkglPage(IPage<MkglVO> page, MkglVO mkgl, TreeVO tree);

	/**
	 * 标准体系字典取得
	 *
	 * @param mklxid
	 * @return
	 */
	List<TreeData> bztxOptions(String mklxid);

	/**
	 * 模块名称取得
	 *
	 * @param mkid
	 * @return
	 */
	String getBgmc(String mkid);

	/**
	 * 模块管理与一期标准体系同步查询
	 *
	 * @param zygzdid
	 * @return
	 */
	MkglVO getSaveMkgl(Long zygzdid);

	/**
	 * 模块管理与一期标准体系同步新增
	 *
	 * @param mkglVO
	 * @return
	 */
	boolean saveMkgl(MkglVO mkglVO);

	/**
	 * 模块管理与一期标准体系同步修改
	 *
	 * @param zygzdgl
	 * @return
	 */
	boolean updateMkgl(Zygzdgl zygzdgl);

	/**
	 * 模块管理主动同步查询
	 *
	 * @return
	 */
	List<Mkgl> getSaveMkglZd();

	/**
	 * 模块管理主动同步修改
	 *
	 * @return
	 */
	boolean updateMkglZd();

}
