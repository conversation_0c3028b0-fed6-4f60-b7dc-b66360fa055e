package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.entity.Bgmkczjl;
import com.xpaas.zpbg.vo.BgmkczjlVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-报告模块操作记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Repository
public interface BgmkczjlMapper extends BaseMapper<Bgmkczjl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmkczjl
	 * @return
	 */
	List<BgmkczjlVO> selectBgmkczjlPage(IPage page, BgmkczjlVO bgmkczjl);

}
