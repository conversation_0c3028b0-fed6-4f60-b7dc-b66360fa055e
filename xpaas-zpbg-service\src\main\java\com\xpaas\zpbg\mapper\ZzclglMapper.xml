<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZzclglMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zzclglResultMap" type="com.xpaas.zpbg.entity.Zzclgl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="ZZCLID" property="zzclid"/>
        <result column="CLMC" property="clmc"/>
        <result column="CMM" property="cmm"/>
        <result column="WJH" property="wjh"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="GLWZ" property="glwz"/>
        <result column="CFWZ" property="cfwz"/>

    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zzclglResultMapVO" type="com.xpaas.zpbg.vo.ZzclglVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="BGID" property="bgid"/>
        <result column="BGMKID" property="bgmkid"/>
        <result column="BBID" property="bbid"/>
        <result column="GLKEY" property="glkey"/>
        <result column="ZZCLID" property="zzclid"/>
        <result column="CLMC" property="clmc"/>
        <result column="CMM" property="cmm"/>
        <result column="WJH" property="wjh"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="GLWZ" property="glwz"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="XH" property="xh"/>
    </resultMap>

    <select id="selectZzclglPage" resultMap="zzclglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZZCLGL ZZCLGL inner join
            t_dt_jxpj_zpbg_bbpz bbpz
                on bbpz.BBID = ZZCLGL.BBID
                and bbpz.COMMENT_ID = ZZCLGL.GLKEY
                and bbpz.SCBJ = 0 where ZZCLGL.scbj = 0
    </select>

    <select id="getMaxPx" resultType="int">
        select
        IFNULL(Max(ZZCLGL.px), 0) AS MaxPx
        from T_DT_JXPJ_ZPBG_ZZCLGL ZZCLGL
    </select>

    <select id="getMkId" parameterType="java.lang.Long" resultMap="zzclglResultMapVO">
        select MKID as ID,MKMC as CLMC from T_DT_JXPJ_ZPBG_BGMK where scbj = 0 and id = #{bgmkid}
    </select>

    <select id="checkGlkey" resultType="int">
        SELECT
        ( SELECT count( 1 ) FROM T_DT_JXPJ_ZPBG_ZZCLGL  ZZCLGL
         INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on ZZCLGL.BBID = bbpz.BBID and ZZCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE ZZCLGL.SCBJ = 0 and ZZCLGL.BBID = #{bbid} and ZZCLGL.BGID = #{bgid} and ZZCLGL.BGMKID = #{bgmkid}
        <if test="glkeyList != null and glkeyList.size() > 0">
            <foreach collection="glkeyList" item="item" open=" AND GLKEY IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>
    <select id="getzzclList" resultMap="zzclglResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZZCLGL ZZCLGL
        INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on ZZCLGL.BBID = bbpz.BBID and ZZCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        where ZZCLGL.scbj = 0 AND ZZCLGL.bbid = #{bbid}
        <foreach collection="glkeyList" item="item" open=" AND ZZCLGL.GLKEY IN (" separator="," close=")">
            #{item}
        </foreach>
        order by ZZCLGL.cjrq
    </select>
    <select id="sameGl" resultType="com.xpaas.zpbg.vo.ZzclglVO">
        SELECT
            *
        FROM
            T_DT_JXPJ_ZPBG_ZZCLGL ZZCLGL
            INNER JOIN T_DT_JXPJ_ZPBG_BBPZ bbpz on ZZCLGL.BBID = bbpz.BBID and ZZCLGL.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE
            ZZCLGL.BGID = #{bgid}
            AND ZZCLGL.BGMKID = #{bgmkid}
            AND ZZCLGL.BBID = #{bbid}
            AND ZZCLGL.ZZCLID = #{zzclid}
            AND ZZCLGL.GLKEY != #{glkey}
            AND ZZCLGL.SCBJ = 0
    </select>
    <select id="getZzlglList" resultMap="zzclglResultMapVO">
        select t2.*
        from
        (
        select
        t1.*
        ,(select @rownum:=@rownum+1) xh
        from
        (
        SELECT
        zzgl.*,
        bbpz.px as bbpzpx,
        (select @rownum:=0) r
        from
        T_DT_JXPJ_ZPBG_ZZCLGL zzgl
        INNER JOIN  T_DT_JXPJ_ZPBG_BBPZ bbpz on zzgl.BBID = bbpz.BBID and zzgl.GLKEY = bbpz.COMMENT_ID and bbpz.SCBJ = 0
        WHERE
        zzgl.BBID =#{bbid}
        AND zzgl.BGMKID =#{bgmkid}
        AND zzgl.BGID =#{bgid}
        AND zzgl.SCBJ = 0
        )  t1
        ) t2
        <where>
            <if test="glkey != null and glkey!=''">
                AND t2.GLKEY = #{glkey}
            </if>
        </where>
        ORDER BY t2.bbpzpx,t2.px
    </select>

    <update id="updataPx">
        update T_DT_JXPJ_ZPBG_ZZCLGL set px = #{newpx} where id = #{zcid} and SCBJ = 0
    </update>
    <select id="getQuote" resultType="com.xpaas.zpbg.vo.ZzclglVO">
        SELECT
            *
        FROM
            T_DT_JXPJ_ZPBG_ZZCLGL ZZCLGL
        WHERE
            ZZCLGL.BGID = #{bgid}
            AND ZZCLGL.BGMKID = #{bgmkid}
            AND ZZCLGL.BBID = #{bbid}
            AND ZZCLGL.ZZCLID = #{zzclid}
            AND ZZCLGL.SCBJ = 0
    </select>
</mapper>
