<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.SjclssmkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sjclssmkResultMap" type="com.xpaas.zpbg.entity.Sjclssmk">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="SJCLID" property="sjclid"/>
        <result column="MKID" property="mkid"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="sjclssmkResultMapVO" type="com.xpaas.zpbg.vo.SjclssmkVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="SJCLID" property="sjclid"/>
        <result column="MKID" property="mkid"/>
    </resultMap>

    <select id="selectSjclssmkPage" resultMap="sjclssmkResultMapVO">
        select * from T_DT_JXPJ_ZPBG_SJCLSSMK where scbj = 0
    </select>

    <select id="selectSjclssmkList" resultType="com.xpaas.zpbg.vo.SjclssmkVO">
        select * from T_DT_JXPJ_ZPBG_SJCLSSMK where scbj = 0 and sjclid = #{sjclid}
    </select>

    <delete id="sjclssmkDelete">
        delete from T_DT_JXPJ_ZPBG_SJCLSSMK where sjclid = #{sjclid}
    </delete>

</mapper>
