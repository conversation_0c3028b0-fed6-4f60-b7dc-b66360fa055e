package com.xpaas.zpbg.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Gjcznjc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-关键词智能检测视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GjcznjcVO对象", description = "教学评价-自评报告-关键词智能检测")
public class GjcznjcVO extends Gjcznjc {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    /**
     * 关键词类型
     */
    @ExcelProperty("关键词类型")
    @ApiModelProperty(value = "关键词类型")
    private Integer gjclx;

    /**
     * 模块ID
     */
    @ExcelProperty("模块ID")
    @ApiModelProperty(value = "模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mkid;

}
