package com.xpaas.zpbg.service.impl;

import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.zpbg.entity.Zjjd;
import com.xpaas.zpbg.mapper.ZjjdMapper;
import com.xpaas.zpbg.service.IZjjdService;
import com.xpaas.zpbg.vo.ZjjdVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 自评报告-专家解读 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Slf4j
@Service
public class ZjjdServiceImpl extends BaseServiceImpl<ZjjdMapper, Zjjd> implements IZjjdService {

	@Override
	public List<ZjjdVO> getTree(ZjjdVO zjjdVO) {
		return getChildData(null, "0", zjjdVO);
	}

	private List<ZjjdVO> getChildData(String sjid, String jb, ZjjdVO zjjdVO) {
		List<ZjjdVO> listZjjd = new ArrayList<>();

		if(jb.equals("0")) {
			zjjdVO.setSjid(null);
			listZjjd = baseMapper.getTreeOne(zjjdVO);
			if(listZjjd != null) {
				for(int i = 0; i < listZjjd.size(); i++) {
					ZjjdVO vo = listZjjd.get(i);

					List<ZjjdVO> child = getChildData(vo.getValue(), "1", zjjdVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}

					// 没有下级，删除本级
					if(vo.getChildren() == null) {
						listZjjd.remove(i);
						i--;
					}

				}
			}
		} else if(jb.equals("1")) {
			zjjdVO.setSjid(sjid);
			listZjjd = baseMapper.getTreeTwo(zjjdVO);
			if(listZjjd != null) {
				for(int i = 0; i < listZjjd.size(); i++) {
					ZjjdVO vo = listZjjd.get(i);

					List<ZjjdVO> child = getChildData(vo.getValue(), "2", zjjdVO);
					if(child != null && child.size() > 0) {
						vo.setChildren(child);
					}

					// 没有下级，删除本级
					if(vo.getChildren() == null) {
						listZjjd.remove(i);
						i--;
					}

				}
			}
		} else if(jb.equals("2")) {
			zjjdVO.setSjid(sjid);
			listZjjd = baseMapper.getTreeThree(zjjdVO);
		} else {
			return null;
		}

		return listZjjd;
	}

}
