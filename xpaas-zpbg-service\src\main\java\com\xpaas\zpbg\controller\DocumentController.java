package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.entity.Demt;
import com.xpaas.zpbg.service.IDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 教学评价-自评报告配置管理-文档管理 控制器
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/demt")
@Api(value = "教学评价-自评报告配置管理-文档", tags = "教学评价-自评报告配置管理-文档 控制器")
public class DocumentController extends BaseController {

    private IDocumentService documentService;

    /**
     * 取得word格式的模版
     */
    @GetMapping("/exportMbWord")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "取得word格式的报告", notes = "传入document")
    @ApiLog("文档管理-取得word格式的模版")
    public void exportMbWord(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 需要参数，模版ID列表，模块类型
        documentService.exportMbWord(demt, request, response);
    }

    /**
     * 取得word文档的URL
     */
    @GetMapping("/previewMbWord")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "取得word文档的URL", notes = "传入demt")
    @ApiLog("文档管理-取得word文档的URL")
    public R previewMbWord(Demt demt) throws Exception {
        // 需要参数，模版ID列表，模块类型
        String url = documentService.previewMbWord(demt);
        return R.data(url);
    }

    /**
     * 取得word格式的报告
     */
    @GetMapping("/exportBgWord")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "导出任务及子任务数据信息", notes = "传入document")
    @ApiLog("文档管理-取得word格式的报告")
    public void exportBgWord(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 需要参数，报告ID
        documentService.exportBgWord(demt, request, response);
    }

    /**
     * 取得pdf格式的报告
     */
    @GetMapping("/exportBgPdf")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "导出任务及子任务数据信息", notes = "传入document")
    @ApiLog("文档管理-取得pdf格式的报告")
    public void exportBgPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 需要参数，报告ID
        documentService.exportBgPdf(demt, request, response);
    }

    /**
     * 取得pdf格式的模块
     */
    @GetMapping("/exportMkPdf")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "取得pdf格式的模块", notes = "传入document")
    @ApiLog("文档管理-取得pdf格式的模块")
    public void exportMkPdf(Demt demt, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 需要参数，报告ID
        documentService.exportMkPdf(demt, request, response);
    }

    /**
     * 取得pdf文档的URL
     */
    @GetMapping("/previewBgPdf")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "取得word文档的URL", notes = "传入demt")
    @ApiLog("文档管理-取得pdf文档的URL")
    public R previewBgPdf(Demt demt) throws Exception {
        // 需要参数，报告ID
        String url = documentService.previewBgPdf(demt);
        return R.data(url);
    }
}
