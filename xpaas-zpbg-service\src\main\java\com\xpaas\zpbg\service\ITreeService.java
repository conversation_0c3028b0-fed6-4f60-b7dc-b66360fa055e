package com.xpaas.zpbg.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Tree;
import com.xpaas.zpbg.vo.TreeVO;

import java.util.List;

/**
 * 自评报告-文章管理 服务类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
public interface ITreeService extends BaseService<Tree> {

	/**
	 * 板块树
	 *
	 * @return
	 */
	List<TreeVO> getMkTree(TreeVO treeVO);

	/**
	 * 报告树
	 *
	 * @return
	 */
	List<TreeVO> getBgTree(TreeVO treeVO);
}
