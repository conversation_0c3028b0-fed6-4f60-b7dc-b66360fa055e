package com.xpaas.zpbg.service.impl;

import com.xpaas.zpbg.vo.ZtProcStatus;
import com.xpaas.zpbg.vo.ZtProcType;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 教学评价-自评报告-状态管理 状态字典
 *
 * <AUTHOR>
 * @since 2024-06-14
 */

public class ZtglStatusDic {
    // 各处理后状态变化字典
    private static final Map<ZtProcType, ZtglProcResult> procResultDic = new LinkedHashMap<>();
    // 各状态可用处理字典
    private static final Map<ZtProcStatus, Map<ZtProcType, Boolean>> procValidDic = new LinkedHashMap();

    // 某处理后：新状态、是否可能为20:已延期、遇到什么操作升版次
    // 参见《5 状态变化表》【4】各状态下各事件（及动作）后状态变化、【2】典型状态变化过程
    static {
        addProcResultDic(ZtProcType.ZXKS10, ZtProcStatus.ZXZ10, true, null);
        addProcResultDic(ZtProcType.DZTJ20, ZtProcStatus.YTJ30, false, ZtProcType.ZXKS10);
        addProcResultDic(ZtProcType.SYKS30, ZtProcStatus.SYZ40, false, null);
        addProcResultDic(ZtProcType.SYTJ40, ZtProcStatus.DXG60, false, ZtProcType.DZTJ20);
        addProcResultDic(ZtProcType.SYWC50, ZtProcStatus.YWC80, false, null);
        addProcResultDic(ZtProcType.DZDG70, ZtProcStatus.YDG90, false, null);
    }

    // 各状态下哪些操作处理可用
    // 参见《5 状态变化表》【3】各状态下可用事件(及动作)
    static {
        addProcValidDic(ZtProcStatus.WKS0, ZtProcType.ZXKS10);
        addProcValidDic(ZtProcStatus.ZXZ10, ZtProcType.DZTJ20, ZtProcType.DZDG70, ZtProcType.ZXZC91, ZtProcType.ZXZC92, ZtProcType.ZXZC93);
        addProcValidDic(ZtProcStatus.YYQ20, ZtProcType.DZTJ20, ZtProcType.DZDG70, ZtProcType.ZXZC91, ZtProcType.ZXZC92, ZtProcType.ZXZC93);
        addProcValidDic(ZtProcStatus.YTJ30, ZtProcType.SYKS30, ZtProcType.DZDG70, ZtProcType.DZCH94);
        addProcValidDic(ZtProcStatus.SYZ40, ZtProcType.SYKS30, ZtProcType.SYTJ40, ZtProcType.SYWC50);
        addProcValidDic(ZtProcStatus.DXG60, ZtProcType.ZXKS10, ZtProcType.SYWC50, ZtProcType.DZDG70);
        addProcValidDic(ZtProcStatus.YWC80, ZtProcType.ZXKS10, ZtProcType.DZDG70);
        addProcValidDic(ZtProcStatus.YDG90, ZtProcType.QXDG95);
    }

    private static void addProcResultDic(ZtProcType procType, ZtProcStatus procStatus, boolean knyq, ZtProcType sbProcType) {
        ZtglProcResult result = new ZtglProcResult();
        result.procStatus = procStatus;
        result.knyq = knyq;
        result.sbProcType = sbProcType;
        procResultDic.put(procType, result);
    }

    private static void addProcValidDic(ZtProcStatus procStatus, ZtProcType... validProcTypes) {
        Map<ZtProcType, Boolean> validMap = new HashMap<>();
        if (validProcTypes != null) {
            for (ZtProcType procType : validProcTypes) {
                if (procType == null) {
                    continue;
                }
                validMap.put(procType, true);
            }
        }
        procValidDic.put(procStatus, validMap);
    }

    /**
     * 获取操作结果
     *
     * @param procType 操作类型
     * @return 操作结果
     */
    public static ZtglProcResult getProcResult(ZtProcType procType) {
        return procResultDic.get(procType);
    }

    /**
     * 校验某状态下，某操作是否可用
     *
     * @param procStatus 状态
     * @param procType   操作类型
     * @return 是否可用
     */
    public static boolean checkProcValid(ZtProcStatus procStatus, ZtProcType procType) {
        if (procType == null) {
            return false;
        }
        Map<ZtProcType, Boolean> validMap = procValidDic.get(procStatus);
        if (validMap == null) {
            return false;
        }
        return validMap.containsKey(procType);
    }
}
