package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import com.xpaas.zpbg.converter.GenericStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-专家意见实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_ZJYJ")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zjyj对象", description = "教学评价-自评报告-专家意见")
public class Zjyj extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 报告ID
	*/
	@ExcelProperty("报告ID")
	@ApiModelProperty(value = "报告ID")
	@TableField("BGID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgid;

	/**
	* 报告模块ID
	*/
	@ExcelProperty("报告模块ID")
	@ApiModelProperty(value = "报告模块ID")
	@TableField("BGMKID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bgmkid;

	/**
	* 版本ID
	*/
	@ExcelProperty("版本ID")
	@ApiModelProperty(value = "版本ID")
	@TableField("BBID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long bbid;

	/**
	* 关联KEY
	*/
	@ExcelProperty("关联KEY")
	@ApiModelProperty(value = "关联KEY")
	@TableField("GLKEY")
	private String glkey;

	/**
	 * 关联文字
	 */
	@ExcelProperty("关联文字")
	@ApiModelProperty(value = "关联文字")
	@TableField("GLWZ")
	private String glwz;

	/**
	* 意见KEY
	*/
	@ExcelProperty("意见KEY")
	@ApiModelProperty(value = "意见KEY")
	@TableField("YJKEY")
	private String yjkey;

	/**
	* 关联信息类型
	*/
	@ExcelProperty("关联信息类型")
	@ApiModelProperty(value = "关联信息类型")
	@TableField("GLCLLX")
	private Integer glcllx;

	/**
	* 关联信息ID
	*/
	@ExcelProperty("关联信息ID")
	@ApiModelProperty(value = "关联信息ID")
	@TableField("GLXXID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long glxxid;

	/**
	* 关联信息名称
	*/
	@ExcelProperty("关联信息名称")
	@ApiModelProperty(value = "关联信息名称")
	@TableField("GLXXMC")
	private String glxxmc;

	/**
	* 意见区分
	*/
	@ExcelProperty(value = "意见区分", converter = GenericStatusConverter.class)
	@ApiModelProperty(value = "意见区分")
	@TableField("YJQF")
	private Integer yjqf;

	/**
	* 材料区分
	*/
	@ExcelProperty(value = "材料区分", converter = GenericStatusConverter.class)
	@ApiModelProperty(value = "材料区分")
	@TableField("CLQF")
	private Integer clqf;

	/**
	* 意见类型
	*/
	@ExcelProperty(value = "意见类型", converter = GenericStatusConverter.class)
	@ApiModelProperty(value = "意见类型")
	@TableField("YJLX")
	private Integer yjlx;

	/**
	* 快捷标签
	*/
	@ExcelProperty("快捷标签")
	@ApiModelProperty(value = "快捷标签")
	@TableField("KJBQ")
	private String kjbq;

	/**
	* 审阅意见
	*/
	@ExcelProperty("审阅意见")
	@ApiModelProperty(value = "审阅意见")
	@TableField("PJNR")
	private String pjnr;

	/**
	* 审阅专家ID
	*/
	@ExcelProperty("审阅专家ID")
	@ApiModelProperty(value = "审阅专家ID")
	@TableField("SYZJID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long syzjid;

	/**
	* 审阅专家姓名
	*/
	@ExcelProperty("审阅专家姓名")
	@ApiModelProperty(value = "审阅专家姓名")
	@TableField("SYZJXM")
	private String syzjxm;

	/**
	* 修改说明
	*/
	@ExcelProperty("修改说明")
	@ApiModelProperty(value = "修改说明")
	@TableField("XGSM")
	private String xgsm;

	/**
	* 修改人ID
	*/
	@ExcelProperty("修改人ID")
	@ApiModelProperty(value = "修改人ID")
	@TableField("XGRID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long xgrid;

	/**
	* 修改人姓名
	*/
	@ExcelProperty("修改人姓名")
	@ApiModelProperty(value = "修改人姓名")
	@TableField("XGRXM")
	private String xgrxm;

	/**
	 * 修改状态
	 */
	@ExcelProperty("修改状态")
	@ApiModelProperty(value = "修改状态")
	@TableField("XGZT")
	private Integer xgzt;

}
