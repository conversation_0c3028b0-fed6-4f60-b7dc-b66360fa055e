<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zpbg.mapper.ZcclMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="zcclResultMap" type="com.xpaas.zpbg.entity.Zccl">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="BCSM" property="bcsm"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="YYZT" property="yyzt"/>
        <result column="YYCS" property="yycs"/>
        <result column="YYZTSTR" property="yyztstr"/>
        <result column="EDITOK" property="editOk"/>
        <result column="PC" property="pc"/>
        <result column="PCMC" property="pcmc"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="zcclResultMapVO" type="com.xpaas.zpbg.vo.ZcclVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="CLMC" property="clmc"/>
        <result column="BCSM" property="bcsm"/>
        <result column="CFWZ" property="cfwz"/>
        <result column="WJLJ" property="wjlj"/>
        <result column="PX" property="px"/>
        <result column="YYZT" property="yyzt"/>
        <result column="YYCS" property="yycs"/>
        <result column="YYZTSTR" property="yyztstr"/>
        <result column="EDITOK" property="editOk"/>
        <result column="MKID" property="mkid"/>
        <result column="PC" property="pc"/>
        <result column="PCMC" property="pcmc"/>
    </resultMap>

    <select id="selectZcclPage" resultType="com.xpaas.zpbg.vo.ZcclVO">
        SELECT
        c.*,
        jm.mkmc,
        IFNULL(jy.yycs, 0) yycs,
        IF(jy.yycs IS NULL, 0, 1) yyzt
        ,glbg.bgmc
        FROM
        t_dt_jxpj_zpbg_zccl c
        LEFT JOIN (
        -- 模块名分组
        SELECT
        c.id,
        GROUP_CONCAT(DISTINCT m.mkmc) mkmc
        FROM
        t_dt_jxpj_zpbg_zccl c
        INNER JOIN t_dt_jxpj_zpbg_zcclssmk cm ON cm.zcclid = c.id
        INNER JOIN t_dt_jxpj_zpbg_mkgl m ON m.id = cm.mkid
        WHERE
        c.scbj = 0
        AND cm.scbj = 0
        AND m.scbj = 0
        GROUP BY
        c.id
        ) jm ON jm.id = c.id
        LEFT JOIN (
        -- 引用次数
        SELECT
        c.id,
        count(1) yycs
        FROM
        t_dt_jxpj_zpbg_zccl c
        INNER JOIN t_dt_jxpj_zpbg_zcclgl cg ON cg.zcclid = c.id
        INNER JOIN t_dt_jxpj_zpbg_bbgl b ON b.id = cg.bbid
        INNER JOIN t_dt_jxpj_zpbg_bbpz p ON p.comment_id = cg.glkey AND p.bbid = cg.bbid
        INNER JOIN t_dt_jxpj_zpbg_bggl g ON g.id = b.bgid
        WHERE
        c.scbj = 0
        AND cg.scbj = 0
        AND b.scbj = 0
        AND p.scbj = 0
        AND g.scbj = 0
        AND b.bblx = 1
        GROUP BY c.id
        ) jy ON jy.id = c.id
        -- 模块所属
        <if test="(zccl.ssmkid != null and zccl.ssmkid != '') or (zccl.mklx != null and zccl.mklx != 0) or (ssmkidslist != null and ssmkidslist.size() > 0)">
            INNER JOIN (
            SELECT cm.zcclid id
            FROM
            t_dt_jxpj_zpbg_zcclssmk cm
            INNER JOIN t_dt_jxpj_zpbg_mkgl m ON m.id = cm.mkid
            WHERE
            cm.scbj = 0
            AND m.scbj = 0
            <if test="zccl.ssmkid != null and zccl.ssmkid != ''">
                AND m.id = #{zccl.ssmkid}
            </if>
            <if test="ssmkidslist != null and ssmkidslist.size() > 0">
                <foreach collection="ssmkidslist" item="item" open=" AND m.id IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zccl.mklx != null and zccl.mklx != 0">
                AND m.mklx = #{zccl.mklx}
            </if>
            GROUP BY cm.zcclid
            ) jg ON jg.id = c.id
        </if>
        -- 关联报告
        left join (
            select c.ID,GROUP_CONCAT(distinct bg.ID) bgid,GROUP_CONCAT(distinct bg.BGMC) bgmc,GROUP_CONCAT(distinct bg.ND) nd,GROUP_CONCAT(distinct bg.RWLX) rwlx
            from t_dt_jxpj_zpbg_zccl c
            inner join t_dt_jxpj_zpbg_zcclgl gl on c.ID=gl.ZCCLID
            inner join t_dt_jxpj_zpbg_bggl bg on gl.BGID=bg.ID
            where c.SCBJ=0 and gl.SCBJ=0 and bg.SCBJ=0
            group by c.ID
        ) glbg on c.ID=glbg.ID
        WHERE
        c.scbj = 0
        <if test="zccl.clmc != null and zccl.clmc != ''">
            AND c.clmc LIKE CONCAT('%', #{zccl.clmc}, '%')
        </if>
        <if test="zccl.yyzt != null and zccl.yyzt == 1">
            AND jy.id IS NOT NULL
        </if>
        <if test="zccl.yyzt != null and zccl.yyzt == 0">
            AND jy.id IS NULL
        </if>
        <if test="zccl.bgId!=null and zccl.bgId!=''">
            and find_in_set(#{zccl.bgId},glbg.bgid)
        </if>
        <if test="zccl.nd!=null and zccl.nd!=''">
            and (find_in_set(#{zccl.nd},glbg.nd) or c.nd=#{zccl.nd})
        </if>
        <if test="zccl.rwlx!=null and zccl.rwlx!=''">
            and (find_in_set(#{zccl.rwlx},glbg.rwlx) or c.rwlx=#{zccl.rwlx})
        </if>
        ORDER BY c.px ASC, c.id ASC
    </select>

    <select id="selectZcclPage_BACKUP" resultMap="zcclResultMapVO">
        select *,
        ( SELECT GROUP_CONCAT(T_DT_JXPJ_ZPBG_MKGL.MKMC) FROM T_DT_JXPJ_ZPBG_MKGL INNER JOIN T_DT_JXPJ_ZPBG_ZCCLSSMK
        ON T_DT_JXPJ_ZPBG_ZCCLSSMK.MKID = T_DT_JXPJ_ZPBG_MKGL.id WHERE T_DT_JXPJ_ZPBG_ZCCLSSMK.zcclid =
        T_DT_JXPJ_ZPBG_ZCCL.id ) AS MKMC,
        CASE
        ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZCCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZCCLGL WHERE T_DT_JXPJ_ZPBG_ZCCLGL.zcclid =
        T_DT_JXPJ_ZPBG_ZCCL.id and T_DT_JXPJ_ZPBG_ZCCLGL.scbj = 0)
        WHEN 0 THEN
        0 ELSE 1
        END AS yyzt ,
        ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZCCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZCCLGL INNER JOIN T_DT_JXPJ_ZPBG_BBGL ON
        T_DT_JXPJ_ZPBG_ZCCLGL.bbid = T_DT_JXPJ_ZPBG_BBGL.id AND T_DT_JXPJ_ZPBG_BBGL.bblx = 1 WHERE
        T_DT_JXPJ_ZPBG_ZCCLGL.zcclid =
        T_DT_JXPJ_ZPBG_ZCCL.id and T_DT_JXPJ_ZPBG_ZCCLGL.scbj = 0) AS yycs,
        ( CASE cjr WHEN #{userId} THEN 0 ELSE 1 END
        + ( SELECT COUNT( T_DT_JXPJ_ZPBG_ZCCLGL.id ) FROM T_DT_JXPJ_ZPBG_ZCCLGL WHERE T_DT_JXPJ_ZPBG_ZCCLGL.zcclid =
        T_DT_JXPJ_ZPBG_ZCCL.id )
        ) AS editOk
        from T_DT_JXPJ_ZPBG_ZCCL where scbj = 0
        <if test="zccl.clmc != null and zccl.clmc != ''">
            and clmc like concat('%', #{zccl.clmc}, '%')
        </if>
        <if test="zccl.yyzt != null and zccl.yyzt == 1">
            and ID in (select CASE
            WHEN zzclid IS NULL THEN 0
            ELSE zzclid
            END AS zzclid from T_DT_JXPJ_ZPBG_ZZCLGL where scbj = 0 )
        </if>
        <if test="zccl.yyzt != null and zccl.yyzt == 0">
            and ID NOT IN (select CASE
            WHEN zzclid IS NULL THEN 0
            ELSE zzclid
            END AS zzclid from T_DT_JXPJ_ZPBG_ZZCLGL where scbj = 0)
        </if>
        <if test="zccl.ssmkid != null ">
            and id in (select zcclid from T_DT_JXPJ_ZPBG_ZCCLSSMK where mkid = #{zccl.ssmkid} )
        </if>
        <if test="zccl.mklx != null and zccl.mklx != 0">
            and id in (select zcclid from T_DT_JXPJ_ZPBG_ZCCLSSMK where mkid in (select id from T_DT_JXPJ_ZPBG_MKGL
            where mklx = #{zccl.mklx} ) )
        </if>
        order by px asc
    </select>

    <select id="checkZccl" resultMap="zcclResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZCCL where scbj = 0
        <if test="clmc != null and clmc != ''">
            and clmc = #{clmc}
        </if>
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <delete id="deleteByZcclId" parameterType="Long">
        delete
        from T_DT_JXPJ_ZPBG_ZCCLSSMK
        where zcclid = #{zcclid}
    </delete>
    <select id="getZzclList" resultMap="zcclResultMapVO">
        SELECT DISTINCT
        zzcl.*
        FROM
        T_DT_JXPJ_ZPBG_ZCCL zzcl
        INNER JOIN t_dt_jxpj_zpbg_zcclssmk zzclssmk ON zzcl.id = zzclssmk.zcclid
        where zzcl.scbj = 0 and zzclssmk.scbj = 0
        <if test="zcclVO.clmc != null and zcclVO.clmc != ''">
            and zzcl.clmc like concat('%', #{zcclVO.clmc}, '%')
        </if>
        <if test="zcclVO.ssmkid != null">
            and zzclssmk.MKID IN (SELECT MKID FROM T_DT_JXPJ_ZPBG_BGMK WHERE id = #{zcclVO.ssmkid} )
            and zzcl.id not in (select zcclid from T_DT_JXPJ_ZPBG_ZCCLGL where BGMKID = #{zcclVO.ssmkid} and bgid =
            #{zcclVO.bgid} and bbid = #{zcclVO.bbid} and scbj = 0)
        </if>
    </select>
    <select id="getZcclId" resultMap="zcclResultMapVO">
        select * from T_DT_JXPJ_ZPBG_ZCCL where scbj = 0
        <if test="clmc != null and clmc != ''">
            and clmc = #{clmc}
        </if>
        <if test="cfwz != null and cfwz != ''">
            and cfwz = #{cfwz}
        </if>
        <if test="bcsm != null and bcsm != ''">
            and bcsm = #{bcsm}
        </if>
        <if test="wjlj != null and wjlj != ''">
            and wjlj = #{wjlj}
        </if>
    </select>

    <select id="getZygzdName" resultType="String">
        select ZBMC from t_dt_jxpj_sz_zygzdgl where scbj = 0
        <if test="list != null and list.size() > 0">
            AND id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <update id="updateZcclByPcid">
        update T_DT_JXPJ_ZPBG_ZCCL set PC = CONCAT(PC,',',#{newPcid}) where FIND_IN_SET(#{oldPcid},PC) AND NOT FIND_IN_SET(#{newPcid},PC)
    </update>

    <!-- 根据材料名称查询所有的相同名称的材料 -->
    <select id="listByClmc" resultType="com.xpaas.zpbg.vo.ZcclVO">
        select t1.ID ID,t1.CLMC,t5.ID BGID,t5.BGMC MC
        from T_DT_JXPJ_ZPBG_ZCCL t1
        left join T_DT_JXPJ_ZPBG_ZCCLGL t4 on t1.ID=t4.ZCCLID and t4.SCBJ=0
        left join T_DT_JXPJ_ZPBG_BGGL t5 on t4.BGID=t5.ID and t5.SCBJ=0
        where t1.SCBJ=0
        and t1.CLMC=#{cl.clmc}
        and (
            t1.RWLX=#{cl.rwlx} or exists(
                select *
                from T_DT_JXPJ_ZPBG_ZCCLGL t2
                left join T_DT_JXPJ_ZPBG_BGGL t3 on t2.BGID=t3.ID and t3.SCBJ=0
                where t1.ID=t2.ZCCLID and t2.SCBJ=0 and t3.SCBJ=0 and t3.RWLX=#{cl.rwlx}
            )
        )
        group by t1.ID,t5.ID
    </select>
</mapper>
