package com.xpaas.zpbg.service;

import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Home;
import com.xpaas.zpbg.vo.HomeVO;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-评建跟踪-任务 服务类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
public interface IHomeService extends BaseService<Home> {

	/**
	 * 获取最大角色
	 * @param roleIds
	 * @param bgid
	 * @param jdid
	 * @param bgmkid
	 * @return
	 */
	int getRoleFlag(String roleIds, String bgid, String jdid, String bgmkid);

	/**
	 * 获取是专家角色还是普通角色
	 * @param roleIds
	 * @return
	 */
	int getRoleFlagZj(String roleIds);

	/**
	 * 获取是否有点长或者撰写人角色
	 * @return
	 */
	Map<String, Object> getDzAndZxrRole(String bgId,String jdId);

	/**
	 * 获取教评管理员报告列表
	 * @param home
	 * @return
	 */
	List<HomeVO> getBgJpList(HomeVO home);

	/**
	 * 获取点长或教评管理员报告列表
	 * @param home
	 * @return
	 */
	List<HomeVO> getBgDzAndZxrList(HomeVO home);

	/**
	 * 获取报告列表 工作台使用 专家、点长、撰写人
	 */
	List<HomeVO> getBgZjAndDzAndZxrList(HomeVO home);

	/**
	 * 教评管理员统计用的所有的模块列表
	 * @param homeVO
	 * @return
	 */
	HomeVO getSymkInfo(HomeVO homeVO);

	/**
	 * 获取专家报告列表
	 * @param home
	 * @return
	 */
	List<HomeVO> getBgZjList(HomeVO home);

	/**
	 * 获取当前时间
	 * @return
	 */
	HomeVO getNowTime();

	/**
	 * 获取进度下拉列表
	 * @param homeVO
	 * @return
	 */
	List<HomeVO> getJdList(HomeVO homeVO);

	/**
	 * 获取专家模块列表
	 * @param homeVO
	 * @return
	 */
	List<HomeVO> getBgmkZjList(HomeVO homeVO);

	/**
	 * 获取点长或教评管理员模块列表
	 * @param homeVO
	 * @return
	 */
	List<HomeVO> getBgmkDzAndZxrList(HomeVO homeVO);

	/**
	 * 获取进度信息
	 * @param home
	 * @return
	 * @throws ParseException
	 */
	Map<String, Object> getJdInfo(HomeVO home) throws ParseException;

	/**
	 * 各单位进度统计
	 * @param homeVO
	 * @return
	 */
	List<Map<String, Object>> getGdwjdtjList(HomeVO homeVO);

	/**
	 * 专家审阅情况统计
	 * @param homeVO
	 * @return
	 */
	List<Map<String, Object>> getZjsyqkList(HomeVO homeVO);

	/**
	 * 各模块审阅频次统计
	 * @param homeVO
	 * @return
	 */
	List<Map<String, Object>> getGmksypcList(HomeVO homeVO);

	/**
	 * 各一级指标情况统计
	 * @param homeVO
	 * @return
	 */
	List<Map<String, Object>> getGyjzbqktjList(HomeVO homeVO);

	/**
	 * 总览信息统计
	 * @param homeVO
	 * @return
	 */
	Map<String, Object> getZlxxtjInfo(HomeVO homeVO);

	/**
	 * 工作台报告数量
	 */
	Map<String, Object> gztbgsl(HomeVO homeVO);
}
