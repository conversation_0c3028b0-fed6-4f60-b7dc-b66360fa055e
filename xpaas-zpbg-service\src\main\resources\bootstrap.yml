spring:
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_IP:***************:8848}
        namespace: ${NNAME_SPACE:jxpj-dev}
      discovery:
        server-addr: ${NACOS_IP:***************:8848}
        namespace: ${DNAME_SPACE:jxpj-dev}
        #ip: ************
  profiles: dev
---
spring:
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_IP:***************:8848}
        namespace: ${NNAME_SPACE:jxpj-test}
      discovery:
        server-addr:  ${NACOS_IP:***************:8848}
        namespace: ${DNAME_SPACE:jxpj-test}
  profiles: test
---
spring:
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_IP:***************:8848}
        namespace: ${NNAME_SPACE:jishuzhongtai-prod}
      discovery:
        server-addr:  ${NACOS_IP:***************:8848}
        namespace: ${DNAME_SPACE:jishuzhongtai-prod}
  profiles: prod
---

