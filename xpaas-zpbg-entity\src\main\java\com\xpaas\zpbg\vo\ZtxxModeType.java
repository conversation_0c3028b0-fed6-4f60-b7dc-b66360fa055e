package com.xpaas.zpbg.vo;

public enum ZtxxModeType {
    write("write"), // 1次撰写
    edit("edit"), // n次撰写
    view("view"), // 查看
    audit("audit"); // 审阅

    private final String value;

    ZtxxModeType(String value) {
        this.value = value;
    }

    public static ZtxxModeType fromValue(String value) {
        for (ZtxxModeType mode : ZtxxModeType.values()) {
            if (mode.toString().equals(value)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("非法枚举值：" + value);
    }

    @Override
    public String toString() {
        return this.value;
    }
}
