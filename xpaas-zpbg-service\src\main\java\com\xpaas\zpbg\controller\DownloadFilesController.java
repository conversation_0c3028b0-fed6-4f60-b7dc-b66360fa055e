package com.xpaas.zpbg.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.tool.api.R;
import com.xpaas.zpbg.service.IDownloadFilesService;
import com.xpaas.zpbg.vo.DownloadFilesVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-自评报告-佐证材料 控制器
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/downloadFile")
@Api(value = "自评报告-文件下载", tags = "自评报告-文件下载接口")
public class DownloadFilesController extends BaseController {
    private IDownloadFilesService downloadFilesService;

    /**
     * 查询的所有数据
     */
    @GetMapping("/allList")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "所有", notes = "传入downloadFilesVO")
    @ApiLog("获取下载文件URL列表")
    public R<List<Map<String, Object>>> allList(DownloadFilesVO downloadFilesVO) {
        List<Map<String, Object>> list = downloadFilesService.getFileUrlList(downloadFilesVO);
        return R.data(list);
    }
}
