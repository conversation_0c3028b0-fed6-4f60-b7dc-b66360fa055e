package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Gjcznjc;
import com.xpaas.zpbg.vo.GjcznjcVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-关键词智能检测包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Component
public class GjcznjcWrapper extends BaseEntityWrapper<Gjcznjc, GjcznjcVO>  {


	@Override
	public GjcznjcVO entityVO(Gjcznjc gjcznjc) {
		GjcznjcVO gjcznjcVO = Objects.requireNonNull(BeanUtil.copy(gjcznjc, GjcznjcVO.class));
		//User cjr = UserCache.getUser(gjcznjc.getCjr());
		//if (cjr != null){
		//	gjcznjcVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcznjc.getGxr());
		//if (gxr != null){
		//	gjcznjcVO.setGxrName(gxr.getName());
		//}
		return gjcznjcVO;
	}

    @Override
    public GjcznjcVO wrapperVO(GjcznjcVO gjcznjcVO) {
		//User cjr = UserCache.getUser(gjcznjcVO.getCjr());
		//if (cjr != null){
		//	gjcznjcVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcznjcVO.getGxr());
		//if (gxr != null){
		//	gjcznjcVO.setGxrName(gxr.getName());
		//}
        return gjcznjcVO;
    }

}
