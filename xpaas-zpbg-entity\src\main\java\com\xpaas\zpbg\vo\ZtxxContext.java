package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.*;

import java.util.Date;
import java.util.List;

/**
 * 教学评价-自评报告-状态信息 数据缓存
 *
 * <AUTHOR>
 * @since 2024-06-14
 */

public class ZtxxContext {
    // 入参信息
    @JsonSerialize(using = ToStringSerializer.class)
    public Long bbid = null;

    // 操作者信息
    @JsonSerialize(using = ToStringSerializer.class)
    public Long userId = null;
    public String userName = null;

    // 版本信息
    public Bbgl bbgl = null;
    public ZtProcStatus procStatus = ZtProcStatus.WKS0; // 版本状态
    public boolean mainBb = false; // 主线版本
    public boolean mainActiveBb = false; // 主版本活跃中(当前进度模块关联)
    public boolean zjBb = false; // 所属为专家

    // 报告信息
    @JsonSerialize(using = ToStringSerializer.class)
    public Long bgid = null;
    public Bggl bggl = null;

    // 报告模块信息
    @JsonSerialize(using = ToStringSerializer.class)
    public Long bgmkid = null;
    public Bgmk bgmk = null;

    // 进度信息
    @JsonSerialize(using = ToStringSerializer.class)
    public Long jdglid = null; //进度管理ID
    public Jdgl jdgl = null;

    // 进度详情
    public Date kssj = null;
    public Date zxjssj = null;
    public Date syjssj = null;

    // 报告模块进度关联
    public List<Jdmkgl> jdmkglList = null; // 报告模块进度关联列表

    // 分工信息
    public List<Rwfg> rwfgList = null; // 人员分工列表

    // 专家信息
    public List<Syzj> syzjList = null; // 审阅专家列表
    public Syzj syzj = null; // 本身审阅信息
    public boolean bzjSy = false; // 本专家审阅且处于审阅阶段
    public boolean bzjSyz = false; // 本专家审阅中

    // 操作记录
    public List<Bgmkztjl> ztjlList = null;
    public int tjcs = 0; // 提交次数
}
