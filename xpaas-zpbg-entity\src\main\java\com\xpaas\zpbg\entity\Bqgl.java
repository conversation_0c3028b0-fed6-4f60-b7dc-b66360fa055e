package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-自评报告-标签管理实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@TableName("T_DT_JXPJ_ZPBG_BQGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bqgl对象", description = "教学评价-自评报告-标签管理")
public class Bqgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 快捷标签
	*/
	@ExcelProperty("快捷标签")
	@ApiModelProperty(value = "快捷标签")
	@TableField("KJBQ")
	private String kjbq;

	/**
	* 标签类型
	*/
	@ExcelProperty("标签类型")
	@ApiModelProperty(value = "标签类型")
	@TableField("BQLX")
	private Integer bqlx;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	@TableField("PX")
	private Integer px;

	/**
	* 添加人
	*/
	@ExcelProperty("添加人")
	@ApiModelProperty(value = "添加人")
	@TableField("TJR")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long tjr;

	/**
	* 添加人姓名
	*/
	@ExcelProperty("添加人姓名")
	@ApiModelProperty(value = "添加人姓名")
	@TableField("TJRXM")
	private String tjrxm;



}
