package com.xpaas.zpbg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.log.annotation.ApiLog;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zpbg.dto.RwryDTO;
import com.xpaas.zpbg.dto.SelDTO;
import com.xpaas.zpbg.entity.Bggl;
import com.xpaas.zpbg.entity.Rwfg;
import com.xpaas.zpbg.service.IRwfgService;
import com.xpaas.zpbg.vo.BaseDeleteVO;
import com.xpaas.zpbg.vo.RwfgVO;
import com.xpaas.zpbg.vo.RwryVO;
import com.xpaas.zpbg.wrapper.RwfgWrapper;
import com.xpaas.zpbg.wrapper.RwryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
/**
 * 教学评价-自评报告-任务分工 控制器
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/rwfg")
@Api(value = "教学评价-自评报告-任务分工", tags = "教学评价-自评报告-任务分工接口")
public class RwfgController extends BaseController {

	private RwfgWrapper rwfgWrapper;
	private RwryWrapper rwryWrapper;
	private IRwfgService rwfgService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入rwfg")
	@ApiLog("任务分工-详情")
	public R<RwfgVO> detail(Rwfg rwfg) {
		Rwfg detail = rwfgService.getOne(Condition.getQueryWrapper(rwfg));
		return R.data(rwfgWrapper.entityVO(detail));
	}

	/**
	 * 分页 教学评价-自评报告-任务分工 (优先使用search接口)
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入rwfg")
	@ApiLog("任务分工-分页")
	public R<IPage<RwfgVO>> list(Rwfg rwfg, Query query) {
		IPage<Rwfg> pages = rwfgService.page(Condition.getPage(query), Condition.getQueryWrapper(rwfg));
		return R.data(rwfgWrapper.pageVO(pages));
	}

    /**
     * 自定义分页 教学评价-自评报告-任务分工
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "自定义分页", notes = "传入rwfg")
	@ApiLog("任务分工-自定义分页")
    public R<IPage<RwfgVO>> page(RwfgVO rwfg, Query query) {
        IPage<RwfgVO> pages = rwfgService.selectRwfgPage(Condition.getPage(query), rwfg);
        return R.data(rwfgWrapper.wrapperPageVO(pages));
    }



	/**
	 * 新增 教学评价-自评报告-任务分工
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入rwfg")
	@ApiLog("任务分工-新增")
	public R save(@Valid @RequestBody RwfgVO rwfgVO) {
		boolean b = rwfgService.save(rwfgVO);

		return R.status(b);
	}

	/**
	 * 修改 教学评价-自评报告-任务分工
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入rwfg")
	@ApiLog("任务分工-修改")
	public R update(@Valid @RequestBody RwfgVO rwfgVO) {
		boolean b = rwfgService.updateById(rwfgVO);
		return R.status(b);
	}

	/**
	 * 新增或修改 教学评价-自评报告-任务分工 (优先使用save或update接口)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入rwfg")
	@ApiLog("任务分工-新增或修改")
	public R submit(@Valid @RequestBody Rwfg rwfg) {
		return R.status(rwfgService.saveOrUpdate(rwfg));
	}


	/**
	 * 删除 教学评价-自评报告-任务分工
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@ApiLog("任务分工-逻辑删除")
	public R remove(@RequestBody BaseDeleteVO deleteVO) {
		boolean b = rwfgService.deleteLogic(Func.toLongList(deleteVO.getIds()));
		return R.status(b);
	}


	/**
	 * 高级查询
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	@ApiLog("任务分工-高级查询")
	public R<IPage<RwfgVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Rwfg> queryWrapper = Condition.getQueryWrapper(map, Rwfg.class);
		IPage<Rwfg> pages = rwfgService.page(Condition.getPage(query), queryWrapper);
		return R.data(rwfgWrapper.pageVO(pages));
	}

	/**
	 * 导出Excel
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@ApiLog("任务分工-导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<RwryDTO> queryWrapper = Condition.getQueryWrapper(map, RwryDTO.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("bgmkid", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<RwryDTO> rwryDTOS = rwfgService.selectMkryByWrapper(queryWrapper);

		Map<Long, List<RwryDTO>> rwryByBgmkid = rwryDTOS.stream().filter(rwryDto -> rwryDto.getBgmkid() != null)
				.collect(Collectors.groupingBy(RwryDTO::getBgmkid));
		List<RwryVO> rwryList = new ArrayList<>();

		// 打印分类结果
		rwryByBgmkid.forEach((bgmkid, rwryDTOList) -> {
			System.out.println(bgmkid + ": " + rwryDTOList.size());
			RwryVO rwryVO = new RwryVO();
			StringBuffer fzr = new StringBuffer();
			StringBuffer zxr = new StringBuffer();
			StringBuffer zj = new StringBuffer();
//			List<SelDTO> fzrData = new ArrayList<>();
//			List<SelDTO> zxrData = new ArrayList<>();
			rwryVO.setId(bgmkid);
			rwryVO.setBgid(rwryDTOList.get(0).getBgid());
			rwryVO.setBgmc(rwryDTOList.get(0).getBgmc());
			rwryVO.setNd(rwryDTOList.get(0).getNd());
			rwryVO.setBgmkid(rwryDTOList.get(0).getBgmkid());
			rwryVO.setMkid(rwryDTOList.get(0).getMkid());
			rwryVO.setMkmc(rwryDTOList.get(0).getMkmc());
			rwryVO.setPx(rwryDTOList.get(0).getPx());
			for(RwryDTO rwryDto : rwryDTOList){
//				SelDTO fzrSelDTO = new SelDTO();
//				SelDTO zxrSelDTO = new SelDTO();
				if(Integer.valueOf(1).equals(rwryDto.getFglx())){
//					fzrSelDTO.setId(rwryDto.getId().toString());
//					fzrSelDTO.setDwryid(rwryDto.getDwid() + "," + rwryDto.getRyid());
//					fzrSelDTO.setLabel(rwryDto.getDwmc() + "," + rwryDto.getRyxm());
//					fzrSelDTO.setValue(rwryDto.getId().toString());
					fzr.append(rwryDto.getDwmc() + "," + rwryDto.getRyxm() + "|");
//					fzrData.add(fzrSelDTO);
				}else if(Integer.valueOf(2).equals(rwryDto.getFglx())){
//					zxrSelDTO.setId(rwryDto.getId().toString());
//					zxrSelDTO.setDwryid(rwryDto.getDwid() + "," + rwryDto.getRyid());
//					zxrSelDTO.setLabel(rwryDto.getDwmc() + "," + rwryDto.getRyxm());
//					zxrSelDTO.setValue(rwryDto.getId().toString());
					zxr.append(rwryDto.getDwmc() + "," + rwryDto.getRyxm() + "|");
//					zxrData.add(zxrSelDTO);
				}else if(Integer.valueOf(3).equals(rwryDto.getFglx())){
					zj.append(rwryDto.getDwmc() + "," + rwryDto.getRyxm() + "|");
				}

			}
			if (fzr.length() > 0) {
				fzr.setLength(fzr.length() - 1); // 去掉最后一个字符
			}
			if (zxr.length() > 0) {
				zxr.setLength(zxr.length() - 1); // 去掉最后一个字符
			}
			if (zj.length() > 0) {
				zj.setLength(zj.length() - 1); // 去掉最后一个字符
			}
			rwryVO.setFzr(fzr.toString());
//			rwryVO.setFzrData(fzrData);
			rwryVO.setZxr(zxr.toString());
//			rwryVO.setZxrData(zxrData);
			rwryVO.setZj(zj.toString());
			System.out.println(rwryVO.toString());
			rwryList.add(rwryVO);
		});

		// 排序
		rwryList.sort((RwryVO rwryVO1, RwryVO rwryVO2) -> {
			if(rwryVO1.getPx() < rwryVO2.getPx()){
				return -1;
			}else if(rwryVO1.getPx() > rwryVO2.getPx()){
				return 1;
			} else {
				if(rwryVO1.getMkid().longValue() < rwryVO2.getMkid().longValue()){
					return 1;
				} else if(rwryVO1.getMkid().longValue() > rwryVO2.getMkid().longValue()){
					return -1;
				} else{
					return 0;
				}
			}
		});

		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, rwryList, RwryVO.class);
	}


	/**
	 * 导入Excel
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	@ApiLog("任务分工-导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Rwfg> list = ExcelUtil.read(file, Rwfg.class);
		//TODO 此处需要根据具体业务添加代码
		rwfgService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	@ApiLog("任务分工-下载导入模板")
	public void template(HttpServletResponse response) {
		QueryWrapper<Rwfg> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Rwfg> list = rwfgService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		//TODO 此处需要根据具体业务添加代码
		//columnFiledNames.add("id");
		//columnFiledNames.add("cjrq");
		ExcelUtil.export(response, "Rwfg导入模板", "Rwfg导入模板",columnFiledNames, list, Rwfg.class);
	}

	/**
	 * 模块分工人员 教学评价-自评报告-任务分工
	 */
	@GetMapping("/mkryList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "模块分工人员", notes = "传入bggl")
	@ApiLog("任务分工-模块分工人员")
	public R<IPage<RwryVO>> mkryList(Bggl bggl, Query query) {
//		List<RwryDTO> rwryDTOS = rwfgService.selectMkry(Condition.getPage(query),bggl);
		List<RwryDTO> rwryDTOS = rwfgService.selectMkry(bggl);
		Map<Long, List<RwryDTO>> rwryByBgmkid = rwryDTOS.stream().filter(rwryDto -> rwryDto.getBgmkid() != null)
				.collect(Collectors.groupingBy(RwryDTO::getBgmkid,TreeMap::new,Collectors.toList()));
		List<RwryVO> rwryList = new ArrayList<>();

		rwryByBgmkid.entrySet()
				.stream()
				.sorted(Comparator.comparing(entry ->
					entry.getValue().get(0).getPx()
				)).forEach(entry -> {
			RwryVO rwryVO = new RwryVO();
			StringBuffer fzr = new StringBuffer();
			StringBuffer zxr = new StringBuffer();
			StringBuffer zj = new StringBuffer();
			List<SelDTO> fzrData = new ArrayList<>();
			List<SelDTO> zxrData = new ArrayList<>();
			List<SelDTO> zjData = new ArrayList<>();
			rwryVO.setId(entry.getKey());
			rwryVO.setBgid(entry.getValue().get(0).getBgid());
			rwryVO.setBgmc(entry.getValue().get(0).getBgmc());
			rwryVO.setNd(entry.getValue().get(0).getNd());
			rwryVO.setKssj(entry.getValue().get(0).getKssj());
			rwryVO.setBgmkid(entry.getValue().get(0).getBgmkid());
			rwryVO.setMkid(entry.getValue().get(0).getMkid());
			rwryVO.setMkmc(entry.getValue().get(0).getMkmc());
			rwryVO.setPx(entry.getValue().get(0).getPx());
			for(RwryDTO rwryDto : entry.getValue()){
				SelDTO fzrSelDTO = new SelDTO();
				SelDTO zxrSelDTO = new SelDTO();
				SelDTO zjSelDTO = new SelDTO();
				if(Integer.valueOf(1).equals(rwryDto.getFglx())){
					fzrSelDTO.setId(rwryDto.getRyid().toString());
					fzrSelDTO.setDwryid(rwryDto.getDwid() + "," + rwryDto.getRyid());
					fzrSelDTO.setLabel(rwryDto.getDwmc() + "," + rwryDto.getRyxm());
					fzr.append(rwryDto.getRyid() + ",");
					fzrData.add(fzrSelDTO);
				}else if(Integer.valueOf(2).equals(rwryDto.getFglx())){
					zxrSelDTO.setId(rwryDto.getRyid().toString());
					zxrSelDTO.setDwryid(rwryDto.getDwid() + "," + rwryDto.getRyid());
					zxrSelDTO.setLabel(rwryDto.getDwmc() + "," + rwryDto.getRyxm());
					zxr.append(rwryDto.getRyid() + ",");
					zxrData.add(zxrSelDTO);
				}else if(Integer.valueOf(3).equals(rwryDto.getFglx())){
					zjSelDTO.setId(rwryDto.getRyid().toString());
					zjSelDTO.setDwryid(rwryDto.getDwid() + "," + rwryDto.getRyid());
					zjSelDTO.setLabel(rwryDto.getDwmc() + "," + rwryDto.getRyxm());
					zj.append(rwryDto.getRyid() + ",");
					zjData.add(zjSelDTO);
				}

			}
			if (fzr.length() > 0) {
				fzr.setLength(fzr.length() - 1); // 去掉最后一个字符
			}
			if (zxr.length() > 0) {
				zxr.setLength(zxr.length() - 1); // 去掉最后一个字符
			}
			if (zj.length() > 0) {
				zj.setLength(zj.length() - 1); // 去掉最后一个字符
			}
			rwryVO.setFzr(fzr.toString());
			rwryVO.setFzrData(fzrData);
			rwryVO.setZxr(zxr.toString());
			rwryVO.setZxrData(zxrData);
			rwryVO.setZj(zj.toString());
			rwryVO.setZjData(zjData);
			System.out.println(rwryVO.toString());
			rwryList.add(rwryVO);
		});

		// 排序
		rwryList.sort((RwryVO rwryVO1, RwryVO rwryVO2) -> {
			if(rwryVO1.getPx() < rwryVO2.getPx()){
				return -1;
			}else if(rwryVO1.getPx() > rwryVO2.getPx()){
				return 1;
			} else {
				if(rwryVO1.getMkid().longValue() < rwryVO2.getMkid().longValue()){
					return 1;
				} else if(rwryVO1.getMkid().longValue() > rwryVO2.getMkid().longValue()){
					return -1;
				} else{
					return 0;
				}
			}
		});

		IPage<RwryVO> page = Condition.getPage(query);

		int pageNum = (int) page.getCurrent();
		int pageSize = (int) page.getSize();


		List<RwryVO> pageList = new ArrayList<>();
		int curIdx = pageNum > 1 ? (pageNum - 1) * pageSize : 0;
		for (int i = 0; i < pageSize && curIdx + i < rwryList.size(); i++) {
			pageList.add(rwryList.get(curIdx + i));
		}

		page.setRecords(pageList);
		page.setTotal(rwryList.size());
		int pages = (int)Math.ceil((double)rwryList.size()/page.getSize());
		page.setPages(pages);

		return R.data(rwryWrapper.wrapperPageVO(page));
	}



	/**
	 * 提交教学评价-自评报告-任务分工
	 */
	@PostMapping("/submitArray")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "提交", notes = "传入rwfg")
	@ApiLog("任务分工-提交")
	public R submitArray(@Valid @RequestBody List<Rwfg> rwfgList) {
		return R.status(rwfgService.deleteAndSave(rwfgList));
	}

    /**
     * 获取当前模块未提交撰写人
     */
    @GetMapping("/getZxr")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "查询", notes = "传入bgid和bgmkid")
    @ApiLog("任务分工-获取当前模块未提交撰写人")
    public R getZxr(Long bgid, Long bgmkid) {
        LambdaQueryWrapper<Rwfg> wrapper = new LambdaQueryWrapper<Rwfg>();
        wrapper
                .eq(Rwfg::getBgid, bgid)
                .eq(Rwfg::getBgmkid, bgmkid)
                .eq(Rwfg::getClzt, 1)
                .eq(Rwfg::getFglx, 2)
                .ne(Rwfg::getRyid, this.getUser().getUserId());

        List<Rwfg> rwfgList = rwfgService.list(wrapper);
        List<String> nameList = new ArrayList<>();
        rwfgList.stream().forEach(item -> nameList.add(item.getRyxm()));
        return R.data(nameList);
    }

	/**
	 * 提交教学评价-自评报告-任务分工-演示报告导入撰写人
	 */
	@PostMapping("/zxrImportSave")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "提交", notes = "传入rwfg")
	@ApiLog("任务分工-演示报告导入撰写人")
	public R zxrImportSave(@Valid @RequestBody Rwfg rwfg) {
		return R.status(rwfgService.zxrImportSave(rwfg));
	}

}
