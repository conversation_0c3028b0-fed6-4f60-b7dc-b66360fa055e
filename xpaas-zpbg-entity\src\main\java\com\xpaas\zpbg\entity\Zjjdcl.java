package com.xpaas.zpbg.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教学评价-自评报告-专家解读材料实体类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@TableName("T_DT_JXPJ_ZSKGL_BZTXGL_ZJJDCL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zjjdcl对象", description = "教学评价-自评报告-专家解读材料")
public class Zjjdcl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 材料名称
	*/
	@ExcelProperty("材料名称")
	@ApiModelProperty(value = "材料名称")
	@TableField("CLMC")
	private String clmc;

	/**
	* 上传人
	*/
	@ExcelProperty("上传人")
	@ApiModelProperty(value = "上传人")
	@TableField("SCR")
	private String scr;

	/**
	* 专家
	*/
	@ExcelProperty("专家")
	@ApiModelProperty(value = "专家")
	@TableField("ZJ")
	private String zj;

	/**
	* 材料类型
	*/
	@ExcelProperty("材料类型")
	@ApiModelProperty(value = "材料类型")
	@TableField("CLLX")
	private String cllx;

	/**
	* 上传日期
	*/
	@ExcelProperty("上传日期")
	@ApiModelProperty(value = "上传日期")
	@TableField("SCRQ")
	private Date scrq;

	/**
	* 是否允许下载 0:允许下载 1:不允许下载
	*/
	@ExcelProperty("是否允许下载 0:允许下载 1:不允许下载")
	@ApiModelProperty(value = "是否允许下载 0:允许下载 1:不允许下载")
	@TableField("SFYXXZ")
	private String sfyxxz;

	/**
	* 置顶 0:置顶 1:未置顶
	*/
	@ExcelProperty("置顶 0:置顶 1:未置顶")
	@ApiModelProperty(value = "置顶 0:置顶 1:未置顶")
	@TableField("ZD")
	private String zd;

	/**
	 * 置顶时间
	 */
	@ExcelProperty("置顶时间")
	@ApiModelProperty(value = "置顶时间")
	@TableField("ZDSJ")
	private Date zdsj;

	/**
	* 上传附件
	*/
	@ExcelProperty("上传附件")
	@ApiModelProperty(value = "上传附件")
	@TableField("SCFJ")
	private String scfj;

	/**
	* 标准体系评价主表ID
	*/
	@ExcelProperty("标准体系评价主表ID")
	@ApiModelProperty(value = "标准体系评价主表ID")
	@TableField("PARENTID")
	private String parentid;

	@TableField(exist = false)
	private String flag;

}
