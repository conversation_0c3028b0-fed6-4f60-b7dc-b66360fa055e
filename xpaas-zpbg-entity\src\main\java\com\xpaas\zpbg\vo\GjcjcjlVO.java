package com.xpaas.zpbg.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xpaas.zpbg.entity.Gjcjcjl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 教学评价-自评报告-关键词检测记录视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GjcjcjlVO对象", description = "教学评价-自评报告-关键词检测记录")
public class GjcjcjlVO extends Gjcjcjl {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "报告名称")
    private String bgmc;

    @ApiModelProperty(value = "进度名称")
    private String jdmc;

    @ApiModelProperty("版本ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bbid;

    @ApiModelProperty(value = "文件内容")
    private String wjnr;

    @ApiModelProperty("模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mkid;

    @ApiModelProperty("报告模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bgmkid;
}
