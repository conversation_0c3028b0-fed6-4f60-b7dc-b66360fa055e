package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Zccl;
import com.xpaas.zpbg.vo.ZcclVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-备查材料包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Component
public class ZcclWrapper extends BaseEntityWrapper<Zccl, ZcclVO>  {


	@Override
	public ZcclVO entityVO(Zccl zccl) {
		ZcclVO zcclVO = Objects.requireNonNull(BeanUtil.copy(zccl, ZcclVO.class));
		//User cjr = UserCache.getUser(zccl.getCjr());
		//if (cjr != null){
		//	zcclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zccl.getGxr());
		//if (gxr != null){
		//	zcclVO.setGxrName(gxr.getName());
		//}
		return zcclVO;
	}

    @Override
    public ZcclVO wrapperVO(ZcclVO zcclVO) {
		//User cjr = UserCache.getUser(zcclVO.getCjr());
		//if (cjr != null){
		//	zcclVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(zcclVO.getGxr());
		//if (gxr != null){
		//	zcclVO.setGxrName(gxr.getName());
		//}
        return zcclVO;
    }

}
