package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.utils.CopyHelper;
import com.xpaas.zlkgl.vo.ZlglVO;
import com.xpaas.zlkgl.mapper.ZlglMapper;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

/**
 * 教学评价-资料库平台-资料管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZlglServiceImpl extends BaseServiceImpl<ZlglMapper, Zlgl> implements IZlglService {

	private final CopyHelper copyHelper;

	@Override
	public IPage<ZlglVO> selectZlglPage(IPage<ZlglVO> page, ZlglVO zlgl) {
		return page.setRecords(baseMapper.selectZlglPage(page, zlgl));
	}

	/**
	 * 复制文件
	 *
	 * @param sourceId 源文件ID
	 * @param targetFolderId 目标文件夹ID
	 * @param newName 新名称（可选，为空时自动生成"副本"名称）
	 * @return 复制是否成功
	 */
	@Override
	public boolean copyFile(String sourceId, String targetFolderId, String newName) {
		try {
			// 1. 查询源文件
			Zlgl sourceFile = this.getById(sourceId);
			if (sourceFile == null) {
				log.error("源文件不存在，ID: {}", sourceId);
				return false;
			}

			// 2. 校验自定义名称
			if (newName != null) {
				String validationError = copyHelper.validateName(newName);
				if (validationError != null) {
					log.error("文件名校验失败: {}", validationError);
					return false;
				}
			}

			// 3. 创建新文件
			Zlgl newFile = createNewFile(sourceFile, targetFolderId, newName);

			// 4. 保存新文件
			return this.save(newFile);
		} catch (Exception e) {
			log.error("复制文件失败，sourceId: {}, targetFolderId: {}, newName: {}", sourceId, targetFolderId, newName, e);
			return false;
		}
	}

	/**
	 * 创建新文件实体
	 *
	 * @param sourceFile 源文件
	 * @param targetFolderId 目标文件夹ID
	 * @param newName 新名称
	 * @return 新文件实体
	 */
	private Zlgl createNewFile(Zlgl sourceFile, String targetFolderId, String newName) {
		Zlgl newFile = new Zlgl();
		copyFileProperties(sourceFile, newFile);
		newFile.setId(null); // 让数据库自动生成新ID
		newFile.setWjjId(targetFolderId);

		// 设置名称
		String finalName = newName != null ? newName.trim() :
			copyHelper.generateUniqueName(sourceFile.getZlMc(), targetFolderId,
				name -> isFileNameExists(name, targetFolderId));
		newFile.setZlMc(finalName);
		newFile.setZlCmm(finalName);

		return newFile;
	}



	/**
	 * 检查文件名是否已存在
	 *
	 * @param fileName 文件名
	 * @param folderId 文件夹ID
	 * @return 是否存在
	 */
	private boolean isFileNameExists(String fileName, String folderId) {
		return this.count(new LambdaQueryWrapper<Zlgl>()
			.eq(Zlgl::getWjjId, folderId)
			.eq(Zlgl::getZlMc, fileName)) > 0;
	}

	/**
	 * 复制文件属性
	 *
	 * @param source 源文件
	 * @param target 目标文件
	 */
	private void copyFileProperties(Zlgl source, Zlgl target) {
		target.setZlMc(source.getZlMc());
		target.setZlCmm(source.getZlCmm());
		target.setZlPx(source.getZlPx());
		target.setZlDz(source.getZlDz());
		target.setZlMj(source.getZlMj());
		target.setWxzlLx(source.getWxzlLx());
		target.setPjLx(source.getPjLx());
	}

}
