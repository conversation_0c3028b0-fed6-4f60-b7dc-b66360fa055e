package com.xpaas.zlkgl.service.impl;

import com.xpaas.zlkgl.dto.CopyResult;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;
import com.xpaas.zlkgl.mapper.ZlglMapper;
import com.xpaas.zlkgl.service.ICopyService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

/**
 * 教学评价-资料库平台-资料管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZlglServiceImpl extends BaseServiceImpl<ZlglMapper, Zlgl> implements IZlglService {

	private final ICopyService copyService;

	@Override
	public IPage<ZlglVO> selectZlglPage(IPage<ZlglVO> page, ZlglVO zlgl) {
		return page.setRecords(baseMapper.selectZlglPage(page, zlgl));
	}

	@Override
	public CopyResult copyFile(String sourceId, String targetParentId, String newName) {
		return copyService.copyFile(sourceId, targetParentId, newName);
	}

}
