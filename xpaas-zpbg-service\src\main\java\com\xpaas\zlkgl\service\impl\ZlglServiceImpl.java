package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;
import com.xpaas.zlkgl.mapper.ZlglMapper;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

/**
 * 教学评价-资料库平台-资料管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
public class ZlglServiceImpl extends BaseServiceImpl<ZlglMapper, Zlgl> implements IZlglService {

	@Override
	public IPage<ZlglVO> selectZlglPage(IPage<ZlglVO> page, ZlglVO zlgl) {
		return page.setRecords(baseMapper.selectZlglPage(page, zlgl));
	}

	/**
	 * 复制文件
	 *
	 * @param sourceId 源文件ID
	 * @param targetFolderId 目标文件夹ID
	 * @param newName 新名称（可选，为空时自动生成"副本"名称）
	 * @return 复制是否成功
	 */
	@Override
	public boolean copyFile(String sourceId, String targetFolderId, String newName) {
		try {
			// 1. 查询源文件
			Zlgl sourceFile = this.getById(sourceId);
			if (sourceFile == null) {
				log.error("源文件不存在，ID: {}", sourceId);
				return false;
			}

			// 2. 生成新名称
			String finalName = StringUtil.isBlank(newName) ?
				generateUniqueName(sourceFile.getZlMc(), targetFolderId) : newName;

			// 3. 创建新文件记录
			Zlgl newFile = new Zlgl();
			copyFileProperties(sourceFile, newFile);
			newFile.setId(null); // 让数据库自动生成新ID
			newFile.setWjjId(targetFolderId);
			newFile.setZlMc(finalName);
			newFile.setZlCmm(finalName); // 重命名也设置为新名称

			// 4. 保存新文件
			return this.save(newFile);
		} catch (Exception e) {
			log.error("复制文件失败，sourceId: {}, targetFolderId: {}, newName: {}", sourceId, targetFolderId, newName, e);
			return false;
		}
	}

	/**
	 * 生成唯一文件名（处理命名冲突）
	 *
	 * @param baseName 基础名称
	 * @param folderId 文件夹ID
	 * @return 唯一名称
	 */
	private String generateUniqueName(String baseName, String folderId) {
		String candidateName = baseName + "副本";
		int counter = 1;

		while (isFileNameExists(candidateName, folderId)) {
			candidateName = baseName + "副本(" + counter + ")";
			counter++;
		}

		return candidateName;
	}

	/**
	 * 检查文件名是否已存在
	 *
	 * @param fileName 文件名
	 * @param folderId 文件夹ID
	 * @return 是否存在
	 */
	private boolean isFileNameExists(String fileName, String folderId) {
		return this.count(new LambdaQueryWrapper<Zlgl>()
			.eq(Zlgl::getWjjId, folderId)
			.eq(Zlgl::getZlMc, fileName)) > 0;
	}

	/**
	 * 复制文件属性
	 *
	 * @param source 源文件
	 * @param target 目标文件
	 */
	private void copyFileProperties(Zlgl source, Zlgl target) {
		target.setZlMc(source.getZlMc());
		target.setZlCmm(source.getZlCmm());
		target.setZlPx(source.getZlPx());
		target.setZlDz(source.getZlDz());
		target.setZlMj(source.getZlMj());
		target.setWxzlLx(source.getWxzlLx());
		target.setPjLx(source.getPjLx());
	}

}
