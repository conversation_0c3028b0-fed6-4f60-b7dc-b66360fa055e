package com.xpaas.zpbg.common.utils;

import java.text.DecimalFormat;

/**
 * 工具类
 * <AUTHOR>
 * @Date 2019/12/16 10:48
 */
public class IntegerUtils {
    /**
     * 计算差值
     *
     * @param bigNum
     * @param smallNum
     * @author: luzhaojun
     * @time: 2022-10-10 10:10:47
     */
    public static Integer getNum(Integer bigNum,Integer smallNum){
        Integer num = null;
        if (bigNum>smallNum){
            num=bigNum-smallNum;
        }else if (bigNum<smallNum){
            num = smallNum-bigNum;
        }else {
            num = 0;
        }
        return num;
    }

    /***
     * 整数相除 保留一位小数
     * @param a
     * @param b
     * @return
     */
    public static String division(int a ,int b){
        String result = "";
        float num =(float)a/b*100;

        DecimalFormat df = new DecimalFormat("0");

        result = df.format(num);

        return result;

    }
    


}
