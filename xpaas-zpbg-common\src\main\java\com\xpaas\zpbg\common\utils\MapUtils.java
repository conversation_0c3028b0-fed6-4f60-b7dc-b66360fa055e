package com.xpaas.zpbg.common.utils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class MapUtils {
    /**
     * entity 转 map
     * @param obj
     * @return
     */
    public static Map<String,Object> EntityToMap(Object obj){
        Field[] fields = obj.getClass().getDeclaredFields();
        Map<String,Object> map = new HashMap<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (Field f : fields) {
                f.setAccessible(true);
                Object val = f.get(obj);
                if(EmptyUtils.isNotEmpty(val)) {
                    if (f.getType() == Date.class) {
                        map.put(f.getName(), sdf.format(val));
                    } else {
                        map.put(f.getName(), val);
                    }
                }else{
                    map.put(f.getName(), " ");
                }
            }
        } catch (IllegalArgumentException | IllegalAccessException e) {
            e.printStackTrace();
        }

        return map;
    }
}
