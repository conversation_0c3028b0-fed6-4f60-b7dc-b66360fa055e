package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.zpbg.entity.Demt;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.entity.Zzclgl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 自评报告-文档 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Repository
public interface DocumentMapper extends BaseMapper<Demt> {

    /**
     * 取得模块文件列表
     *
     * @param demt
     * @return
     */
    List<Demt> getMkList(Demt demt);

    /**
     * 取得报告模块
     *
     * @param demt
     * @return
     */
    List<Demt> getBgmkList(Demt demt);

    /**
     * 取得佐证材料
     *
     * @param demt
     * @return
     */
    List<Zzclgl> getZzclList(Demt demt);

    /**
     * 取得备查材料
     *
     * @param demt
     * @return
     */
    List<Zcclgl> getZcclList(Demt demt);


    /**
     * 取得封面路径
     *
     * @param demt
     * @return
     */
    String getFmlj(Demt demt);
}
