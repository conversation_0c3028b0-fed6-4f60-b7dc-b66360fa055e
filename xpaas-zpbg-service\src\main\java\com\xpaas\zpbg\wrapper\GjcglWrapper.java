package com.xpaas.zpbg.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zpbg.entity.Gjcgl;
import com.xpaas.zpbg.vo.GjcglVO;
import org.springframework.stereotype.Component;

import java.util.Objects;
/**
 * 教学评价-自评报告-关键词管理包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Component
public class GjcglWrapper extends BaseEntityWrapper<Gjcgl, GjcglVO>  {


	@Override
	public GjcglVO entityVO(Gjcgl gjcgl) {
		GjcglVO gjcglVO = Objects.requireNonNull(BeanUtil.copy(gjcgl, GjcglVO.class));
		//User cjr = UserCache.getUser(gjcgl.getCjr());
		//if (cjr != null){
		//	gjcglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcgl.getGxr());
		//if (gxr != null){
		//	gjcglVO.setGxrName(gxr.getName());
		//}
		return gjcglVO;
	}

    @Override
    public GjcglVO wrapperVO(GjcglVO gjcglVO) {
		//User cjr = UserCache.getUser(gjcglVO.getCjr());
		//if (cjr != null){
		//	gjcglVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(gjcglVO.getGxr());
		//if (gxr != null){
		//	gjcglVO.setGxrName(gxr.getName());
		//}
        return gjcglVO;
    }

}
