package com.xpaas.zlkgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.vo.WxzlWjjVO;

import java.util.List;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface IWxzlWjjService extends BaseService<WxzlWjj> {

    /**
     * 自定义分页
     *
     * @param page
     * @param wxzlWjj 教学评价-资料库平台-外校资料文件夹树表 实体
     * @return
     * <AUTHOR>
     * @since 2025-07-25
     */
    IPage<WxzlWjjVO> selectWxzlWjjPage(IPage<WxzlWjjVO> page, WxzlWjjVO wxzlWjj);

    /**
     * 查询树结构
     */
    List<WxzlWjjVO> listTree(WxzlWjjVO bo);

    /**
     * 新增
     */
    boolean insert(WxzlWjjVO vo);

    /**
     * 修改
     */
    boolean update(WxzlWjjVO vo);

    /**
     * 删除
     */
    boolean deleteByIds(List<Long> ids);

    /**
     * 复制外校文件夹
     *
     * @param sourceId 源文件夹ID
     * @param targetParentId 目标父文件夹ID
     * @param newName 新名称（可选）
     * @return 复制结果
     */
    com.xpaas.zlkgl.dto.CopyResult copyFolder(String sourceId, String targetParentId, String newName);

}
