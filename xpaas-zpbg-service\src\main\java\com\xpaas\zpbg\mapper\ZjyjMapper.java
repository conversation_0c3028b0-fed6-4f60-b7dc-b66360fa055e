package com.xpaas.zpbg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zpbg.dto.ZjyjExcelDTO;
import com.xpaas.zpbg.entity.Zjyj;
import com.xpaas.zpbg.vo.ZjyjVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教学评价-自评报告-专家意见 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Repository
public interface ZjyjMapper extends BaseMapper<Zjyj> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zjyj
	 * @return
	 */
	List<ZjyjVO> selectZjyjPage(IPage page, ZjyjVO zjyj);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zjyj
	 * @return
	 */
	List<ZjyjVO> selectOtherzjPage(IPage page, ZjyjVO zjyj);

	/**
	 * 导出意见
	 *
	 * @param bgid
	 * @return
	 */
	List<ZjyjExcelDTO> getZjyjExcel (String bgid, String bbid,String bgmkid);

	/**
	 * 批注用
	 *
	 * @param bbid
	 * @param glkeyList
	 * @return
	 */
	List<ZjyjVO> getzjyjList(String bbid,List<String> glkeyList);

	/**
	 * 获取审阅意见个数
	 *
	 * @param zjyjvo
	 * @return
	 */
	int getZjyjCount(ZjyjVO zjyjvo);

	/**
	 * 获取未修改意见的个数
	 *
	 * @param bgid
	 * @param bbid
	 * param bgmkid
	 * @return
	 */
	int getyjChecking (String bgid, String bbid,String bgmkid);

	/**
	 * 获取上一版本意见
	 *
	 * @param bgid
	 * @param bbid
	 * param bgmkid
	 * param syzjid
	 * @return
	 */
	List<Zjyj> getLsyj(String bgid, String bbid,String bgmkid,String syzjid);
}
