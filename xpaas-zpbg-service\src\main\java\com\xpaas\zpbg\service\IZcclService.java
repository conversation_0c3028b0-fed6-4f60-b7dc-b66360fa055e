package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Zccl;
import com.xpaas.zpbg.entity.Zcclgl;
import com.xpaas.zpbg.vo.ZcclVO;
import com.xpaas.zpbg.vo.ZcclglVO;

import java.util.List;

/**
 * 教学评价-自评报告-备查材料 服务类
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface IZcclService extends BaseService<Zccl> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zccl
	 * @return
	 */
	IPage<ZcclVO> selectZcclPage(IPage<ZcclVO> page, ZcclVO zccl);

	Boolean checkZccl(ZcclVO zccl);

	List<ZcclVO> getZcclId(ZcclVO zccl);

	Boolean saveZZCLSSMK(ZcclVO zcclVO);

	ZcclVO getZcclDetail(Zccl zccl);

	IPage<ZcclVO> getZzclList(ZcclglVO zcclVO, IPage<ZcclVO> page);

	void requestZcZjjyApi(Long clid,String wjlj,String name,String wjmc,int updataOrSave);

	List<ZcclglVO> getQuote(Zcclgl zcclgl);

	/**
	 * 根据老的批次名称和新的批次名称更改备查材料的批次信息
	 * @param oldPcid
	 * @param newPcid
	 */
	void updateZcclByPcid(Long oldPcid,Long newPcid);

	/**
	 * 根据材料名称查询所有的相同名称的材料
	 */
	List<ZcclVO> listByClmc(Zccl zccl);
}
