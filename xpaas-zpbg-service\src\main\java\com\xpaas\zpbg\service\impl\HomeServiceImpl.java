package com.xpaas.zpbg.service.impl;

import com.google.common.base.Joiner;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.system.entity.Dept;
import com.xpaas.system.feign.IDeptClient;
import com.xpaas.zpbg.entity.Home;
import com.xpaas.zpbg.entity.Jdgl;
import com.xpaas.zpbg.mapper.HomeMapper;
import com.xpaas.zpbg.service.IHomeService;
import com.xpaas.zpbg.service.IJdglService;
import com.xpaas.zpbg.utils.MapConvertUtils;
import com.xpaas.zpbg.vo.HomeVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自评报告-首页 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Slf4j
@Service
public class HomeServiceImpl extends BaseServiceImpl<HomeMapper, Home> implements IHomeService {

    @Autowired
    IJdglService jdglService;

    @Autowired
    IDeptClient deptClient;

    //院首长角色ID
    @Value("${xpaas.roles.pjgzYszRoleId}")
    private String[] pjgzYszRoleId;

    // # 自评报告撰写平台-教评中心管理员-角色ID
    @Value("${xpaas.roles.zpbgzxptGlyRoleId}")
    private String[] zpbgzxptGlyRoleId;

    // # 自评报告撰写平台-专家-角色ID
    @Value("${xpaas.roles.zpbgzxptZjRoleId}")
    private String[] zpbgzxptZjRoleId;

    public static String[] mainArray = {"院首长", "办公室", "教务处", "教保处", "科研学术处",
            "政治工作处", "纪检监察处", "安全管理处", "供应保障处", "职业教育中心",
            "教学考评中心", "教研保障中心", "服务保障中心", "警通勤务连", "基础部",
            "作战指挥系", "航海观通系", "战略导弹与水中兵器系", "动力操纵系", "防险救生系",
            "接改装训练大队", "潜艇兵训练基地", "学员一大队", "学员二大队", "学员三大队",
            "外训大队"};

    /**
     * 获取教评管理员报告列表
     * @param home
     * @return
     */
    @Override
    public List<HomeVO> getBgJpList(HomeVO home) {
        return baseMapper.getBgJpList(home);
    }

    /**
     * 获取专家报告列表
     * @param home
     * @return
     */
    @Override
    public List<HomeVO> getBgZjList(HomeVO home) {

        LoginUser user = AuthUtil.getUser();
        String userId = user.getUserId().toString();
        home.setUserId(userId);

        return baseMapper.getBgZjList(home);
    }

    /**
     * 获取当前时间
     * @return
     */
    @Override
    public HomeVO getNowTime() {
        return baseMapper.getNowTime();
    }

    /**
     * 获取点长或教评管理员报告列表
     * @param home
     * @return
     */
    @Override
    public List<HomeVO> getBgDzAndZxrList(HomeVO home) {
        if(Func.isBlank(home.getUserId())){
            LoginUser user = AuthUtil.getUser();
            String userId = user.getUserId().toString();
            home.setUserId(userId);
        }
        return baseMapper.getBgDzAndZxrList(home);
    }

    /**
     * 获取报告列表 工作台使用 专家、点长、撰写人
     */
    @Override
    public List<HomeVO> getBgZjAndDzAndZxrList(HomeVO home){
        if(Func.isBlank(home.getUserId())){
            LoginUser user = AuthUtil.getUser();
            String userId = user.getUserId().toString();
            home.setUserId(userId);
        }
        return baseMapper.getBgZjAndDzAndZxrList(home);
    }

    /**
     * 获取专家模块列表
     * @param homeVO
     * @return
     */
    @Override
    public List<HomeVO> getBgmkZjList(HomeVO homeVO) {

        LoginUser user = AuthUtil.getUser();
        String userId = user.getUserId().toString();
        homeVO.setUserId(userId);

        return baseMapper.getBgmkZjList(homeVO);
    }

    /**
     * 获取点长或教评管理员模块列表
     * @param homeVO
     * @return
     */
    @Override
    public List<HomeVO> getBgmkDzAndZxrList(HomeVO homeVO) {

        LoginUser user = AuthUtil.getUser();
        String userId = user.getUserId().toString();
        homeVO.setUserId(userId);

        return baseMapper.getBgmkDzAndZxrList(homeVO);
    }

    /**
     * 教评管理员统计用的所有的模块列表
     * @param homeVO
     * @return
     */
    @Override
    public HomeVO getSymkInfo(HomeVO homeVO) {
        HomeVO retVo = new HomeVO();

        List<Map<String, Object>> mkList = baseMapper.getSymkList(homeVO);

        List<Map<String, Object>> rwfgList = baseMapper.getSymkRwfgList(homeVO);

        List<Map<String, Object>> retRwfgList = new ArrayList<>();

        if(rwfgList != null && rwfgList.size() > 0){

            List<String> deptSortTemp = new ArrayList<>();

            List<String> dwlist = rwfgList.stream().map(item -> String.valueOf(item.get("dwid"))).collect(Collectors.toList());

            List<Dept> deptList = new ArrayList<>();
            if(dwlist != null && dwlist.size() > 0){
                // 取得二级单位liest
                R<List<Dept>> deptR = deptClient.getSecondDeptList(Joiner.on(",").join(dwlist));

                if (deptR.getData() != null){
                    deptList = deptR.getData();
                }
            }

            if(deptList != null && deptList.size() > 0){
                for(int k = 0; k < rwfgList.size(); k++) {

                    Map<String, Object>  rwfgMap = rwfgList.get(k);
                    // 转换成二级单位
                    // 看看查询二级部门用不用做缓存
                    String dwid = String.valueOf(rwfgMap.get("dwid"));

                    // 业务上只会找到1
                    List<Dept> findDept = deptList.stream().filter(item -> dwid.equals(item.getRemark())).collect(Collectors.toList());

                    if(findDept != null && findDept.size() > 0){
                        rwfgMap.put("ejdwmc", findDept.get(0).getDeptName());
                    }else{
                        rwfgMap.put("ejdwmc", null);
                    }

                    // 如果二级单位为空 也就是可能是一级单位不统计
                    String ejdwmc = "";
                    if(rwfgMap.get("ejdwmc") != null && StringUtils.isNotBlank(String.valueOf(rwfgMap.get("ejdwmc")))){
                        ejdwmc = String.valueOf(rwfgMap.get("ejdwmc"));
                        rwfgMap.put("ejdwmc", ejdwmc);
                        retRwfgList.add(rwfgMap);

                        // 如果是一级单位，单位统计的时候不应该计入这个分工，如果其他总览统计应该计算这个模块，那应该是前端判断，饼图是否计数
                        if(!deptSortTemp.contains(ejdwmc)){
                            deptSortTemp.add(ejdwmc);
                        }

                    }else{
                        // 可能是一级单位不统计
                    }

                }

                for (Map<String, Object> rwfgMap : retRwfgList) {

                    String ejdwmc = "";
                    if(rwfgMap.get("ejdwmc") != null && StringUtils.isNotBlank(String.valueOf(rwfgMap.get("ejdwmc")))){
                        ejdwmc = String.valueOf(rwfgMap.get("ejdwmc"));
                    }
                    // 二级单位排序
                    rwfgMap.put("dwmcSort", getMainDeptSortIndex(ejdwmc, deptSortTemp));
                }
            }

        }

        retRwfgList = retRwfgList.stream().sorted(Comparator.comparing((Map<String, Object> h) -> h.get("bxmcSort")==null? -99999 : (Integer)h.get("bxmcSort"))).collect(Collectors.toList());

        MapConvertUtils.convertLongToString(mkList);
        MapConvertUtils.convertLongToString(retRwfgList);

        retVo.setSymkList(mkList);
        retVo.setSymkRwfgList(retRwfgList);

        return retVo;
    }

    /**
     * 获取进度下拉列表
     * @param homeVO
     * @return
     */
    @Override
    public List<HomeVO> getJdList(HomeVO homeVO) {

        return baseMapper.getJdList(homeVO);
    }

    /**
     * 各单位进度统计
     * @param homeVO
     * @return
     */
    @Override
    public List<Map<String, Object>> getGdwjdtjList(HomeVO homeVO) {
        List<Map<String, Object>> list = getMainDeptList(homeVO);
        return list;
    }

    /**
     * 专家审阅情况统计
     * @param homeVO
     * @return
     */
    @Override
    public List<Map<String, Object>> getZjsyqkList(HomeVO homeVO) {
        List<Map<String, Object>> list = baseMapper.getZjsyqkList(homeVO);

        boolean flag30 = false;
        boolean flag40 = false;
        boolean flag60 = false;
        boolean flag80 = false;

        if(list != null && list.size() > 0){
            for (Map<String, Object> ztMap : list) {

                if((Integer) ztMap.get("bgmkzt") == 30){
                    flag30 = true;
                }

                if((Integer) ztMap.get("bgmkzt") == 40){
                    flag40 = true;
                }

                if((Integer) ztMap.get("bgmkzt") == 60){
                    flag60 = true;
                }

                if((Integer) ztMap.get("bgmkzt") == 80){
                    flag80 = true;
                }

            }
        }

        if(!flag30){
            Map<String, Object> ztMapTemp = new HashMap<>();
            ztMapTemp.put("bgmkztmc", "待审阅");
            ztMapTemp.put("bgmkzt", 30);
            ztMapTemp.put("cnt", 0);
            list.add(ztMapTemp);
        }

        if(!flag40){
            Map<String, Object> ztMapTemp = new HashMap<>();
            ztMapTemp.put("bgmkztmc", "审阅中");
            ztMapTemp.put("bgmkzt", 40);
            ztMapTemp.put("cnt", 0);
            list.add(ztMapTemp);
        }

        if(!flag60){
            Map<String, Object> ztMapTemp = new HashMap<>();
            ztMapTemp.put("bgmkztmc", "已审阅");
            ztMapTemp.put("bgmkzt", 60);
            ztMapTemp.put("cnt", 0);
            list.add(ztMapTemp);
        }

        if(!flag80){
            Map<String, Object> ztMapTemp = new HashMap<>();
            ztMapTemp.put("bgmkztmc", "已完成");
            ztMapTemp.put("bgmkzt", 80);
            ztMapTemp.put("cnt", 0);
            list.add(ztMapTemp);
        }

        List<Map<String, Object>> ztListNew = list.stream().sorted(Comparator.comparing(item -> (Integer)item.get("bgmkzt"))).collect(Collectors.toList());

        return ztListNew;
    }

    /**
     * 各模块审阅频次统计
     * @param homeVO
     * @return
     */
    @Override
    public List<Map<String, Object>> getGmksypcList(HomeVO homeVO) {
        List<Map<String, Object>> list = baseMapper.getGmksypcList(homeVO);
        return list;
    }

    /**
     * 各一级指标情况统计
     * @param homeVO
     * @return
     */
    @Override
    public List<Map<String, Object>> getGyjzbqktjList(HomeVO homeVO) {
        List<Map<String, Object>> retFList = new ArrayList<Map<String, Object>>();
        LinkedHashMap<String, List<Map<String, Object>>> zbListMap = new LinkedHashMap<>();

        List<LinkedHashMap<String, Object>> gyjzbqktjList = baseMapper.getGyjzbqktjList(homeVO);

        if(gyjzbqktjList != null && gyjzbqktjList.size() > 0){
            zbListMap = gyjzbqktjList.stream().collect(Collectors.groupingBy(item -> String.valueOf(item.get("yjzb")), LinkedHashMap::new, Collectors.toList()));
        }else{
            return null;
        }

        if(zbListMap != null && zbListMap.size() > 0){
            for (Map.Entry<String, List<Map<String, Object>>> entry : zbListMap.entrySet()) {

                List<Map<String, Object>> ztList = entry.getValue();

                boolean flag0 = false;
                boolean flag10 = false;
                boolean flag20 = false;
                boolean flag30 = false;
//                boolean flag40 = false;
//                boolean flag60 = false;
                boolean flag80 = false;
                boolean flag90 = false;

                if(ztList != null && ztList.size() > 0){
                    for(int i = 0; i < ztList.size(); i++){
                        Map<String, Object> ztMap = ztList.get(i);

                        if((Integer) ztMap.get("bgmkzt") == 0){
                            flag0 = true;
                        }

                        if((Integer) ztMap.get("bgmkzt") == 10){
                            flag10 = true;
                        }

                        if((Integer) ztMap.get("bgmkzt") == 20){
                            flag20 = true;
                        }

                        if((Integer) ztMap.get("bgmkzt") == 30){
                            flag30 = true;
                        }

//                        if((Integer) ztMap.get("bgmkzt") == 40){
//                            flag40 = true;
//                        }
//
//                        if((Integer) ztMap.get("bgmkzt") == 60){
//                            flag60 = true;
//                        }

                        if((Integer) ztMap.get("bgmkzt") == 80){
                            flag80 = true;
                        }

                        if((Integer) ztMap.get("bgmkzt") == 90){
                            flag90 = true;
                        }

                    }

                    Map<String, Object> ztMap = ztList.get(0);
                    if(!flag0){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 0);
                        ztMapTemp.put("bgmkztmc", "未开始");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

                    if(!flag10){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 10);
                        ztMapTemp.put("bgmkztmc", "撰写中");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

                    if(!flag20){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 20);
                        ztMapTemp.put("bgmkztmc", "撰写延期");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

                    if(!flag30){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 30);
                        ztMapTemp.put("bgmkztmc", "专家审阅");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

//                    if(!flag40){
//                        Map<String, Object> ztMapTemp = new HashMap<>();
//                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
//                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
//                        ztMapTemp.put("bgmkzt", 40);
//                        ztMapTemp.put("bgmkztmc", "审阅中");
//                        ztMapTemp.put("cnt", 0);
//                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
//                        ztList.add(ztMapTemp);
//                    }
//
//                    if(!flag60){
//                        Map<String, Object> ztMapTemp = new HashMap<>();
//                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
//                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
//                        ztMapTemp.put("bgmkzt", 60);
//                        ztMapTemp.put("bgmkztmc", "已审阅");
//                        ztMapTemp.put("cnt", 0);
//                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
//                        ztList.add(ztMapTemp);
//                    }

                    if(!flag80){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 80);
                        ztMapTemp.put("bgmkztmc", "已完成");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

                    if(!flag90){
                        Map<String, Object> ztMapTemp = new HashMap<>();
                        ztMapTemp.put("yjzbid", ztMap.get("yjzbid"));
                        ztMapTemp.put("yjzb", ztMap.get("yjzb"));
                        ztMapTemp.put("bgmkzt", 90);
                        ztMapTemp.put("bgmkztmc", "已提交");
                        ztMapTemp.put("cnt", 0);
                        ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                        ztList.add(ztMapTemp);
                    }

                    List<Map<String, Object>> ztListNew = ztList.stream().sorted(Comparator.comparing(item -> (Integer)item.get("bgmkzt"))).collect(Collectors.toList());

                    entry.setValue(ztListNew);
                }

                Map mapRet = new HashMap<>();
                mapRet.put("yjzb", entry.getKey());
                mapRet.put("ztList", entry.getValue());
                retFList.add(mapRet);
            }
        }

        return retFList;
    }

    /**
     * 总览信息统计
     * @param homeVO
     * @return
     */
    @Override
    public Map<String, Object> getZlxxtjInfo(HomeVO homeVO) {
        Map<String, Object> map = baseMapper.getZlxxtjInfo(homeVO);
        return map;
    }

    /**
     * 二级部门 列表显示用 获取排序位置
     * @param bxmc
     * @return
     */
    private int getMainDeptSortIndex(String bxmc, List<String> deptSortTemp) {

        for(int i = 0; i < mainArray.length; i++) {
            if(mainArray[i].equals(bxmc)) {
                return i + 1;
            }
        }

        for(int j = 0; j < deptSortTemp.size(); j++) {
            if(deptSortTemp.get(j).equals(bxmc)){
                return mainArray.length + j + 1;
            }
        }

        return mainArray.length + deptSortTemp.size();

    }

    /**
     * 二级部门排序
     * @param homeVO
     * @return
     */
    private List<Map<String, Object>> getMainDeptList(HomeVO homeVO) {

        List<Map<String, Object>> retFList = new ArrayList<Map<String, Object>>();
        List<Map.Entry<String, List<Map<String, Object>>>> retList = new ArrayList<Map.Entry<String, List<Map<String, Object>>>>();

        List<Map<String, Object>> gdwjdtjList = baseMapper.getGdwjdtjList(homeVO);
        List<Map.Entry<String, List<Map<String, Object>>>> deptList = new ArrayList<Map.Entry<String, List<Map<String, Object>>>>();
        Map<String, List<Map<String, Object>>> deptListMap = new HashMap<>();

        if(gdwjdtjList != null && gdwjdtjList.size() > 0){
            deptListMap = gdwjdtjList.stream().collect(Collectors.groupingBy(item -> String.valueOf(item.get("dwmc"))));
        }else{
            return null;
        }

        for (Map.Entry<String, List<Map<String, Object>>> entry : deptListMap.entrySet()) {

            List<Map<String, Object>> ztList = entry.getValue();

            boolean flag0 = false;
            boolean flag10 = false;
            boolean flag20 = false;
            boolean flag30 = false;
//            boolean flag40 = false;
//            boolean flag60 = false;
            boolean flag80 = false;
            boolean flag90 = false;

            if(ztList != null && ztList.size() > 0){
                for(int i = 0; i < ztList.size(); i++){
                    Map<String, Object> ztMap = ztList.get(i);

                    if((Long) ztMap.get("bgmkzt") == 0L){
                        flag0 = true;
                    }

                    if((Long) ztMap.get("bgmkzt") == 10L){
                        flag10 = true;
                    }

                    if((Long) ztMap.get("bgmkzt") == 20L){
                        flag20 = true;
                    }

                    if((Long) ztMap.get("bgmkzt") == 30L){
                        flag30 = true;
                    }

//                    if((Long) ztMap.get("bgmkzt") == 40L){
//                        flag40 = true;
//                    }

//                    if((Long) ztMap.get("bgmkzt") == 60L){
//                        flag60 = true;
//                    }

                    if((Long) ztMap.get("bgmkzt") == 80L){
                        flag80 = true;
                    }

                    if((Long) ztMap.get("bgmkzt") == 90L){
                        flag90 = true;
                    }

                }

                Map<String, Object> ztMap = ztList.get(0);
                if(!flag0){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 0L);
                    ztMapTemp.put("bgmkztmc", "未开始");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

                if(!flag10){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 10L);
                    ztMapTemp.put("bgmkztmc", "撰写中");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

                if(!flag20){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 20L);
                    ztMapTemp.put("bgmkztmc", "撰写延期");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

                if(!flag30){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 30L);
                    ztMapTemp.put("bgmkztmc", "专家审阅");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

//                if(!flag40){
//                    Map<String, Object> ztMapTemp = new HashMap<>();
//                    ztMapTemp.put("dwid", ztMap.get("dwid"));
//                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
//                    ztMapTemp.put("bgmkzt", 40L);
//                    ztMapTemp.put("bgmkztmc", "审阅中");
//                    ztMapTemp.put("cnt", 0);
//                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
//                    ztList.add(ztMapTemp);
//                }
//
//                if(!flag60){
//                    Map<String, Object> ztMapTemp = new HashMap<>();
//                    ztMapTemp.put("dwid", ztMap.get("dwid"));
//                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
//                    ztMapTemp.put("bgmkzt", 60L);
//                    ztMapTemp.put("bgmkztmc", "已审阅");
//                    ztMapTemp.put("cnt", 0);
//                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
//                    ztList.add(ztMapTemp);
//                }

                if(!flag80){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 80L);
                    ztMapTemp.put("bgmkztmc", "已完成");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

                if(!flag90){
                    Map<String, Object> ztMapTemp = new HashMap<>();
                    ztMapTemp.put("dwid", ztMap.get("dwid"));
                    ztMapTemp.put("dwmc", ztMap.get("dwmc"));
                    ztMapTemp.put("bgmkzt", 90L);
                    ztMapTemp.put("bgmkztmc", "已提交");
                    ztMapTemp.put("cnt", 0);
                    ztMapTemp.put("totalCnt", ztMap.get("totalCnt"));
                    ztList.add(ztMapTemp);
                }

                List<Map<String, Object>> ztListNew = ztList.stream().sorted(Comparator.comparing(item -> (Long)item.get("bgmkzt"))).collect(Collectors.toList());

                entry.setValue(ztListNew);
            }

            deptList.add(entry);

        }

        // 按顺序找到部门并加入
        for(int i = 0; i < mainArray.length; i++) {
            for(int k = 0; k < deptList.size(); k++) {

                Map.Entry<String, List<Map<String, Object>>> deptMap = deptList.get(k);

                if(mainArray[i].equals(String.valueOf(deptMap.getKey()))) {
                    retList.add(deptList.get(k));
                    deptList.remove(k);
                    break;
                }
            }
        }

        // 加入剩余的部门
        if(deptList.size() > 0) {
            retList.addAll(deptList);
        }

        if(retList != null && retList.size() > 0){
            for(int i = 0; i < retList.size(); i++){
                Map.Entry<String, List<Map<String, Object>>> entryRet = retList.get(i);
                Map mapRet = new HashMap<>();
                mapRet.put("dwmc", entryRet.getKey());
                mapRet.put("ztList", entryRet.getValue());
                retFList.add(mapRet);
            }
        }

        return retFList;
    }
    @Override
    public Map<String, Object> getDzAndZxrRole(String bgId,String jdId) {
        LoginUser user = AuthUtil.getUser();
        Map<String, Object> map = baseMapper.getDzAndZxrRole(user.getUserId(),bgId,jdId);
        return map;
    }

    @Override
    public int getRoleFlag(String roleIds, String bgid, String jdid, String bgmkid) {
        LoginUser user = AuthUtil.getUser();
        // 判断是否为 0：管理员 1：专家 2：点长 3：撰写人

        if (StringUtil.isNotEmpty(bgid)) {

            if(StringUtil.isNotEmpty(jdid) && !StringUtil.isNotEmpty(bgmkid)){

                // 根据报告id 查询是否是点长
                // 首页 导出按钮用
                int count = baseMapper.getZygzdRole(Long.parseLong(bgid), Long.parseLong(jdid), user.getUserId());
                if (count > 0) {
                    return 2;
                }else{
                    return 3;
                }
            }

            if(StringUtil.isNotEmpty(bgmkid)){
                Long bgmkidParam = null;
                bgmkidParam = Long.parseLong(bgmkid);

                // 根据报告id、模块id 查询是否是点长
                // 给其他页面留用
                int count = baseMapper.getZygzdRoleByBgmkId(Long.parseLong(bgid), Long.parseLong(jdid), bgmkidParam, user.getUserId());
                if (count > 0) {
                    return 2;
                }else{
                    return 3;
                }
            }

        }

        if(!com.xpaas.core.tool.utils.StringUtil.isEmpty(roleIds)) {
            String[] strArr = roleIds.split(",");
            if(strArr.length > 0) {

                for (String roleId : strArr) {
                    if(getRoleGly(roleId)) { // # 自评报告撰写平台-教评中心管理员-角色ID
                        return 0;
                    }
                }

                for (String roleId : strArr) {
                    if(getRoleZj(roleId)) { // # 自评报告撰写平台-专家-角色ID
                        return 1;
                    }
                }

            }

            return 3;
        }else{
            return 3;
        }

    }

    @Override
    public int getRoleFlagZj(String roleIds) {
        // 判断是否为 1：专家 3：撰写人

        if(!com.xpaas.core.tool.utils.StringUtil.isEmpty(roleIds)) {
            String[] strArr = roleIds.split(",");
            if(strArr.length > 0) {

                for (String roleId : strArr) {
                    if(getRoleZj(roleId)) { // # 自评报告撰写平台-专家-角色ID
                        return 1;
                    }
                }

            }

            return 3;
        }else{
            return 3;
        }

    }

    // 根据角色id 返回当前登录用户是否为管理员
    private boolean getRoleGly(String roleId) {
        return Arrays.stream(zpbgzxptGlyRoleId).anyMatch(s -> s.equals(roleId));
    }

    /**
     * 判断当前用户角色是否包含传入角色id
     */
    private boolean existRoleId(String[] matchRoleIds, String userRoleIds) {
        return Arrays.stream(matchRoleIds).anyMatch(s -> userRoleIds.contains(s));
    }

    // 根据角色id 返回当前登录用户是否为专家
    private boolean getRoleZj(String roleId) {
        return Arrays.stream(zpbgzxptZjRoleId).anyMatch(s -> s.equals(roleId));
    }

    @Override
    public Map<String, Object> getJdInfo(HomeVO home) throws ParseException {

        Jdgl jdgl = jdglService.getById(home.getJdId());

        if(jdgl == null){
            return new HashMap<>();
        }

        SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String kssjStr = sdf.format(jdgl.getKssj()) + " 00:00:00";
        String zxjssjStr = sdf.format(jdgl.getZxjssj()) + " 23:59:59";
        String syjssjStr = sdf.format(jdgl.getSyjssj()) + " 23:59:59";

        Date kssj = sdfTime.parse(kssjStr);
        Date zxjssj = sdfTime.parse(zxjssjStr);
        Date syjssj = sdfTime.parse(syjssjStr);

        Date now = new Date();
        String startTime = sdfTime.format(now);

        String format = "yyyy-MM-dd";
        SimpleDateFormat sd = new SimpleDateFormat(format);
        String endTime = "";

        String strType = "";

        if(now.getTime() >= kssj.getTime() && now.getTime() <= zxjssj.getTime()){
            strType = "撰写";
            endTime = zxjssjStr;
        }else if(now.getTime() > zxjssj.getTime() && now.getTime() <= syjssj.getTime()){
            strType = "审阅";
            endTime = syjssjStr;
        }else{

            Map<String, Object> map = new HashMap<>();
            map.put("jdztStr", "已结束");
            map.put("jdmc", jdgl.getJdmc());
            return map;
        }

        return dateDiff(startTime, endTime, "yyyy-MM-dd HH:mm:ss", jdgl.getJdmc(), strType);

    }

    private Map<String, Object> dateDiff(String startTime, String endTime, String format, String jdmc, String strType) {
        Map<String, Object> map = new HashMap<>();
        // 按照传入的格式生成一个simpledateformate对象
        SimpleDateFormat sd = new SimpleDateFormat(format);
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        // 获得两个时间的毫秒时间差异
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();

            if(diff > 0 || !StringUtil.isEmpty(strType)){
                day = diff / nd;// 计算差多少天
                hour = diff % nd / nh + day * 24;// 计算差多少小时
                min = diff % nd % nh / nm + day * 24 * 60;// 计算差多少分钟
                sec = diff % nd % nh % nm / ns;// 计算差多少秒
                // 输出结果
//                System.out.println("距离" + strType + "结束时间还剩：" + day + "天" + (hour - day * 24) + "小时"
//                        + (min - day * 24 * 60) + "分钟" + sec + "秒。");
//                System.out.println("hour=" + hour + ",min=" + min);

                String ret =  + day + "天" + (hour - day * 24) + "小时"
                        + (min - day * 24 * 60) + "分";

                map.put("jdztStr", "进行中");
                map.put("jlTitle", "距离" + strType + "结束时间还剩：");
                map.put("jlText", ret);

            }else{
                map.put("jdztStr", "已结束");
            }

            map.put("jdmc", jdmc);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 工作台报告数量
     */
    @Override
    public Map<String, Object> gztbgsl(HomeVO homeVO){
        if(!existRoleId(pjgzYszRoleId,AuthUtil.getUser().getRoleId())&&!existRoleId(zpbgzxptGlyRoleId,AuthUtil.getUser().getRoleId())){
            homeVO.setUserId(AuthUtil.getUserId().toString());
        }
        return baseMapper.gztbgsl(homeVO);
    }

}
