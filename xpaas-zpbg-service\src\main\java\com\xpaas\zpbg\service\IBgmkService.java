package com.xpaas.zpbg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zpbg.entity.Bgmk;
import com.xpaas.zpbg.vo.BgmkVO;

/**
 * 教学评价-自评报告-报告模块 服务类
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface IBgmkService extends BaseService<Bgmk> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param bgmk
	 * @return
	 */
	IPage<BgmkVO> selectBgmkPage(IPage<BgmkVO> page, BgmkVO bgmk);

}
